# PowerShell script to fix npm dependencies automatically

Write-Host "🔧 Fixing npm dependencies automatically..." -ForegroundColor Blue

function Write-Status {
    param($Message)
    Write-Host "[INFO] $Message" -ForegroundColor Cyan
}

function Write-Success {
    param($Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param($Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param($Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

try {
    # Step 1: Clear npm cache
    Write-Status "Clearing npm cache..."
    npm cache clean --force
    Write-Success "npm cache cleared"

    # Step 2: Remove node_modules and package-lock.json
    Write-Status "Removing node_modules and package-lock.json..."
    if (Test-Path "node_modules") {
        Remove-Item -Recurse -Force "node_modules"
    }
    if (Test-Path "package-lock.json") {
        Remove-Item -Force "package-lock.json"
    }
    Write-Success "Cleaned up old dependencies"

    # Step 3: Update npm to latest version
    Write-Status "Updating npm to latest version..."
    npm install -g npm@latest
    Write-Success "npm updated"

    # Step 4: Install dependencies
    Write-Status "Installing dependencies..."
    $installResult = npm install
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Dependencies installed successfully!"
    } else {
        Write-Error "Failed to install dependencies"
        
        # Try alternative solutions
        Write-Status "Trying alternative solutions..."
        
        # Try with --legacy-peer-deps
        Write-Status "Trying with --legacy-peer-deps..."
        npm install --legacy-peer-deps
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Dependencies installed with --legacy-peer-deps!"
        } else {
            # Try with --force
            Write-Status "Trying with --force..."
            npm install --force
            
            if ($LASTEXITCODE -eq 0) {
                Write-Success "Dependencies installed with --force!"
            } else {
                Write-Error "All installation methods failed. Please check manually."
                exit 1
            }
        }
    }

    # Step 5: Verify installation
    Write-Status "Verifying installation..."
    if ((Test-Path "node_modules") -and (Test-Path "package-lock.json")) {
        Write-Success "Installation verified successfully!"
        
        # Check if we can build the project
        Write-Status "Testing build..."
        npm run build
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Build test passed!"
            Write-Host ""
            Write-Host "🎉 All fixed! You can now run:" -ForegroundColor Green
            Write-Host "   npm run start:dev" -ForegroundColor White
        } else {
            Write-Warning "Build test failed, but dependencies are installed"
        }
    } else {
        Write-Error "Installation verification failed"
        exit 1
    }

    Write-Host ""
    Write-Success "✅ Dependency fix completed!"

} catch {
    Write-Error "An error occurred: $_"
    exit 1
}
