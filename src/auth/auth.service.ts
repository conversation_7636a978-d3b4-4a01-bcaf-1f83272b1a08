import { Injectable, UnauthorizedException, BadRequestException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { Response } from 'express';
import * as bcrypt from 'bcryptjs';
import ms from 'ms';

import { UsersService } from '../users/users.service';
import { RegisterDto } from './dto/register.dto';
import { IUser } from './interfaces/user.interface';

@Injectable()
export class AuthService {
  constructor(
    private usersService: UsersService,
    private jwtService: JwtService,
    private configService: ConfigService,
  ) {}

  async validateUser(username: string, password: string): Promise<any> {
    const user = await this.usersService.findByUsername(username);
    if (user && (await bcrypt.compare(password, user.password))) {
      const { password, refresh_token, ...result } = user;
      return result;
    }
    return null;
  }

  private createRefreshToken(payload: any) {
    return this.jwtService.sign(payload, {
      secret: this.configService.get<string>('JWT_REFRESH_KEY'),
      expiresIn: this.configService.get<string>('JWT_REFRESH_EXPIRE'),
    });
  }

  async login(user: IUser, response: Response) {
    const { id, user_id, username, email, full_name, role } = user;
    const payload = {
      sub: 'Token login',
      iss: 'From server',
      id,
      user_id,
      username,
      email,
      full_name,
      role,
    };

    const refresh_token = this.createRefreshToken(payload);

    await this.usersService.updateRefreshToken(refresh_token, id);
    response.cookie('refresh_token', refresh_token, {
      httpOnly: true,
      maxAge: ms(this.configService.get<string>('JWT_REFRESH_EXPIRE')),
    });

    return {
      access_token: this.jwtService.sign(payload),
      user: {
        id,
        user_id,
        username,
        email,
        full_name,
        role,
      },
    };
  }

  async register(registerDto: RegisterDto) {
    const result = await this.usersService.create(registerDto);
    const { id, created_at } = result;
    return {
      id,
      created_at,
    };
  }

  async refreshToken(refreshToken: string, response: Response) {
    try {
      const payload = this.jwtService.verify(refreshToken, {
        secret: this.configService.get<string>('JWT_REFRESH_KEY'),
      });

      const user = await this.usersService.findByRefreshToken(refreshToken);
      if (!user) {
        throw new UnauthorizedException('Invalid refresh token');
      }

      const newPayload = {
        sub: 'Token refresh',
        iss: 'From server',
        id: user.id,
        user_id: user.user_id,
        username: user.username,
        email: user.email,
        full_name: user.full_name,
        role: user.role,
      };

      const newRefreshToken = this.createRefreshToken(newPayload);
      await this.usersService.updateRefreshToken(newRefreshToken, user.id);

      response.cookie('refresh_token', newRefreshToken, {
        httpOnly: true,
        maxAge: ms(this.configService.get<string>('JWT_REFRESH_EXPIRE')),
      });

      return {
        access_token: this.jwtService.sign(newPayload),
        user: {
          id: user.id,
          user_id: user.user_id,
          username: user.username,
          email: user.email,
          full_name: user.full_name,
          role: user.role,
        },
      };
    } catch (error) {
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  async logout(user: IUser, response: Response) {
    await this.usersService.updateRefreshToken(null, user.id);
    response.clearCookie('refresh_token');
    return { message: 'Logged out successfully' };
  }
}
