"use strict";
var __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
    function accept(f) { if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected"); return f; }
    var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
    var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
    var _, done = false;
    for (var i = decorators.length - 1; i >= 0; i--) {
        var context = {};
        for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
        for (var p in contextIn.access) context.access[p] = contextIn.access[p];
        context.addInitializer = function (f) { if (done) throw new TypeError("Cannot add initializers after decoration has completed"); extraInitializers.push(accept(f || null)); };
        var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
        if (kind === "accessor") {
            if (result === void 0) continue;
            if (result === null || typeof result !== "object") throw new TypeError("Object expected");
            if (_ = accept(result.get)) descriptor.get = _;
            if (_ = accept(result.set)) descriptor.set = _;
            if (_ = accept(result.init)) initializers.unshift(_);
        }
        else if (_ = accept(result)) {
            if (kind === "field") initializers.unshift(_);
            else descriptor[key] = _;
        }
    }
    if (target) Object.defineProperty(target, contextIn.name, descriptor);
    done = true;
};
var __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {
    var useValue = arguments.length > 2;
    for (var i = 0; i < initializers.length; i++) {
        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
    }
    return useValue ? value : void 0;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __setFunctionName = (this && this.__setFunctionName) || function (f, name, prefix) {
    if (typeof name === "symbol") name = name.description ? "[".concat(name.description, "]") : "";
    return Object.defineProperty(f, "name", { configurable: true, value: prefix ? "".concat(prefix, " ", name) : name });
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
var common_1 = require("@nestjs/common");
var bcrypt = require("bcryptjs");
var ms_1 = require("ms");
var AuthService = function () {
    var _classDecorators = [(0, common_1.Injectable)()];
    var _classDescriptor;
    var _classExtraInitializers = [];
    var _classThis;
    var AuthService = _classThis = /** @class */ (function () {
        function AuthService_1(usersService, jwtService, configService) {
            this.usersService = usersService;
            this.jwtService = jwtService;
            this.configService = configService;
        }
        AuthService_1.prototype.validateUser = function (username, password) {
            return __awaiter(this, void 0, void 0, function () {
                var user, _a, password_1, refresh_token, result;
                return __generator(this, function (_b) {
                    switch (_b.label) {
                        case 0: return [4 /*yield*/, this.usersService.findByUsername(username)];
                        case 1:
                            user = _b.sent();
                            _a = user;
                            if (!_a) return [3 /*break*/, 3];
                            return [4 /*yield*/, bcrypt.compare(password, user.password)];
                        case 2:
                            _a = (_b.sent());
                            _b.label = 3;
                        case 3:
                            if (_a) {
                                password_1 = user.password, refresh_token = user.refresh_token, result = __rest(user, ["password", "refresh_token"]);
                                return [2 /*return*/, result];
                            }
                            return [2 /*return*/, null];
                    }
                });
            });
        };
        AuthService_1.prototype.createRefreshToken = function (payload) {
            return this.jwtService.sign(payload, {
                secret: this.configService.get('JWT_REFRESH_KEY'),
                expiresIn: this.configService.get('JWT_REFRESH_EXPIRE'),
            });
        };
        AuthService_1.prototype.login = function (user, response) {
            return __awaiter(this, void 0, void 0, function () {
                var id, user_id, username, email, full_name, role, payload, refresh_token;
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0:
                            id = user.id, user_id = user.user_id, username = user.username, email = user.email, full_name = user.full_name, role = user.role;
                            payload = {
                                sub: 'Token login',
                                iss: 'From server',
                                id: id,
                                user_id: user_id,
                                username: username,
                                email: email,
                                full_name: full_name,
                                role: role,
                            };
                            refresh_token = this.createRefreshToken(payload);
                            return [4 /*yield*/, this.usersService.updateRefreshToken(refresh_token, id)];
                        case 1:
                            _a.sent();
                            response.cookie('refresh_token', refresh_token, {
                                httpOnly: true,
                                maxAge: (0, ms_1.default)(this.configService.get('JWT_REFRESH_EXPIRE')),
                            });
                            return [2 /*return*/, {
                                    access_token: this.jwtService.sign(payload),
                                    user: {
                                        id: id,
                                        user_id: user_id,
                                        username: username,
                                        email: email,
                                        full_name: full_name,
                                        role: role,
                                    },
                                }];
                    }
                });
            });
        };
        AuthService_1.prototype.register = function (registerDto) {
            return __awaiter(this, void 0, void 0, function () {
                var result, id, created_at;
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0: return [4 /*yield*/, this.usersService.create(registerDto)];
                        case 1:
                            result = _a.sent();
                            id = result.id, created_at = result.created_at;
                            return [2 /*return*/, {
                                    id: id,
                                    created_at: created_at,
                                }];
                    }
                });
            });
        };
        AuthService_1.prototype.refreshToken = function (refreshToken, response) {
            return __awaiter(this, void 0, void 0, function () {
                var payload, user, newPayload, newRefreshToken, error_1;
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0:
                            _a.trys.push([0, 3, , 4]);
                            payload = this.jwtService.verify(refreshToken, {
                                secret: this.configService.get('JWT_REFRESH_KEY'),
                            });
                            return [4 /*yield*/, this.usersService.findByRefreshToken(refreshToken)];
                        case 1:
                            user = _a.sent();
                            if (!user) {
                                throw new common_1.UnauthorizedException('Invalid refresh token');
                            }
                            newPayload = {
                                sub: 'Token refresh',
                                iss: 'From server',
                                id: user.id,
                                user_id: user.user_id,
                                username: user.username,
                                email: user.email,
                                full_name: user.full_name,
                                role: user.role,
                            };
                            newRefreshToken = this.createRefreshToken(newPayload);
                            return [4 /*yield*/, this.usersService.updateRefreshToken(newRefreshToken, user.id)];
                        case 2:
                            _a.sent();
                            response.cookie('refresh_token', newRefreshToken, {
                                httpOnly: true,
                                maxAge: (0, ms_1.default)(this.configService.get('JWT_REFRESH_EXPIRE')),
                            });
                            return [2 /*return*/, {
                                    access_token: this.jwtService.sign(newPayload),
                                    user: {
                                        id: user.id,
                                        user_id: user.user_id,
                                        username: user.username,
                                        email: user.email,
                                        full_name: user.full_name,
                                        role: user.role,
                                    },
                                }];
                        case 3:
                            error_1 = _a.sent();
                            throw new common_1.UnauthorizedException('Invalid refresh token');
                        case 4: return [2 /*return*/];
                    }
                });
            });
        };
        AuthService_1.prototype.logout = function (user, response) {
            return __awaiter(this, void 0, void 0, function () {
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0: return [4 /*yield*/, this.usersService.updateRefreshToken(null, user.id)];
                        case 1:
                            _a.sent();
                            response.clearCookie('refresh_token');
                            return [2 /*return*/, { message: 'Logged out successfully' }];
                    }
                });
            });
        };
        return AuthService_1;
    }());
    __setFunctionName(_classThis, "AuthService");
    (function () {
        var _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(null) : void 0;
        __esDecorate(null, _classDescriptor = { value: _classThis }, _classDecorators, { kind: "class", name: _classThis.name, metadata: _metadata }, null, _classExtraInitializers);
        AuthService = _classThis = _classDescriptor.value;
        if (_metadata) Object.defineProperty(_classThis, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
        __runInitializers(_classThis, _classExtraInitializers);
    })();
    return AuthService = _classThis;
}();
exports.AuthService = AuthService;
