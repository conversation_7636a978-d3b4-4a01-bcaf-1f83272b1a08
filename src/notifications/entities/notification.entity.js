"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
    function accept(f) { if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected"); return f; }
    var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
    var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
    var _, done = false;
    for (var i = decorators.length - 1; i >= 0; i--) {
        var context = {};
        for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
        for (var p in contextIn.access) context.access[p] = contextIn.access[p];
        context.addInitializer = function (f) { if (done) throw new TypeError("Cannot add initializers after decoration has completed"); extraInitializers.push(accept(f || null)); };
        var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
        if (kind === "accessor") {
            if (result === void 0) continue;
            if (result === null || typeof result !== "object") throw new TypeError("Object expected");
            if (_ = accept(result.get)) descriptor.get = _;
            if (_ = accept(result.set)) descriptor.set = _;
            if (_ = accept(result.init)) initializers.unshift(_);
        }
        else if (_ = accept(result)) {
            if (kind === "field") initializers.unshift(_);
            else descriptor[key] = _;
        }
    }
    if (target) Object.defineProperty(target, contextIn.name, descriptor);
    done = true;
};
var __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {
    var useValue = arguments.length > 2;
    for (var i = 0; i < initializers.length; i++) {
        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
    }
    return useValue ? value : void 0;
};
var __setFunctionName = (this && this.__setFunctionName) || function (f, name, prefix) {
    if (typeof name === "symbol") name = name.description ? "[".concat(name.description, "]") : "";
    return Object.defineProperty(f, "name", { configurable: true, value: prefix ? "".concat(prefix, " ", name) : name });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Notification = void 0;
var openapi = require("@nestjs/swagger");
var typeorm_1 = require("typeorm");
var base_entity_1 = require("../../common/entities/base.entity");
var user_entity_1 = require("../../users/entities/user.entity");
var Notification = function () {
    var _classDecorators = [(0, typeorm_1.Entity)('notifications')];
    var _classDescriptor;
    var _classExtraInitializers = [];
    var _classThis;
    var _classSuper = base_entity_1.BaseEntity;
    var _notification_id_decorators;
    var _notification_id_initializers = [];
    var _notification_id_extraInitializers = [];
    var _recipient_id_decorators;
    var _recipient_id_initializers = [];
    var _recipient_id_extraInitializers = [];
    var _content_decorators;
    var _content_initializers = [];
    var _content_extraInitializers = [];
    var _is_read_decorators;
    var _is_read_initializers = [];
    var _is_read_extraInitializers = [];
    var _recipient_decorators;
    var _recipient_initializers = [];
    var _recipient_extraInitializers = [];
    var Notification = _classThis = /** @class */ (function (_super) {
        __extends(Notification_1, _super);
        function Notification_1() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.notification_id = __runInitializers(_this, _notification_id_initializers, void 0);
            _this.recipient_id = (__runInitializers(_this, _notification_id_extraInitializers), __runInitializers(_this, _recipient_id_initializers, void 0));
            _this.content = (__runInitializers(_this, _recipient_id_extraInitializers), __runInitializers(_this, _content_initializers, void 0));
            _this.is_read = (__runInitializers(_this, _content_extraInitializers), __runInitializers(_this, _is_read_initializers, void 0));
            // Relations
            _this.recipient = (__runInitializers(_this, _is_read_extraInitializers), __runInitializers(_this, _recipient_initializers, void 0));
            __runInitializers(_this, _recipient_extraInitializers);
            return _this;
        }
        Notification_1._OPENAPI_METADATA_FACTORY = function () {
            return { notification_id: { required: true, type: function () { return Number; } }, recipient_id: { required: true, type: function () { return Number; } }, content: { required: true, type: function () { return String; } }, is_read: { required: true, type: function () { return Boolean; } }, recipient: { required: true, type: function () { return require("../../users/entities/user.entity").User; } } };
        };
        return Notification_1;
    }(_classSuper));
    __setFunctionName(_classThis, "Notification");
    (function () {
        var _a;
        var _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create((_a = _classSuper[Symbol.metadata]) !== null && _a !== void 0 ? _a : null) : void 0;
        _notification_id_decorators = [(0, typeorm_1.Column)({ name: 'notification_id' })];
        _recipient_id_decorators = [(0, typeorm_1.Column)({ name: 'recipient_id' })];
        _content_decorators = [(0, typeorm_1.Column)({ name: 'content', type: 'text' })];
        _is_read_decorators = [(0, typeorm_1.Column)({ name: 'is_read', default: false })];
        _recipient_decorators = [(0, typeorm_1.ManyToOne)(function () { return user_entity_1.User; }, function (user) { return user.notifications; }, { onDelete: 'CASCADE' }), (0, typeorm_1.JoinColumn)({ name: 'recipient_id' })];
        __esDecorate(null, null, _notification_id_decorators, { kind: "field", name: "notification_id", static: false, private: false, access: { has: function (obj) { return "notification_id" in obj; }, get: function (obj) { return obj.notification_id; }, set: function (obj, value) { obj.notification_id = value; } }, metadata: _metadata }, _notification_id_initializers, _notification_id_extraInitializers);
        __esDecorate(null, null, _recipient_id_decorators, { kind: "field", name: "recipient_id", static: false, private: false, access: { has: function (obj) { return "recipient_id" in obj; }, get: function (obj) { return obj.recipient_id; }, set: function (obj, value) { obj.recipient_id = value; } }, metadata: _metadata }, _recipient_id_initializers, _recipient_id_extraInitializers);
        __esDecorate(null, null, _content_decorators, { kind: "field", name: "content", static: false, private: false, access: { has: function (obj) { return "content" in obj; }, get: function (obj) { return obj.content; }, set: function (obj, value) { obj.content = value; } }, metadata: _metadata }, _content_initializers, _content_extraInitializers);
        __esDecorate(null, null, _is_read_decorators, { kind: "field", name: "is_read", static: false, private: false, access: { has: function (obj) { return "is_read" in obj; }, get: function (obj) { return obj.is_read; }, set: function (obj, value) { obj.is_read = value; } }, metadata: _metadata }, _is_read_initializers, _is_read_extraInitializers);
        __esDecorate(null, null, _recipient_decorators, { kind: "field", name: "recipient", static: false, private: false, access: { has: function (obj) { return "recipient" in obj; }, get: function (obj) { return obj.recipient; }, set: function (obj, value) { obj.recipient = value; } }, metadata: _metadata }, _recipient_initializers, _recipient_extraInitializers);
        __esDecorate(null, _classDescriptor = { value: _classThis }, _classDecorators, { kind: "class", name: _classThis.name, metadata: _metadata }, null, _classExtraInitializers);
        Notification = _classThis = _classDescriptor.value;
        if (_metadata) Object.defineProperty(_classThis, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
        __runInitializers(_classThis, _classExtraInitializers);
    })();
    return Notification = _classThis;
}();
exports.Notification = Notification;
