import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Notification } from './entities/notification.entity';

export enum NotificationType {
  APPLICATION_SUBMITTED = 'application_submitted',
  APPLICATION_ACCEPTED = 'application_accepted',
  APPLICATION_REJECTED = 'application_rejected',
  INTERVIEW_SCHEDULED = 'interview_scheduled',
  INTERVIEW_REMINDER = 'interview_reminder',
  JOB_POSTED = 'job_posted',
  COMPANY_FOLLOWED = 'company_followed',
}

@Injectable()
export class NotificationCreatorService {
  constructor(
    @InjectRepository(Notification)
    private notificationRepository: Repository<Notification>,
  ) {}

  /**
   * Create notification when user applies for a job
   */
  async createApplicationNotification(userId: number, jobTitle: string, companyName: string) {
    return this.createNotification({
      recipient_id: userId,
      type: NotificationType.APPLICATION_SUBMITTED,
      title: 'Application Submitted',
      message: `Your application for ${jobTitle} at ${companyName} has been submitted successfully.`,
    });
  }

  /**
   * Create notification when application is accepted
   */
  async createApplicationAcceptedNotification(userId: number, jobTitle: string, companyName: string) {
    return this.createNotification({
      recipient_id: userId,
      type: NotificationType.APPLICATION_ACCEPTED,
      title: 'Application Accepted',
      message: `Congratulations! Your application for ${jobTitle} at ${companyName} has been accepted.`,
    });
  }

  /**
   * Create notification when application is rejected
   */
  async createApplicationRejectedNotification(userId: number, jobTitle: string, companyName: string) {
    return this.createNotification({
      recipient_id: userId,
      type: NotificationType.APPLICATION_REJECTED,
      title: 'Application Update',
      message: `Thank you for your interest in ${jobTitle} at ${companyName}. We have decided to move forward with other candidates.`,
    });
  }

  /**
   * Create notification when interview is scheduled
   */
  async createInterviewScheduledNotification(userId: number, jobTitle: string, interviewDate: Date) {
    return this.createNotification({
      recipient_id: userId,
      type: NotificationType.INTERVIEW_SCHEDULED,
      title: 'Interview Scheduled',
      message: `Your interview for ${jobTitle} has been scheduled for ${interviewDate.toLocaleDateString()}.`,
    });
  }

  /**
   * Create notification when new job is posted by followed company
   */
  async createJobPostedNotification(userId: number, jobTitle: string, companyName: string) {
    return this.createNotification({
      recipient_id: userId,
      type: NotificationType.JOB_POSTED,
      title: 'New Job Posted',
      message: `${companyName} has posted a new job: ${jobTitle}`,
    });
  }

  /**
   * Generic method to create notifications
   */
  private async createNotification(data: {
    recipient_id: number;
    type: NotificationType;
    title: string;
    message: string;
  }) {
    const notification = this.notificationRepository.create({
      ...data,
      notification_id: Math.floor(Math.random() * 1000000), // Temporary ID generation
    });

    return this.notificationRepository.save(notification);
  }
}
