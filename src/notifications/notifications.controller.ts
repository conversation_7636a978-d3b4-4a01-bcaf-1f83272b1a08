import {
  <PERSON>,
  <PERSON>,
  <PERSON>,
  Param,
  ParseIntPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';

import { NotificationsService } from './notifications.service';
import { ResponseMessage } from '../common/decorators/response-message.decorator';
import { User } from '../common/decorators/user.decorator';
import { IUser } from '../auth/interfaces/user.interface';
import { Roles } from '../common/decorators/roles.decorator';
import { UserRole } from '../common/enums/user-role.enum';

@ApiTags('notifications')
@ApiBearerAuth('access-token')
@Controller('notifications')
export class NotificationsController {
  constructor(private readonly notificationsService: NotificationsService) {}

  @Get()
  @ApiOperation({ summary: 'Get my notifications' })
  @ApiResponse({ status: 200, description: 'Notifications retrieved successfully' })
  @ResponseMessage('Notifications retrieved successfully')
  getMyNotifications(@User() user: IUser) {
    return this.notificationsService.findByUser(user.id);
  }

  @Get('unread')
  @ApiOperation({ summary: 'Get my unread notifications' })
  @ApiResponse({ status: 200, description: 'Unread notifications retrieved successfully' })
  @ResponseMessage('Unread notifications retrieved successfully')
  getMyUnreadNotifications(@User() user: IUser) {
    return this.notificationsService.findUnreadByUser(user.id);
  }

  @Get('stats')
  @ApiOperation({ summary: 'Get my notification statistics' })
  @ApiResponse({ status: 200, description: 'Notification statistics retrieved successfully' })
  @ResponseMessage('Notification statistics retrieved successfully')
  getMyNotificationStats(@User() user: IUser) {
    return this.notificationsService.getNotificationStats(user.id);
  }

  // Admin only - view all notifications
  @Roles(UserRole.ADMIN)
  @Get('all')
  @ApiOperation({ summary: 'Get all notifications (Admin only)' })
  @ApiResponse({ status: 200, description: 'All notifications retrieved successfully' })
  @ResponseMessage('All notifications retrieved successfully')
  getAllNotifications() {
    return this.notificationsService.findAll();
  }

  @Patch(':id/read')
  @ApiOperation({ summary: 'Mark notification as read' })
  @ApiResponse({ status: 200, description: 'Notification marked as read' })
  @ResponseMessage('Notification marked as read')
  markAsRead(@User() user: IUser, @Param('id', ParseIntPipe) id: number) {
    return this.notificationsService.markAsRead(id, user.id);
  }

  @Patch('mark-all-read')
  @ApiOperation({ summary: 'Mark all my notifications as read' })
  @ApiResponse({ status: 200, description: 'All notifications marked as read' })
  @ResponseMessage('All notifications marked as read')
  markAllAsRead(@User() user: IUser) {
    return this.notificationsService.markAllAsReadForUser(user.id);
  }
}
}
