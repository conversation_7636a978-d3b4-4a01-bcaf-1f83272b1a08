import {
  <PERSON>,
  Get,
  <PERSON>,
  Param,
  ParseIntPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';

import { NotificationsService } from './notifications.service';
import { ResponseMessage } from '../common/decorators/response-message.decorator';
import { User } from '../common/decorators/user.decorator';
import { IUser } from '../auth/interfaces/user.interface';
import { Roles } from '../common/decorators/roles.decorator';
import { UserRole } from '../common/enums/user-role.enum';

@ApiTags('notifications')
@ApiBearerAuth('access-token')
@Controller('notifications')
export class NotificationsController {
  constructor(private readonly notificationsService: NotificationsService) {}

  @Get()
  @ApiOperation({ summary: 'Get notifications' })
  @ApiResponse({ status: 200, description: 'Notifications retrieved successfully' })
  @ResponseMessage('Notifications retrieved successfully')
  getNotifications(@User() user: IUser, @Query('unread') unread?: string) {
    if (unread === 'true') {
      return this.notificationsService.findUnreadByUser(user.id);
    }
    return this.notificationsService.findByUser(user.id);
  }

  @Get('count')
  @ApiOperation({ summary: 'Get notification count' })
  @ApiResponse({ status: 200, description: 'Notification count retrieved successfully' })
  @ResponseMessage('Notification count retrieved successfully')
  getNotificationCount(@User() user: IUser) {
    return this.notificationsService.getNotificationStats(user.id);
  }

  @Patch(':id/read')
  @ApiOperation({ summary: 'Mark notification as read' })
  @ApiResponse({ status: 200, description: 'Notification marked as read' })
  @ResponseMessage('Notification marked as read')
  markAsRead(@User() user: IUser, @Param('id', ParseIntPipe) id: number) {
    return this.notificationsService.markAsRead(id, user.id);
  }

  @Patch('read-all')
  @ApiOperation({ summary: 'Mark all notifications as read' })
  @ApiResponse({ status: 200, description: 'All notifications marked as read' })
  @ResponseMessage('All notifications marked as read')
  markAllAsRead(@User() user: IUser) {
    return this.notificationsService.markAllAsReadForUser(user.id);
  }
}
}
