import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ParseIntPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';

import { NotificationsService } from './notifications.service';
import { CreateNotificationDto } from './dto/create-notification.dto';
import { UpdateNotificationDto } from './dto/update-notification.dto';
import { ResponseMessage } from '../common/decorators/response-message.decorator';
import { User } from '../common/decorators/user.decorator';
import { IUser } from '../auth/interfaces/user.interface';

@ApiTags('notifications')
@ApiBearerAuth('access-token')
@Controller('notifications')
export class NotificationsController {
  constructor(private readonly notificationsService: NotificationsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new notification' })
  @ApiResponse({ status: 201, description: 'Notification created successfully' })
  @ResponseMessage('Notification created successfully')
  create(@Body() createNotificationDto: CreateNotificationDto) {
    return this.notificationsService.create(createNotificationDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all notifications' })
  @ApiResponse({ status: 200, description: 'Notifications retrieved successfully' })
  @ResponseMessage('Notifications retrieved successfully')
  findAll() {
    return this.notificationsService.findAll();
  }

  @Get('my-notifications')
  @ApiOperation({ summary: 'Get current user notifications' })
  @ApiResponse({ status: 200, description: 'User notifications retrieved successfully' })
  @ResponseMessage('User notifications retrieved successfully')
  getMyNotifications(@User() user: IUser) {
    return this.notificationsService.findByUser(user.id);
  }

  @Get('my-notifications/unread')
  @ApiOperation({ summary: 'Get current user unread notifications' })
  @ApiResponse({ status: 200, description: 'Unread notifications retrieved successfully' })
  @ResponseMessage('Unread notifications retrieved successfully')
  getMyUnreadNotifications(@User() user: IUser) {
    return this.notificationsService.findUnreadByUser(user.id);
  }

  @Get('my-notifications/stats')
  @ApiOperation({ summary: 'Get current user notification statistics' })
  @ApiResponse({ status: 200, description: 'Notification statistics retrieved successfully' })
  @ResponseMessage('Notification statistics retrieved successfully')
  getMyNotificationStats(@User() user: IUser) {
    return this.notificationsService.getNotificationStats(user.id);
  }

  @Get('user/:userId')
  @ApiOperation({ summary: 'Get notifications by user ID' })
  @ApiResponse({ status: 200, description: 'User notifications retrieved successfully' })
  @ResponseMessage('User notifications retrieved successfully')
  findByUser(@Param('userId', ParseIntPipe) userId: number) {
    return this.notificationsService.findByUser(userId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get notification by ID' })
  @ApiResponse({ status: 200, description: 'Notification retrieved successfully' })
  @ResponseMessage('Notification retrieved successfully')
  findOne(@Param('id', ParseIntPipe) id: number) {
    return this.notificationsService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update notification by ID' })
  @ApiResponse({ status: 200, description: 'Notification updated successfully' })
  @ResponseMessage('Notification updated successfully')
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateNotificationDto: UpdateNotificationDto,
  ) {
    return this.notificationsService.update(id, updateNotificationDto);
  }

  @Patch(':id/read')
  @ApiOperation({ summary: 'Mark notification as read' })
  @ApiResponse({ status: 200, description: 'Notification marked as read' })
  @ResponseMessage('Notification marked as read')
  markAsRead(@Param('id', ParseIntPipe) id: number) {
    return this.notificationsService.markAsRead(id);
  }

  @Patch('mark-all-read')
  @ApiOperation({ summary: 'Mark all notifications as read for current user' })
  @ApiResponse({ status: 200, description: 'All notifications marked as read' })
  @ResponseMessage('All notifications marked as read')
  markAllAsRead(@User() user: IUser) {
    return this.notificationsService.markAllAsReadForUser(user.id);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete notification by ID' })
  @ApiResponse({ status: 200, description: 'Notification deleted successfully' })
  @ResponseMessage('Notification deleted successfully')
  remove(@Param('id', ParseIntPipe) id: number) {
    return this.notificationsService.remove(id);
  }
}
