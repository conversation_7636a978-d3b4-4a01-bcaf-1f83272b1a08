import { Injectable } from '@nestjs/common';
import { NotificationCreatorService } from '../notification-creator.service';

/**
 * Example usage of NotificationCreatorService
 * 
 * This shows how to integrate notifications into your business logic.
 * Call these methods when specific events happen in your application.
 */

@Injectable()
export class ExampleUsageService {
  constructor(
    private readonly notificationCreator: NotificationCreatorService,
  ) {}

  /**
   * Example: When user applies for a job
   */
  async handleJobApplication(userId: number, jobTitle: string, companyName: string) {
    // Your business logic here...
    // Save application to database, etc.

    // Create notification
    await this.notificationCreator.createApplicationNotification(
      userId,
      jobTitle,
      companyName
    );
  }

  /**
   * Example: When HR accepts an application
   */
  async handleApplicationAccepted(userId: number, jobTitle: string, companyName: string) {
    // Your business logic here...
    // Update application status, etc.

    // Create notification
    await this.notificationCreator.createApplicationAcceptedNotification(
      userId,
      jobTitle,
      companyName
    );
  }

  /**
   * Example: When interview is scheduled
   */
  async handleInterviewScheduled(userId: number, jobTitle: string, interviewDate: Date) {
    // Your business logic here...
    // Save interview to database, etc.

    // Create notification
    await this.notificationCreator.createInterviewScheduledNotification(
      userId,
      jobTitle,
      interviewDate
    );
  }

  /**
   * Example: When company posts new job (notify followers)
   */
  async handleNewJobPosted(companyId: number, jobTitle: string, companyName: string) {
    // Get all followers of this company
    const followers = await this.getCompanyFollowers(companyId);

    // Create notification for each follower
    for (const follower of followers) {
      await this.notificationCreator.createJobPostedNotification(
        follower.user_id,
        jobTitle,
        companyName
      );
    }
  }

  private async getCompanyFollowers(companyId: number) {
    // This would be implemented in your companies service
    // Return array of followers
    return [];
  }
}

/**
 * Integration Points:
 * 
 * 1. Applications Service:
 *    - Call createApplicationNotification() when user applies
 *    - Call createApplicationAcceptedNotification() when HR accepts
 *    - Call createApplicationRejectedNotification() when HR rejects
 * 
 * 2. Interviews Service:
 *    - Call createInterviewScheduledNotification() when interview is created
 * 
 * 3. Jobs Service:
 *    - Call createJobPostedNotification() for company followers when job is posted
 * 
 * 4. Companies Service:
 *    - Get followers list for job posting notifications
 */
