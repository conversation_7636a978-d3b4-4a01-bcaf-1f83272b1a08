"use strict";
var __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
    function accept(f) { if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected"); return f; }
    var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
    var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
    var _, done = false;
    for (var i = decorators.length - 1; i >= 0; i--) {
        var context = {};
        for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
        for (var p in contextIn.access) context.access[p] = contextIn.access[p];
        context.addInitializer = function (f) { if (done) throw new TypeError("Cannot add initializers after decoration has completed"); extraInitializers.push(accept(f || null)); };
        var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
        if (kind === "accessor") {
            if (result === void 0) continue;
            if (result === null || typeof result !== "object") throw new TypeError("Object expected");
            if (_ = accept(result.get)) descriptor.get = _;
            if (_ = accept(result.set)) descriptor.set = _;
            if (_ = accept(result.init)) initializers.unshift(_);
        }
        else if (_ = accept(result)) {
            if (kind === "field") initializers.unshift(_);
            else descriptor[key] = _;
        }
    }
    if (target) Object.defineProperty(target, contextIn.name, descriptor);
    done = true;
};
var __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {
    var useValue = arguments.length > 2;
    for (var i = 0; i < initializers.length; i++) {
        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
    }
    return useValue ? value : void 0;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __setFunctionName = (this && this.__setFunctionName) || function (f, name, prefix) {
    if (typeof name === "symbol") name = name.description ? "[".concat(name.description, "]") : "";
    return Object.defineProperty(f, "name", { configurable: true, value: prefix ? "".concat(prefix, " ", name) : name });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExampleUsageService = void 0;
var common_1 = require("@nestjs/common");
/**
 * Example usage of NotificationCreatorService
 *
 * This shows how to integrate notifications into your business logic.
 * Call these methods when specific events happen in your application.
 */
var ExampleUsageService = function () {
    var _classDecorators = [(0, common_1.Injectable)()];
    var _classDescriptor;
    var _classExtraInitializers = [];
    var _classThis;
    var ExampleUsageService = _classThis = /** @class */ (function () {
        function ExampleUsageService_1(notificationCreator) {
            this.notificationCreator = notificationCreator;
        }
        /**
         * Example: When user applies for a job
         */
        ExampleUsageService_1.prototype.handleJobApplication = function (userId, jobTitle, companyName) {
            return __awaiter(this, void 0, void 0, function () {
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0: 
                        // Your business logic here...
                        // Save application to database, etc.
                        // Create notification
                        return [4 /*yield*/, this.notificationCreator.createApplicationNotification(userId, jobTitle, companyName)];
                        case 1:
                            // Your business logic here...
                            // Save application to database, etc.
                            // Create notification
                            _a.sent();
                            return [2 /*return*/];
                    }
                });
            });
        };
        /**
         * Example: When HR accepts an application
         */
        ExampleUsageService_1.prototype.handleApplicationAccepted = function (userId, jobTitle, companyName) {
            return __awaiter(this, void 0, void 0, function () {
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0: 
                        // Your business logic here...
                        // Update application status, etc.
                        // Create notification
                        return [4 /*yield*/, this.notificationCreator.createApplicationAcceptedNotification(userId, jobTitle, companyName)];
                        case 1:
                            // Your business logic here...
                            // Update application status, etc.
                            // Create notification
                            _a.sent();
                            return [2 /*return*/];
                    }
                });
            });
        };
        /**
         * Example: When interview is scheduled
         */
        ExampleUsageService_1.prototype.handleInterviewScheduled = function (userId, jobTitle, interviewDate) {
            return __awaiter(this, void 0, void 0, function () {
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0: 
                        // Your business logic here...
                        // Save interview to database, etc.
                        // Create notification
                        return [4 /*yield*/, this.notificationCreator.createInterviewScheduledNotification(userId, jobTitle, interviewDate)];
                        case 1:
                            // Your business logic here...
                            // Save interview to database, etc.
                            // Create notification
                            _a.sent();
                            return [2 /*return*/];
                    }
                });
            });
        };
        /**
         * Example: When company posts new job (notify followers)
         */
        ExampleUsageService_1.prototype.handleNewJobPosted = function (companyId, jobTitle, companyName) {
            return __awaiter(this, void 0, void 0, function () {
                var followers, _i, followers_1, follower;
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0: return [4 /*yield*/, this.getCompanyFollowers(companyId)];
                        case 1:
                            followers = _a.sent();
                            _i = 0, followers_1 = followers;
                            _a.label = 2;
                        case 2:
                            if (!(_i < followers_1.length)) return [3 /*break*/, 5];
                            follower = followers_1[_i];
                            return [4 /*yield*/, this.notificationCreator.createJobPostedNotification(follower.user_id, jobTitle, companyName)];
                        case 3:
                            _a.sent();
                            _a.label = 4;
                        case 4:
                            _i++;
                            return [3 /*break*/, 2];
                        case 5: return [2 /*return*/];
                    }
                });
            });
        };
        ExampleUsageService_1.prototype.getCompanyFollowers = function (companyId) {
            return __awaiter(this, void 0, void 0, function () {
                return __generator(this, function (_a) {
                    // This would be implemented in your companies service
                    // Return array of followers
                    return [2 /*return*/, []];
                });
            });
        };
        return ExampleUsageService_1;
    }());
    __setFunctionName(_classThis, "ExampleUsageService");
    (function () {
        var _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(null) : void 0;
        __esDecorate(null, _classDescriptor = { value: _classThis }, _classDecorators, { kind: "class", name: _classThis.name, metadata: _metadata }, null, _classExtraInitializers);
        ExampleUsageService = _classThis = _classDescriptor.value;
        if (_metadata) Object.defineProperty(_classThis, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
        __runInitializers(_classThis, _classExtraInitializers);
    })();
    return ExampleUsageService = _classThis;
}();
exports.ExampleUsageService = ExampleUsageService;
/**
 * Integration Points:
 *
 * 1. Applications Service:
 *    - Call createApplicationNotification() when user applies
 *    - Call createApplicationAcceptedNotification() when HR accepts
 *    - Call createApplicationRejectedNotification() when HR rejects
 *
 * 2. Interviews Service:
 *    - Call createInterviewScheduledNotification() when interview is created
 *
 * 3. Jobs Service:
 *    - Call createJobPostedNotification() for company followers when job is posted
 *
 * 4. Companies Service:
 *    - Get followers list for job posting notifications
 */
