import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { Notification } from './entities/notification.entity';
import { CreateNotificationDto } from './dto/create-notification.dto';
import { UpdateNotificationDto } from './dto/update-notification.dto';
import { UsersService } from '../users/users.service';

@Injectable()
export class NotificationsService {
  constructor(
    @InjectRepository(Notification)
    private notificationRepository: Repository<Notification>,
    private usersService: UsersService,
  ) {}

  async create(createNotificationDto: CreateNotificationDto): Promise<Notification> {
    // Verify recipient exists
    await this.usersService.findOne(createNotificationDto.recipient_id);

    const notification = this.notificationRepository.create({
      ...createNotificationDto,
      notification_id: Math.floor(Math.random() * 1000000),
      is_read: createNotificationDto.is_read ?? false,
    });

    return this.notificationRepository.save(notification);
  }

  async findAll(): Promise<Notification[]> {
    return this.notificationRepository.find({
      relations: ['recipient'],
      order: { created_at: 'DESC' },
    });
  }

  async findByUser(userId: number): Promise<Notification[]> {
    return this.notificationRepository.find({
      where: { recipient_id: userId },
      relations: ['recipient'],
      order: { created_at: 'DESC' },
    });
  }

  async findUnreadByUser(userId: number): Promise<Notification[]> {
    return this.notificationRepository.find({
      where: { recipient_id: userId, is_read: false },
      relations: ['recipient'],
      order: { created_at: 'DESC' },
    });
  }

  async findOne(id: number): Promise<Notification> {
    const notification = await this.notificationRepository.findOne({
      where: { id },
      relations: ['recipient'],
    });

    if (!notification) {
      throw new NotFoundException('Notification not found');
    }

    return notification;
  }

  async update(id: number, updateNotificationDto: UpdateNotificationDto): Promise<Notification> {
    const notification = await this.findOne(id);

    if (updateNotificationDto.recipient_id && 
        updateNotificationDto.recipient_id !== notification.recipient_id) {
      await this.usersService.findOne(updateNotificationDto.recipient_id);
    }

    await this.notificationRepository.update(id, updateNotificationDto);
    return this.findOne(id);
  }

  async markAsRead(id: number): Promise<Notification> {
    await this.notificationRepository.update(id, { is_read: true });
    return this.findOne(id);
  }

  async markAllAsReadForUser(userId: number): Promise<void> {
    await this.notificationRepository.update(
      { recipient_id: userId, is_read: false },
      { is_read: true },
    );
  }

  async remove(id: number): Promise<void> {
    const notification = await this.findOne(id);
    await this.notificationRepository.remove(notification);
  }

  async getNotificationStats(userId: number) {
    const total = await this.notificationRepository.count({
      where: { recipient_id: userId },
    });

    const unread = await this.notificationRepository.count({
      where: { recipient_id: userId, is_read: false },
    });

    return {
      total,
      unread,
      read: total - unread,
    };
  }

  // Helper method to create system notifications
  async createSystemNotification(recipientId: number, content: string): Promise<Notification> {
    return this.create({
      recipient_id: recipientId,
      content,
    });
  }
}
