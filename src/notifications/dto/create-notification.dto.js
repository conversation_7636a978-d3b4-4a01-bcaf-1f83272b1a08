"use strict";
var __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
    function accept(f) { if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected"); return f; }
    var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
    var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
    var _, done = false;
    for (var i = decorators.length - 1; i >= 0; i--) {
        var context = {};
        for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
        for (var p in contextIn.access) context.access[p] = contextIn.access[p];
        context.addInitializer = function (f) { if (done) throw new TypeError("Cannot add initializers after decoration has completed"); extraInitializers.push(accept(f || null)); };
        var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
        if (kind === "accessor") {
            if (result === void 0) continue;
            if (result === null || typeof result !== "object") throw new TypeError("Object expected");
            if (_ = accept(result.get)) descriptor.get = _;
            if (_ = accept(result.set)) descriptor.set = _;
            if (_ = accept(result.init)) initializers.unshift(_);
        }
        else if (_ = accept(result)) {
            if (kind === "field") initializers.unshift(_);
            else descriptor[key] = _;
        }
    }
    if (target) Object.defineProperty(target, contextIn.name, descriptor);
    done = true;
};
var __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {
    var useValue = arguments.length > 2;
    for (var i = 0; i < initializers.length; i++) {
        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
    }
    return useValue ? value : void 0;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateNotificationDto = void 0;
var openapi = require("@nestjs/swagger");
var swagger_1 = require("@nestjs/swagger");
var class_validator_1 = require("class-validator");
var CreateNotificationDto = function () {
    var _a;
    var _recipient_id_decorators;
    var _recipient_id_initializers = [];
    var _recipient_id_extraInitializers = [];
    var _content_decorators;
    var _content_initializers = [];
    var _content_extraInitializers = [];
    var _is_read_decorators;
    var _is_read_initializers = [];
    var _is_read_extraInitializers = [];
    return _a = /** @class */ (function () {
            function CreateNotificationDto() {
                this.recipient_id = __runInitializers(this, _recipient_id_initializers, void 0);
                this.content = (__runInitializers(this, _recipient_id_extraInitializers), __runInitializers(this, _content_initializers, void 0));
                this.is_read = (__runInitializers(this, _content_extraInitializers), __runInitializers(this, _is_read_initializers, void 0));
                __runInitializers(this, _is_read_extraInitializers);
            }
            CreateNotificationDto._OPENAPI_METADATA_FACTORY = function () {
                return { recipient_id: { required: true, type: function () { return Number; } }, content: { required: true, type: function () { return String; } }, is_read: { required: false, type: function () { return Boolean; } } };
            };
            return CreateNotificationDto;
        }()),
        (function () {
            var _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(null) : void 0;
            _recipient_id_decorators = [(0, swagger_1.ApiProperty)({ example: 1, description: 'Recipient user ID' }), (0, class_validator_1.IsNotEmpty)(), (0, class_validator_1.IsNumber)()];
            _content_decorators = [(0, swagger_1.ApiProperty)({ example: 'You have a new job application', description: 'Notification content' }), (0, class_validator_1.IsNotEmpty)(), (0, class_validator_1.IsString)()];
            _is_read_decorators = [(0, swagger_1.ApiProperty)({ example: false, description: 'Read status' }), (0, class_validator_1.IsOptional)(), (0, class_validator_1.IsBoolean)()];
            __esDecorate(null, null, _recipient_id_decorators, { kind: "field", name: "recipient_id", static: false, private: false, access: { has: function (obj) { return "recipient_id" in obj; }, get: function (obj) { return obj.recipient_id; }, set: function (obj, value) { obj.recipient_id = value; } }, metadata: _metadata }, _recipient_id_initializers, _recipient_id_extraInitializers);
            __esDecorate(null, null, _content_decorators, { kind: "field", name: "content", static: false, private: false, access: { has: function (obj) { return "content" in obj; }, get: function (obj) { return obj.content; }, set: function (obj, value) { obj.content = value; } }, metadata: _metadata }, _content_initializers, _content_extraInitializers);
            __esDecorate(null, null, _is_read_decorators, { kind: "field", name: "is_read", static: false, private: false, access: { has: function (obj) { return "is_read" in obj; }, get: function (obj) { return obj.is_read; }, set: function (obj, value) { obj.is_read = value; } }, metadata: _metadata }, _is_read_initializers, _is_read_extraInitializers);
            if (_metadata) Object.defineProperty(_a, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
        })(),
        _a;
}();
exports.CreateNotificationDto = CreateNotificationDto;
