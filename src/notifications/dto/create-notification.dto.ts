import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsString, IsOptional, IsBoolean } from 'class-validator';

export class CreateNotificationDto {
  @ApiProperty({ example: 1, description: 'Recipient user ID' })
  @IsNotEmpty()
  @IsNumber()
  recipient_id: number;

  @ApiProperty({ example: 'You have a new job application', description: 'Notification content' })
  @IsNotEmpty()
  @IsString()
  content: string;

  @ApiProperty({ example: false, description: 'Read status' })
  @IsOptional()
  @IsBoolean()
  is_read?: boolean;
}
