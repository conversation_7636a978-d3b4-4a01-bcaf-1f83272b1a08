"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
    function accept(f) { if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected"); return f; }
    var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
    var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
    var _, done = false;
    for (var i = decorators.length - 1; i >= 0; i--) {
        var context = {};
        for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
        for (var p in contextIn.access) context.access[p] = contextIn.access[p];
        context.addInitializer = function (f) { if (done) throw new TypeError("Cannot add initializers after decoration has completed"); extraInitializers.push(accept(f || null)); };
        var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
        if (kind === "accessor") {
            if (result === void 0) continue;
            if (result === null || typeof result !== "object") throw new TypeError("Object expected");
            if (_ = accept(result.get)) descriptor.get = _;
            if (_ = accept(result.set)) descriptor.set = _;
            if (_ = accept(result.init)) initializers.unshift(_);
        }
        else if (_ = accept(result)) {
            if (kind === "field") initializers.unshift(_);
            else descriptor[key] = _;
        }
    }
    if (target) Object.defineProperty(target, contextIn.name, descriptor);
    done = true;
};
var __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {
    var useValue = arguments.length > 2;
    for (var i = 0; i < initializers.length; i++) {
        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
    }
    return useValue ? value : void 0;
};
var __setFunctionName = (this && this.__setFunctionName) || function (f, name, prefix) {
    if (typeof name === "symbol") name = name.description ? "[".concat(name.description, "]") : "";
    return Object.defineProperty(f, "name", { configurable: true, value: prefix ? "".concat(prefix, " ", name) : name });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Permission = void 0;
var openapi = require("@nestjs/swagger");
var typeorm_1 = require("typeorm");
var base_entity_1 = require("../../common/entities/base.entity");
var user_entity_1 = require("../../users/entities/user.entity");
var Permission = function () {
    var _classDecorators = [(0, typeorm_1.Entity)('permissions')];
    var _classDescriptor;
    var _classExtraInitializers = [];
    var _classThis;
    var _classSuper = base_entity_1.BaseEntity;
    var _permission_id_decorators;
    var _permission_id_initializers = [];
    var _permission_id_extraInitializers = [];
    var _permission_name_decorators;
    var _permission_name_initializers = [];
    var _permission_name_extraInitializers = [];
    var _users_decorators;
    var _users_initializers = [];
    var _users_extraInitializers = [];
    var Permission = _classThis = /** @class */ (function (_super) {
        __extends(Permission_1, _super);
        function Permission_1() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.permission_id = __runInitializers(_this, _permission_id_initializers, void 0);
            _this.permission_name = (__runInitializers(_this, _permission_id_extraInitializers), __runInitializers(_this, _permission_name_initializers, void 0));
            _this.users = (__runInitializers(_this, _permission_name_extraInitializers), __runInitializers(_this, _users_initializers, void 0));
            __runInitializers(_this, _users_extraInitializers);
            return _this;
        }
        Permission_1._OPENAPI_METADATA_FACTORY = function () {
            return { permission_id: { required: true, type: function () { return Number; } }, permission_name: { required: true, type: function () { return String; } }, users: { required: true, type: function () { return [require("../../users/entities/user.entity").User]; } } };
        };
        return Permission_1;
    }(_classSuper));
    __setFunctionName(_classThis, "Permission");
    (function () {
        var _a;
        var _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create((_a = _classSuper[Symbol.metadata]) !== null && _a !== void 0 ? _a : null) : void 0;
        _permission_id_decorators = [(0, typeorm_1.Column)({ name: 'permission_id' })];
        _permission_name_decorators = [(0, typeorm_1.Column)({ name: 'permission_name', length: 100, unique: true })];
        _users_decorators = [(0, typeorm_1.OneToMany)(function () { return user_entity_1.User; }, function (user) { return user.permission; })];
        __esDecorate(null, null, _permission_id_decorators, { kind: "field", name: "permission_id", static: false, private: false, access: { has: function (obj) { return "permission_id" in obj; }, get: function (obj) { return obj.permission_id; }, set: function (obj, value) { obj.permission_id = value; } }, metadata: _metadata }, _permission_id_initializers, _permission_id_extraInitializers);
        __esDecorate(null, null, _permission_name_decorators, { kind: "field", name: "permission_name", static: false, private: false, access: { has: function (obj) { return "permission_name" in obj; }, get: function (obj) { return obj.permission_name; }, set: function (obj, value) { obj.permission_name = value; } }, metadata: _metadata }, _permission_name_initializers, _permission_name_extraInitializers);
        __esDecorate(null, null, _users_decorators, { kind: "field", name: "users", static: false, private: false, access: { has: function (obj) { return "users" in obj; }, get: function (obj) { return obj.users; }, set: function (obj, value) { obj.users = value; } }, metadata: _metadata }, _users_initializers, _users_extraInitializers);
        __esDecorate(null, _classDescriptor = { value: _classThis }, _classDecorators, { kind: "class", name: _classThis.name, metadata: _metadata }, null, _classExtraInitializers);
        Permission = _classThis = _classDescriptor.value;
        if (_metadata) Object.defineProperty(_classThis, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
        __runInitializers(_classThis, _classExtraInitializers);
    })();
    return Permission = _classThis;
}();
exports.Permission = Permission;
