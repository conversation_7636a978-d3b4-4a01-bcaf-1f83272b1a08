import { <PERSON><PERSON><PERSON>, Column, <PERSON>ToMany } from 'typeorm';
import { BaseEntity } from '../../common/entities/base.entity';
import { User } from '../../users/entities/user.entity';

@Entity('permissions')
export class Permission extends BaseEntity {
  @Column({ name: 'permission_id' })
  permission_id: number;

  @Column({ name: 'permission_name', length: 100, unique: true })
  permission_name: string;

  @OneToMany(() => User, (user) => user.permission)
  users: User[];
}
