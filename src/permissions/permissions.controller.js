"use strict";
var __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {
    var useValue = arguments.length > 2;
    for (var i = 0; i < initializers.length; i++) {
        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
    }
    return useValue ? value : void 0;
};
var __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
    function accept(f) { if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected"); return f; }
    var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
    var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
    var _, done = false;
    for (var i = decorators.length - 1; i >= 0; i--) {
        var context = {};
        for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
        for (var p in contextIn.access) context.access[p] = contextIn.access[p];
        context.addInitializer = function (f) { if (done) throw new TypeError("Cannot add initializers after decoration has completed"); extraInitializers.push(accept(f || null)); };
        var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
        if (kind === "accessor") {
            if (result === void 0) continue;
            if (result === null || typeof result !== "object") throw new TypeError("Object expected");
            if (_ = accept(result.get)) descriptor.get = _;
            if (_ = accept(result.set)) descriptor.set = _;
            if (_ = accept(result.init)) initializers.unshift(_);
        }
        else if (_ = accept(result)) {
            if (kind === "field") initializers.unshift(_);
            else descriptor[key] = _;
        }
    }
    if (target) Object.defineProperty(target, contextIn.name, descriptor);
    done = true;
};
var __setFunctionName = (this && this.__setFunctionName) || function (f, name, prefix) {
    if (typeof name === "symbol") name = name.description ? "[".concat(name.description, "]") : "";
    return Object.defineProperty(f, "name", { configurable: true, value: prefix ? "".concat(prefix, " ", name) : name });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PermissionsController = void 0;
var openapi = require("@nestjs/swagger");
var common_1 = require("@nestjs/common");
var swagger_1 = require("@nestjs/swagger");
var response_message_decorator_1 = require("../common/decorators/response-message.decorator");
var PermissionsController = function () {
    var _classDecorators = [(0, swagger_1.ApiTags)('permissions'), (0, swagger_1.ApiBearerAuth)('access-token'), (0, common_1.Controller)('permissions')];
    var _classDescriptor;
    var _classExtraInitializers = [];
    var _classThis;
    var _instanceExtraInitializers = [];
    var _create_decorators;
    var _findAll_decorators;
    var _findOne_decorators;
    var _update_decorators;
    var _remove_decorators;
    var PermissionsController = _classThis = /** @class */ (function () {
        function PermissionsController_1(permissionsService) {
            this.permissionsService = (__runInitializers(this, _instanceExtraInitializers), permissionsService);
        }
        PermissionsController_1.prototype.create = function (createPermissionDto) {
            return this.permissionsService.create(createPermissionDto);
        };
        PermissionsController_1.prototype.findAll = function () {
            return this.permissionsService.findAll();
        };
        PermissionsController_1.prototype.findOne = function (id) {
            return this.permissionsService.findOne(id);
        };
        PermissionsController_1.prototype.update = function (id, updatePermissionDto) {
            return this.permissionsService.update(id, updatePermissionDto);
        };
        PermissionsController_1.prototype.remove = function (id) {
            return this.permissionsService.remove(id);
        };
        return PermissionsController_1;
    }());
    __setFunctionName(_classThis, "PermissionsController");
    (function () {
        var _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(null) : void 0;
        _create_decorators = [(0, common_1.Post)(), (0, swagger_1.ApiOperation)({ summary: 'Create a new permission' }), (0, swagger_1.ApiResponse)({ status: 201, description: 'Permission created successfully' }), (0, response_message_decorator_1.ResponseMessage)('Permission created successfully'), openapi.ApiResponse({ status: 201, type: require("./entities/permission.entity").Permission })];
        _findAll_decorators = [(0, common_1.Get)(), (0, swagger_1.ApiOperation)({ summary: 'Get all permissions' }), (0, swagger_1.ApiResponse)({ status: 200, description: 'Permissions retrieved successfully' }), (0, response_message_decorator_1.ResponseMessage)('Permissions retrieved successfully'), openapi.ApiResponse({ status: 200, type: [require("./entities/permission.entity").Permission] })];
        _findOne_decorators = [(0, common_1.Get)(':id'), (0, swagger_1.ApiOperation)({ summary: 'Get permission by ID' }), (0, swagger_1.ApiResponse)({ status: 200, description: 'Permission retrieved successfully' }), (0, response_message_decorator_1.ResponseMessage)('Permission retrieved successfully'), openapi.ApiResponse({ status: 200, type: require("./entities/permission.entity").Permission })];
        _update_decorators = [(0, common_1.Patch)(':id'), (0, swagger_1.ApiOperation)({ summary: 'Update permission by ID' }), (0, swagger_1.ApiResponse)({ status: 200, description: 'Permission updated successfully' }), (0, response_message_decorator_1.ResponseMessage)('Permission updated successfully'), openapi.ApiResponse({ status: 200, type: require("./entities/permission.entity").Permission })];
        _remove_decorators = [(0, common_1.Delete)(':id'), (0, swagger_1.ApiOperation)({ summary: 'Delete permission by ID' }), (0, swagger_1.ApiResponse)({ status: 200, description: 'Permission deleted successfully' }), (0, response_message_decorator_1.ResponseMessage)('Permission deleted successfully'), openapi.ApiResponse({ status: 200 })];
        __esDecorate(_classThis, null, _create_decorators, { kind: "method", name: "create", static: false, private: false, access: { has: function (obj) { return "create" in obj; }, get: function (obj) { return obj.create; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _findAll_decorators, { kind: "method", name: "findAll", static: false, private: false, access: { has: function (obj) { return "findAll" in obj; }, get: function (obj) { return obj.findAll; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _findOne_decorators, { kind: "method", name: "findOne", static: false, private: false, access: { has: function (obj) { return "findOne" in obj; }, get: function (obj) { return obj.findOne; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _update_decorators, { kind: "method", name: "update", static: false, private: false, access: { has: function (obj) { return "update" in obj; }, get: function (obj) { return obj.update; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _remove_decorators, { kind: "method", name: "remove", static: false, private: false, access: { has: function (obj) { return "remove" in obj; }, get: function (obj) { return obj.remove; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(null, _classDescriptor = { value: _classThis }, _classDecorators, { kind: "class", name: _classThis.name, metadata: _metadata }, null, _classExtraInitializers);
        PermissionsController = _classThis = _classDescriptor.value;
        if (_metadata) Object.defineProperty(_classThis, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
        __runInitializers(_classThis, _classExtraInitializers);
    })();
    return PermissionsController = _classThis;
}();
exports.PermissionsController = PermissionsController;
