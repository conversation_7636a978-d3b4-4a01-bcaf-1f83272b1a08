"use strict";
var __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
    function accept(f) { if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected"); return f; }
    var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
    var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
    var _, done = false;
    for (var i = decorators.length - 1; i >= 0; i--) {
        var context = {};
        for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
        for (var p in contextIn.access) context.access[p] = contextIn.access[p];
        context.addInitializer = function (f) { if (done) throw new TypeError("Cannot add initializers after decoration has completed"); extraInitializers.push(accept(f || null)); };
        var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
        if (kind === "accessor") {
            if (result === void 0) continue;
            if (result === null || typeof result !== "object") throw new TypeError("Object expected");
            if (_ = accept(result.get)) descriptor.get = _;
            if (_ = accept(result.set)) descriptor.set = _;
            if (_ = accept(result.init)) initializers.unshift(_);
        }
        else if (_ = accept(result)) {
            if (kind === "field") initializers.unshift(_);
            else descriptor[key] = _;
        }
    }
    if (target) Object.defineProperty(target, contextIn.name, descriptor);
    done = true;
};
var __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {
    var useValue = arguments.length > 2;
    for (var i = 0; i < initializers.length; i++) {
        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
    }
    return useValue ? value : void 0;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateApplicationDto = void 0;
var openapi = require("@nestjs/swagger");
var swagger_1 = require("@nestjs/swagger");
var class_validator_1 = require("class-validator");
var CreateApplicationDto = function () {
    var _a;
    var _job_id_decorators;
    var _job_id_initializers = [];
    var _job_id_extraInitializers = [];
    var _resume_id_decorators;
    var _resume_id_initializers = [];
    var _resume_id_extraInitializers = [];
    var _status_decorators;
    var _status_initializers = [];
    var _status_extraInitializers = [];
    return _a = /** @class */ (function () {
            function CreateApplicationDto() {
                this.job_id = __runInitializers(this, _job_id_initializers, void 0);
                this.resume_id = (__runInitializers(this, _job_id_extraInitializers), __runInitializers(this, _resume_id_initializers, void 0));
                this.status = (__runInitializers(this, _resume_id_extraInitializers), __runInitializers(this, _status_initializers, void 0));
                __runInitializers(this, _status_extraInitializers);
            }
            CreateApplicationDto._OPENAPI_METADATA_FACTORY = function () {
                return { job_id: { required: true, type: function () { return Number; } }, resume_id: { required: true, type: function () { return Number; } }, status: { required: false, type: function () { return String; }, enum: ['pending', 'reviewed', 'accepted', 'rejected'] } };
            };
            return CreateApplicationDto;
        }()),
        (function () {
            var _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(null) : void 0;
            _job_id_decorators = [(0, swagger_1.ApiProperty)({ example: 1, description: 'Job ID' }), (0, class_validator_1.IsNotEmpty)(), (0, class_validator_1.IsNumber)()];
            _resume_id_decorators = [(0, swagger_1.ApiProperty)({ example: 1, description: 'Resume ID' }), (0, class_validator_1.IsNotEmpty)(), (0, class_validator_1.IsNumber)()];
            _status_decorators = [(0, swagger_1.ApiProperty)({
                    example: 'pending',
                    description: 'Application status',
                    enum: ['pending', 'reviewed', 'accepted', 'rejected']
                }), (0, class_validator_1.IsOptional)(), (0, class_validator_1.IsIn)(['pending', 'reviewed', 'accepted', 'rejected'])];
            __esDecorate(null, null, _job_id_decorators, { kind: "field", name: "job_id", static: false, private: false, access: { has: function (obj) { return "job_id" in obj; }, get: function (obj) { return obj.job_id; }, set: function (obj, value) { obj.job_id = value; } }, metadata: _metadata }, _job_id_initializers, _job_id_extraInitializers);
            __esDecorate(null, null, _resume_id_decorators, { kind: "field", name: "resume_id", static: false, private: false, access: { has: function (obj) { return "resume_id" in obj; }, get: function (obj) { return obj.resume_id; }, set: function (obj, value) { obj.resume_id = value; } }, metadata: _metadata }, _resume_id_initializers, _resume_id_extraInitializers);
            __esDecorate(null, null, _status_decorators, { kind: "field", name: "status", static: false, private: false, access: { has: function (obj) { return "status" in obj; }, get: function (obj) { return obj.status; }, set: function (obj, value) { obj.status = value; } }, metadata: _metadata }, _status_initializers, _status_extraInitializers);
            if (_metadata) Object.defineProperty(_a, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
        })(),
        _a;
}();
exports.CreateApplicationDto = CreateApplicationDto;
