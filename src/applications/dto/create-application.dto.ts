import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString, IsEnum } from 'class-validator';
import { ApplicationStatus } from '../entities/application.entity';

export class CreateApplicationDto {
  @ApiProperty({ example: 1, description: 'Job ID' })
  @IsNotEmpty()
  @IsNumber()
  job_id: number;

  @ApiProperty({ example: 'I am very interested in this position...', description: 'Cover letter' })
  @IsOptional()
  @IsString()
  cover_letter?: string;

  @ApiProperty({ example: 'https://example.com/resume.pdf', description: 'Resume URL' })
  @IsOptional()
  @IsString()
  resume_url?: string;

  @ApiProperty({
    example: ApplicationStatus.PENDING,
    description: 'Application status',
    enum: ApplicationStatus
  })
  @IsOptional()
  @IsEnum(ApplicationStatus)
  status?: ApplicationStatus;

  @ApiProperty({ example: 'Additional notes about the application', description: 'Notes' })
  @IsOptional()
  @IsString()
  notes?: string;
}
