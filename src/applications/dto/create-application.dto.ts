import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsN<PERSON>ber, IsOptional, IsIn } from 'class-validator';

export class CreateApplicationDto {
  @ApiProperty({ example: 1, description: 'Job ID' })
  @IsNotEmpty()
  @IsNumber()
  job_id: number;

  @ApiProperty({ example: 1, description: 'Resume ID' })
  @IsNotEmpty()
  @IsNumber()
  resume_id: number;

  @ApiProperty({ 
    example: 'pending', 
    description: 'Application status',
    enum: ['pending', 'reviewed', 'accepted', 'rejected']
  })
  @IsOptional()
  @IsIn(['pending', 'reviewed', 'accepted', 'rejected'])
  status?: string;
}
