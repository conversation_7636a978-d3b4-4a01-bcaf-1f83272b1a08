import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { Application } from './entities/application.entity';
import { CreateApplicationDto } from './dto/create-application.dto';
import { UpdateApplicationDto } from './dto/update-application.dto';
import { JobsService } from '../jobs/jobs.service';
import { ResumesService } from '../resumes/resumes.service';

@Injectable()
export class ApplicationsService {
  constructor(
    @InjectRepository(Application)
    private applicationRepository: Repository<Application>,
    private jobsService: JobsService,
    private resumesService: ResumesService,
  ) {}

  async create(createApplicationDto: CreateApplicationDto): Promise<Application> {
    // Verify job and resume exist
    await this.jobsService.findOne(createApplicationDto.job_id);
    await this.resumesService.findOne(createApplicationDto.resume_id);

    // Check if application already exists
    const existingApplication = await this.applicationRepository.findOne({
      where: {
        job_id: createApplicationDto.job_id,
        resume_id: createApplicationDto.resume_id,
      },
    });

    if (existingApplication) {
      throw new BadRequestException('Application already exists for this job and resume');
    }

    const application = this.applicationRepository.create({
      ...createApplicationDto,
      application_id: Math.floor(Math.random() * 1000000),
      status: createApplicationDto.status || 'pending',
    });

    return this.applicationRepository.save(application);
  }

  async findAll(): Promise<Application[]> {
    return this.applicationRepository.find({
      relations: ['job_post', 'resume', 'resume.user', 'interviews'],
      order: { applied_at: 'DESC' },
    });
  }

  async findByUser(userId: number): Promise<Application[]> {
    return this.applicationRepository
      .createQueryBuilder('application')
      .leftJoinAndSelect('application.job_post', 'job_post')
      .leftJoinAndSelect('application.resume', 'resume')
      .leftJoinAndSelect('application.interviews', 'interviews')
      .leftJoinAndSelect('job_post.company', 'company')
      .where('resume.user_id = :userId', { userId })
      .orderBy('application.applied_at', 'DESC')
      .getMany();
  }

  async findByJob(jobId: number): Promise<Application[]> {
    return this.applicationRepository.find({
      where: { job_id: jobId },
      relations: ['job_post', 'resume', 'resume.user', 'interviews'],
      order: { applied_at: 'DESC' },
    });
  }

  async findOne(id: number): Promise<Application> {
    const application = await this.applicationRepository.findOne({
      where: { id },
      relations: ['job_post', 'resume', 'resume.user', 'interviews'],
    });

    if (!application) {
      throw new NotFoundException('Application not found');
    }

    return application;
  }

  async update(id: number, updateApplicationDto: UpdateApplicationDto): Promise<Application> {
    const application = await this.findOne(id);

    if (updateApplicationDto.job_id && updateApplicationDto.job_id !== application.job_id) {
      await this.jobsService.findOne(updateApplicationDto.job_id);
    }

    if (updateApplicationDto.resume_id && updateApplicationDto.resume_id !== application.resume_id) {
      await this.resumesService.findOne(updateApplicationDto.resume_id);
    }

    await this.applicationRepository.update(id, updateApplicationDto);
    return this.findOne(id);
  }

  async remove(id: number): Promise<void> {
    const application = await this.findOne(id);
    await this.applicationRepository.remove(application);
  }
}
