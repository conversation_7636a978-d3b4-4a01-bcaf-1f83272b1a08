import {
  Injectable,
  NotFoundException,
  ConflictException,
  ForbiddenException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Application, ApplicationStatus } from './entities/application.entity';
import { CreateApplicationDto } from './dto/create-application.dto';
import { UpdateApplicationDto } from './dto/update-application.dto';
import { JobsService } from '../jobs/jobs.service';
import { UsersService } from '../users/users.service';

@Injectable()
export class ApplicationsService {
  constructor(
    @InjectRepository(Application)
    private readonly applicationRepository: Repository<Application>,
    private readonly jobsService: JobsService,
    private readonly usersService: UsersService,
  ) {}

  async create(createApplicationDto: CreateApplicationDto, userId: number): Promise<Application> {
    // Verify job exists
    await this.jobsService.findOne(createApplicationDto.job_id);

    // Check if user already applied for this job
    const existingApplication = await this.applicationRepository.findOne({
      where: {
        user_id: userId,
        job_id: createApplicationDto.job_id,
      },
    });

    if (existingApplication) {
      throw new ConflictException('You have already applied for this job');
    }

    // Create application
    const application = this.applicationRepository.create({
      ...createApplicationDto,
      user_id: userId,
    });

    const savedApplication = await this.applicationRepository.save(application);

    // Increment applications count for the job
    await this.jobsService.incrementApplicationsCount(createApplicationDto.job_id);

    return savedApplication;
  }

  async findAll(): Promise<Application[]> {
    return this.applicationRepository.find({
      relations: ['user', 'job', 'job.company'],
      order: { created_at: 'DESC' },
    });
  }

  async findByUser(userId: number): Promise<Application[]> {
    return this.applicationRepository.find({
      where: { user_id: userId },
      relations: ['job', 'job.company'],
      order: { created_at: 'DESC' },
    });
  }

  async findByJob(jobId: number): Promise<Application[]> {
    return this.applicationRepository.find({
      where: { job_id: jobId },
      relations: ['user'],
      order: { created_at: 'DESC' },
    });
  }

  async findByStatus(status: ApplicationStatus): Promise<Application[]> {
    return this.applicationRepository.find({
      where: { status },
      relations: ['user', 'job', 'job.company'],
      order: { created_at: 'DESC' },
    });
  }

  async findOne(id: number): Promise<Application> {
    const application = await this.applicationRepository.findOne({
      where: { application_id: id },
      relations: ['user', 'job', 'job.company'],
    });

    if (!application) {
      throw new NotFoundException(`Application with ID ${id} not found`);
    }

    return application;
  }

  async update(id: number, updateApplicationDto: UpdateApplicationDto, userId?: number): Promise<Application> {
    const application = await this.findOne(id);

    // If userId is provided, check if user owns this application
    if (userId && application.user_id !== userId) {
      throw new ForbiddenException('You can only update your own applications');
    }

    Object.assign(application, updateApplicationDto);
    return this.applicationRepository.save(application);
  }

  async remove(id: number, userId?: number): Promise<void> {
    const application = await this.findOne(id);

    // If userId is provided, check if user owns this application
    if (userId && application.user_id !== userId) {
      throw new ForbiddenException('You can only delete your own applications');
    }

    await this.applicationRepository.remove(application);

    // Decrement applications count for the job
    await this.jobsService.decrementApplicationsCount(application.job_id);
  }

  async updateStatus(id: number, status: ApplicationStatus): Promise<Application> {
    const application = await this.findOne(id);
    application.status = status;
    return this.applicationRepository.save(application);
  }

  async getApplicationStats(userId?: number): Promise<any> {
    const query = this.applicationRepository.createQueryBuilder('application');

    if (userId) {
      query.where('application.user_id = :userId', { userId });
    }

    const [total, pending, reviewing, interviewed, accepted, rejected, withdrawn] = await Promise.all([
      query.getCount(),
      query.clone().andWhere('application.status = :status', { status: ApplicationStatus.PENDING }).getCount(),
      query.clone().andWhere('application.status = :status', { status: ApplicationStatus.REVIEWING }).getCount(),
      query.clone().andWhere('application.status = :status', { status: ApplicationStatus.INTERVIEWED }).getCount(),
      query.clone().andWhere('application.status = :status', { status: ApplicationStatus.ACCEPTED }).getCount(),
      query.clone().andWhere('application.status = :status', { status: ApplicationStatus.REJECTED }).getCount(),
      query.clone().andWhere('application.status = :status', { status: ApplicationStatus.WITHDRAWN }).getCount(),
    ]);

    return {
      total,
      pending,
      reviewing,
      interviewed,
      accepted,
      rejected,
      withdrawn,
    };
  }
}
