"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
    function accept(f) { if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected"); return f; }
    var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
    var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
    var _, done = false;
    for (var i = decorators.length - 1; i >= 0; i--) {
        var context = {};
        for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
        for (var p in contextIn.access) context.access[p] = contextIn.access[p];
        context.addInitializer = function (f) { if (done) throw new TypeError("Cannot add initializers after decoration has completed"); extraInitializers.push(accept(f || null)); };
        var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
        if (kind === "accessor") {
            if (result === void 0) continue;
            if (result === null || typeof result !== "object") throw new TypeError("Object expected");
            if (_ = accept(result.get)) descriptor.get = _;
            if (_ = accept(result.set)) descriptor.set = _;
            if (_ = accept(result.init)) initializers.unshift(_);
        }
        else if (_ = accept(result)) {
            if (kind === "field") initializers.unshift(_);
            else descriptor[key] = _;
        }
    }
    if (target) Object.defineProperty(target, contextIn.name, descriptor);
    done = true;
};
var __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {
    var useValue = arguments.length > 2;
    for (var i = 0; i < initializers.length; i++) {
        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
    }
    return useValue ? value : void 0;
};
var __setFunctionName = (this && this.__setFunctionName) || function (f, name, prefix) {
    if (typeof name === "symbol") name = name.description ? "[".concat(name.description, "]") : "";
    return Object.defineProperty(f, "name", { configurable: true, value: prefix ? "".concat(prefix, " ", name) : name });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Application = void 0;
var openapi = require("@nestjs/swagger");
var typeorm_1 = require("typeorm");
var base_entity_1 = require("../../common/entities/base.entity");
var job_post_entity_1 = require("../../jobs/entities/job-post.entity");
var resume_entity_1 = require("../../resumes/entities/resume.entity");
var interview_entity_1 = require("../../interviews/entities/interview.entity");
var Application = function () {
    var _classDecorators = [(0, typeorm_1.Entity)('applications')];
    var _classDescriptor;
    var _classExtraInitializers = [];
    var _classThis;
    var _classSuper = base_entity_1.BaseEntity;
    var _application_id_decorators;
    var _application_id_initializers = [];
    var _application_id_extraInitializers = [];
    var _job_id_decorators;
    var _job_id_initializers = [];
    var _job_id_extraInitializers = [];
    var _resume_id_decorators;
    var _resume_id_initializers = [];
    var _resume_id_extraInitializers = [];
    var _status_decorators;
    var _status_initializers = [];
    var _status_extraInitializers = [];
    var _applied_at_decorators;
    var _applied_at_initializers = [];
    var _applied_at_extraInitializers = [];
    var _job_post_decorators;
    var _job_post_initializers = [];
    var _job_post_extraInitializers = [];
    var _resume_decorators;
    var _resume_initializers = [];
    var _resume_extraInitializers = [];
    var _interviews_decorators;
    var _interviews_initializers = [];
    var _interviews_extraInitializers = [];
    var Application = _classThis = /** @class */ (function (_super) {
        __extends(Application_1, _super);
        function Application_1() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.application_id = __runInitializers(_this, _application_id_initializers, void 0);
            _this.job_id = (__runInitializers(_this, _application_id_extraInitializers), __runInitializers(_this, _job_id_initializers, void 0));
            _this.resume_id = (__runInitializers(_this, _job_id_extraInitializers), __runInitializers(_this, _resume_id_initializers, void 0));
            _this.status = (__runInitializers(_this, _resume_id_extraInitializers), __runInitializers(_this, _status_initializers, void 0));
            _this.applied_at = (__runInitializers(_this, _status_extraInitializers), __runInitializers(_this, _applied_at_initializers, void 0));
            // Relations
            _this.job_post = (__runInitializers(_this, _applied_at_extraInitializers), __runInitializers(_this, _job_post_initializers, void 0));
            _this.resume = (__runInitializers(_this, _job_post_extraInitializers), __runInitializers(_this, _resume_initializers, void 0));
            // Derived relation through resume
            _this.user = __runInitializers(_this, _resume_extraInitializers);
            _this.interviews = __runInitializers(_this, _interviews_initializers, void 0);
            __runInitializers(_this, _interviews_extraInitializers);
            return _this;
        }
        Application_1._OPENAPI_METADATA_FACTORY = function () {
            return { application_id: { required: true, type: function () { return Number; } }, job_id: { required: true, type: function () { return Number; } }, resume_id: { required: true, type: function () { return Number; } }, status: { required: true, type: function () { return String; } }, applied_at: { required: true, type: function () { return Date; } }, job_post: { required: true, type: function () { return require("../../jobs/entities/job-post.entity").JobPost; } }, resume: { required: true, type: function () { return require("../../resumes/entities/resume.entity").Resume; } }, user: { required: true, type: function () { return require("../../users/entities/user.entity").User; } }, interviews: { required: true, type: function () { return [require("../../interviews/entities/interview.entity").Interview]; } } };
        };
        return Application_1;
    }(_classSuper));
    __setFunctionName(_classThis, "Application");
    (function () {
        var _a;
        var _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create((_a = _classSuper[Symbol.metadata]) !== null && _a !== void 0 ? _a : null) : void 0;
        _application_id_decorators = [(0, typeorm_1.Column)({ name: 'application_id' })];
        _job_id_decorators = [(0, typeorm_1.Column)({ name: 'job_id' })];
        _resume_id_decorators = [(0, typeorm_1.Column)({ name: 'resume_id' })];
        _status_decorators = [(0, typeorm_1.Column)({ name: 'status', length: 20, default: 'pending' })];
        _applied_at_decorators = [(0, typeorm_1.Column)({ name: 'applied_at', type: 'timestamp', default: function () { return 'CURRENT_TIMESTAMP'; } })];
        _job_post_decorators = [(0, typeorm_1.ManyToOne)(function () { return job_post_entity_1.JobPost; }, function (jobPost) { return jobPost.applications; }, { onDelete: 'CASCADE' }), (0, typeorm_1.JoinColumn)({ name: 'job_id' })];
        _resume_decorators = [(0, typeorm_1.ManyToOne)(function () { return resume_entity_1.Resume; }, function (resume) { return resume.applications; }, { onDelete: 'CASCADE' }), (0, typeorm_1.JoinColumn)({ name: 'resume_id' })];
        _interviews_decorators = [(0, typeorm_1.OneToMany)(function () { return interview_entity_1.Interview; }, function (interview) { return interview.application; })];
        __esDecorate(null, null, _application_id_decorators, { kind: "field", name: "application_id", static: false, private: false, access: { has: function (obj) { return "application_id" in obj; }, get: function (obj) { return obj.application_id; }, set: function (obj, value) { obj.application_id = value; } }, metadata: _metadata }, _application_id_initializers, _application_id_extraInitializers);
        __esDecorate(null, null, _job_id_decorators, { kind: "field", name: "job_id", static: false, private: false, access: { has: function (obj) { return "job_id" in obj; }, get: function (obj) { return obj.job_id; }, set: function (obj, value) { obj.job_id = value; } }, metadata: _metadata }, _job_id_initializers, _job_id_extraInitializers);
        __esDecorate(null, null, _resume_id_decorators, { kind: "field", name: "resume_id", static: false, private: false, access: { has: function (obj) { return "resume_id" in obj; }, get: function (obj) { return obj.resume_id; }, set: function (obj, value) { obj.resume_id = value; } }, metadata: _metadata }, _resume_id_initializers, _resume_id_extraInitializers);
        __esDecorate(null, null, _status_decorators, { kind: "field", name: "status", static: false, private: false, access: { has: function (obj) { return "status" in obj; }, get: function (obj) { return obj.status; }, set: function (obj, value) { obj.status = value; } }, metadata: _metadata }, _status_initializers, _status_extraInitializers);
        __esDecorate(null, null, _applied_at_decorators, { kind: "field", name: "applied_at", static: false, private: false, access: { has: function (obj) { return "applied_at" in obj; }, get: function (obj) { return obj.applied_at; }, set: function (obj, value) { obj.applied_at = value; } }, metadata: _metadata }, _applied_at_initializers, _applied_at_extraInitializers);
        __esDecorate(null, null, _job_post_decorators, { kind: "field", name: "job_post", static: false, private: false, access: { has: function (obj) { return "job_post" in obj; }, get: function (obj) { return obj.job_post; }, set: function (obj, value) { obj.job_post = value; } }, metadata: _metadata }, _job_post_initializers, _job_post_extraInitializers);
        __esDecorate(null, null, _resume_decorators, { kind: "field", name: "resume", static: false, private: false, access: { has: function (obj) { return "resume" in obj; }, get: function (obj) { return obj.resume; }, set: function (obj, value) { obj.resume = value; } }, metadata: _metadata }, _resume_initializers, _resume_extraInitializers);
        __esDecorate(null, null, _interviews_decorators, { kind: "field", name: "interviews", static: false, private: false, access: { has: function (obj) { return "interviews" in obj; }, get: function (obj) { return obj.interviews; }, set: function (obj, value) { obj.interviews = value; } }, metadata: _metadata }, _interviews_initializers, _interviews_extraInitializers);
        __esDecorate(null, _classDescriptor = { value: _classThis }, _classDecorators, { kind: "class", name: _classThis.name, metadata: _metadata }, null, _classExtraInitializers);
        Application = _classThis = _classDescriptor.value;
        if (_metadata) Object.defineProperty(_classThis, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
        __runInitializers(_classThis, _classExtraInitializers);
    })();
    return Application = _classThis;
}();
exports.Application = Application;
