import { <PERSON><PERSON><PERSON>, Column, ManyToOne, OneToMany, <PERSON><PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm';
import { BaseEntity } from '../../common/entities/base.entity';
import { JobPost } from '../../jobs/entities/job-post.entity';
import { Resume } from '../../resumes/entities/resume.entity';
import { User } from '../../users/entities/user.entity';
import { Interview } from '../../interviews/entities/interview.entity';

@Entity('applications')
export class Application extends BaseEntity {
  @Column({ name: 'application_id' })
  application_id: number;

  @Column({ name: 'job_id' })
  job_id: number;

  @Column({ name: 'resume_id' })
  resume_id: number;

  @Column({ name: 'status', length: 20, default: 'pending' })
  status: string;

  @Column({ name: 'applied_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  applied_at: Date;

  // Relations
  @ManyToOne(() => JobPost, (jobPost) => jobPost.applications, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'job_id' })
  job_post: JobPost;

  @ManyToOne(() => Resume, (resume) => resume.applications, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'resume_id' })
  resume: Resume;

  // Derived relation through resume
  user: User;

  @OneToMany(() => Interview, (interview) => interview.application)
  interviews: Interview[];
}
