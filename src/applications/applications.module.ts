import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { ApplicationsService } from './applications.service';
import { ApplicationsController } from './applications.controller';
import { Application } from './entities/application.entity';
import { JobsModule } from '../jobs/jobs.module';
import { ResumesModule } from '../resumes/resumes.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Application]),
    JobsModule,
    ResumesModule,
  ],
  controllers: [ApplicationsController],
  providers: [ApplicationsService],
  exports: [ApplicationsService],
})
export class ApplicationsModule {}
