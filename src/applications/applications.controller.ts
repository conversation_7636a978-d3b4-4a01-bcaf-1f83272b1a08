import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ParseIntPipe,
  Query,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { ApplicationsService } from './applications.service';
import { CreateApplicationDto } from './dto/create-application.dto';
import { UpdateApplicationDto } from './dto/update-application.dto';
import { Application, ApplicationStatus } from './entities/application.entity';
import { User } from '../common/decorators/user.decorator';
import { IUser } from '../common/interfaces/user.interface';
import { Public } from '../common/decorators/public.decorator';

@ApiTags('applications')
@Controller('applications')
export class ApplicationsController {
  constructor(private readonly applicationsService: ApplicationsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new application' })
  @ApiResponse({ status: 201, description: 'Application created successfully', type: Application })
  @ApiResponse({ status: 409, description: 'Already applied for this job' })
  create(
    @Body() createApplicationDto: CreateApplicationDto,
    @User() user: IUser,
  ): Promise<Application> {
    return this.applicationsService.create(createApplicationDto, user.user_id);
  }

  @Get()
  @Public()
  @ApiOperation({ summary: 'Get all applications (admin only)' })
  @ApiResponse({ status: 200, description: 'List of applications', type: [Application] })
  findAll(): Promise<Application[]> {
    return this.applicationsService.findAll();
  }

  @Get('my-applications')
  @ApiOperation({ summary: 'Get current user applications' })
  @ApiResponse({ status: 200, description: 'User applications', type: [Application] })
  findMyApplications(@User() user: IUser): Promise<Application[]> {
    return this.applicationsService.findByUser(user.user_id);
  }

  @Get('job/:jobId')
  @ApiOperation({ summary: 'Get applications for a specific job' })
  @ApiResponse({ status: 200, description: 'Job applications', type: [Application] })
  findByJob(@Param('jobId', ParseIntPipe) jobId: number): Promise<Application[]> {
    return this.applicationsService.findByJob(jobId);
  }

  @Get('status/:status')
  @ApiOperation({ summary: 'Get applications by status' })
  @ApiResponse({ status: 200, description: 'Applications by status', type: [Application] })
  findByStatus(@Param('status') status: ApplicationStatus): Promise<Application[]> {
    return this.applicationsService.findByStatus(status);
  }

  @Get('stats')
  @ApiOperation({ summary: 'Get application statistics' })
  @ApiQuery({ name: 'userId', required: false, description: 'User ID for user-specific stats' })
  @ApiResponse({ status: 200, description: 'Application statistics' })
  getStats(@Query('userId') userId?: number, @User() user?: IUser): Promise<any> {
    // If userId is not provided and user is authenticated, use current user
    const targetUserId = userId || (user ? user.user_id : undefined);
    return this.applicationsService.getApplicationStats(targetUserId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get application by ID' })
  @ApiResponse({ status: 200, description: 'Application found', type: Application })
  @ApiResponse({ status: 404, description: 'Application not found' })
  findOne(@Param('id', ParseIntPipe) id: number): Promise<Application> {
    return this.applicationsService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update application by ID' })
  @ApiResponse({ status: 200, description: 'Application updated successfully', type: Application })
  @ApiResponse({ status: 404, description: 'Application not found' })
  @ApiResponse({ status: 403, description: 'Forbidden - can only update own applications' })
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateApplicationDto: UpdateApplicationDto,
    @User() user: IUser,
  ): Promise<Application> {
    return this.applicationsService.update(id, updateApplicationDto, user.user_id);
  }

  @Patch(':id/status')
  @ApiOperation({ summary: 'Update application status (employer/admin only)' })
  @ApiResponse({ status: 200, description: 'Status updated successfully', type: Application })
  @ApiResponse({ status: 404, description: 'Application not found' })
  updateStatus(
    @Param('id', ParseIntPipe) id: number,
    @Body('status') status: ApplicationStatus,
  ): Promise<Application> {
    return this.applicationsService.updateStatus(id, status);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete application by ID' })
  @ApiResponse({ status: 200, description: 'Application deleted successfully' })
  @ApiResponse({ status: 404, description: 'Application not found' })
  @ApiResponse({ status: 403, description: 'Forbidden - can only delete own applications' })
  remove(
    @Param('id', ParseIntPipe) id: number,
    @User() user: IUser,
  ): Promise<void> {
    return this.applicationsService.remove(id, user.user_id);
  }
}
