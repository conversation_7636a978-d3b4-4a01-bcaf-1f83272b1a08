import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ParseIntPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';

import { ApplicationsService } from './applications.service';
import { CreateApplicationDto } from './dto/create-application.dto';
import { UpdateApplicationDto } from './dto/update-application.dto';
import { ResponseMessage } from '../common/decorators/response-message.decorator';
import { User } from '../common/decorators/user.decorator';
import { IUser } from '../auth/interfaces/user.interface';

@ApiTags('applications')
@ApiBearerAuth('access-token')
@Controller('applications')
export class ApplicationsController {
  constructor(private readonly applicationsService: ApplicationsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new job application' })
  @ApiResponse({ status: 201, description: 'Application created successfully' })
  @ResponseMessage('Application created successfully')
  create(@Body() createApplicationDto: CreateApplicationDto) {
    return this.applicationsService.create(createApplicationDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all applications' })
  @ApiResponse({ status: 200, description: 'Applications retrieved successfully' })
  @ResponseMessage('Applications retrieved successfully')
  findAll() {
    return this.applicationsService.findAll();
  }

  @Get('my-applications')
  @ApiOperation({ summary: 'Get current user applications' })
  @ApiResponse({ status: 200, description: 'User applications retrieved successfully' })
  @ResponseMessage('User applications retrieved successfully')
  getMyApplications(@User() user: IUser) {
    return this.applicationsService.findByUser(user.id);
  }

  @Get('job/:jobId')
  @ApiOperation({ summary: 'Get applications by job ID' })
  @ApiResponse({ status: 200, description: 'Job applications retrieved successfully' })
  @ResponseMessage('Job applications retrieved successfully')
  findByJob(@Param('jobId', ParseIntPipe) jobId: number) {
    return this.applicationsService.findByJob(jobId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get application by ID' })
  @ApiResponse({ status: 200, description: 'Application retrieved successfully' })
  @ResponseMessage('Application retrieved successfully')
  findOne(@Param('id', ParseIntPipe) id: number) {
    return this.applicationsService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update application by ID' })
  @ApiResponse({ status: 200, description: 'Application updated successfully' })
  @ResponseMessage('Application updated successfully')
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateApplicationDto: UpdateApplicationDto,
  ) {
    return this.applicationsService.update(id, updateApplicationDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete application by ID' })
  @ApiResponse({ status: 200, description: 'Application deleted successfully' })
  @ResponseMessage('Application deleted successfully')
  remove(@Param('id', ParseIntPipe) id: number) {
    return this.applicationsService.remove(id);
  }
}
