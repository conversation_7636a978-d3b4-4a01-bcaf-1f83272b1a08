"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
    function accept(f) { if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected"); return f; }
    var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
    var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
    var _, done = false;
    for (var i = decorators.length - 1; i >= 0; i--) {
        var context = {};
        for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
        for (var p in contextIn.access) context.access[p] = contextIn.access[p];
        context.addInitializer = function (f) { if (done) throw new TypeError("Cannot add initializers after decoration has completed"); extraInitializers.push(accept(f || null)); };
        var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
        if (kind === "accessor") {
            if (result === void 0) continue;
            if (result === null || typeof result !== "object") throw new TypeError("Object expected");
            if (_ = accept(result.get)) descriptor.get = _;
            if (_ = accept(result.set)) descriptor.set = _;
            if (_ = accept(result.init)) initializers.unshift(_);
        }
        else if (_ = accept(result)) {
            if (kind === "field") initializers.unshift(_);
            else descriptor[key] = _;
        }
    }
    if (target) Object.defineProperty(target, contextIn.name, descriptor);
    done = true;
};
var __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {
    var useValue = arguments.length > 2;
    for (var i = 0; i < initializers.length; i++) {
        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
    }
    return useValue ? value : void 0;
};
var __setFunctionName = (this && this.__setFunctionName) || function (f, name, prefix) {
    if (typeof name === "symbol") name = name.description ? "[".concat(name.description, "]") : "";
    return Object.defineProperty(f, "name", { configurable: true, value: prefix ? "".concat(prefix, " ", name) : name });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JobPost = void 0;
var openapi = require("@nestjs/swagger");
var typeorm_1 = require("typeorm");
var base_entity_1 = require("../../common/entities/base.entity");
var company_entity_1 = require("../../companies/entities/company.entity");
var application_entity_1 = require("../../applications/entities/application.entity");
var saved_job_entity_1 = require("../../saved-jobs/entities/saved-job.entity");
var JobPost = function () {
    var _classDecorators = [(0, typeorm_1.Entity)('job_posts')];
    var _classDescriptor;
    var _classExtraInitializers = [];
    var _classThis;
    var _classSuper = base_entity_1.BaseEntity;
    var _job_id_decorators;
    var _job_id_initializers = [];
    var _job_id_extraInitializers = [];
    var _company_id_decorators;
    var _company_id_initializers = [];
    var _company_id_extraInitializers = [];
    var _job_title_decorators;
    var _job_title_initializers = [];
    var _job_title_extraInitializers = [];
    var _description_decorators;
    var _description_initializers = [];
    var _description_extraInitializers = [];
    var _location_decorators;
    var _location_initializers = [];
    var _location_extraInitializers = [];
    var _salary_decorators;
    var _salary_initializers = [];
    var _salary_extraInitializers = [];
    var _job_type_decorators;
    var _job_type_initializers = [];
    var _job_type_extraInitializers = [];
    var _status_decorators;
    var _status_initializers = [];
    var _status_extraInitializers = [];
    var _posted_date_decorators;
    var _posted_date_initializers = [];
    var _posted_date_extraInitializers = [];
    var _save_count_decorators;
    var _save_count_initializers = [];
    var _save_count_extraInitializers = [];
    var _company_decorators;
    var _company_initializers = [];
    var _company_extraInitializers = [];
    var _applications_decorators;
    var _applications_initializers = [];
    var _applications_extraInitializers = [];
    var _saved_by_users_decorators;
    var _saved_by_users_initializers = [];
    var _saved_by_users_extraInitializers = [];
    var JobPost = _classThis = /** @class */ (function (_super) {
        __extends(JobPost_1, _super);
        function JobPost_1() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.job_id = __runInitializers(_this, _job_id_initializers, void 0);
            _this.company_id = (__runInitializers(_this, _job_id_extraInitializers), __runInitializers(_this, _company_id_initializers, void 0));
            _this.job_title = (__runInitializers(_this, _company_id_extraInitializers), __runInitializers(_this, _job_title_initializers, void 0));
            _this.description = (__runInitializers(_this, _job_title_extraInitializers), __runInitializers(_this, _description_initializers, void 0));
            _this.location = (__runInitializers(_this, _description_extraInitializers), __runInitializers(_this, _location_initializers, void 0));
            _this.salary = (__runInitializers(_this, _location_extraInitializers), __runInitializers(_this, _salary_initializers, void 0));
            _this.job_type = (__runInitializers(_this, _salary_extraInitializers), __runInitializers(_this, _job_type_initializers, void 0));
            _this.status = (__runInitializers(_this, _job_type_extraInitializers), __runInitializers(_this, _status_initializers, void 0));
            _this.posted_date = (__runInitializers(_this, _status_extraInitializers), __runInitializers(_this, _posted_date_initializers, void 0));
            _this.save_count = (__runInitializers(_this, _posted_date_extraInitializers), __runInitializers(_this, _save_count_initializers, void 0));
            // Relations
            _this.company = (__runInitializers(_this, _save_count_extraInitializers), __runInitializers(_this, _company_initializers, void 0));
            _this.applications = (__runInitializers(_this, _company_extraInitializers), __runInitializers(_this, _applications_initializers, void 0));
            _this.saved_by_users = (__runInitializers(_this, _applications_extraInitializers), __runInitializers(_this, _saved_by_users_initializers, void 0));
            __runInitializers(_this, _saved_by_users_extraInitializers);
            return _this;
        }
        JobPost_1._OPENAPI_METADATA_FACTORY = function () {
            return { job_id: { required: true, type: function () { return Number; } }, company_id: { required: true, type: function () { return Number; } }, job_title: { required: true, type: function () { return String; } }, description: { required: true, type: function () { return String; } }, location: { required: true, type: function () { return String; } }, salary: { required: true, type: function () { return String; } }, job_type: { required: true, type: function () { return String; } }, status: { required: true, type: function () { return String; } }, posted_date: { required: true, type: function () { return Date; } }, save_count: { required: true, type: function () { return Number; } }, company: { required: true, type: function () { return require("../../companies/entities/company.entity").Company; } }, applications: { required: true, type: function () { return [require("../../applications/entities/application.entity").Application]; } }, saved_by_users: { required: true, type: function () { return [require("../../saved-jobs/entities/saved-job.entity").SavedJob]; } } };
        };
        return JobPost_1;
    }(_classSuper));
    __setFunctionName(_classThis, "JobPost");
    (function () {
        var _a;
        var _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create((_a = _classSuper[Symbol.metadata]) !== null && _a !== void 0 ? _a : null) : void 0;
        _job_id_decorators = [(0, typeorm_1.Column)({ name: 'job_id' })];
        _company_id_decorators = [(0, typeorm_1.Column)({ name: 'company_id' })];
        _job_title_decorators = [(0, typeorm_1.Column)({ name: 'job_title', length: 200 })];
        _description_decorators = [(0, typeorm_1.Column)({ name: 'description', type: 'text', nullable: true })];
        _location_decorators = [(0, typeorm_1.Column)({ name: 'location', length: 200, nullable: true })];
        _salary_decorators = [(0, typeorm_1.Column)({ name: 'salary', length: 100, nullable: true })];
        _job_type_decorators = [(0, typeorm_1.Column)({ name: 'job_type', length: 50, nullable: true })];
        _status_decorators = [(0, typeorm_1.Column)({ name: 'status', length: 20, default: 'active' })];
        _posted_date_decorators = [(0, typeorm_1.Column)({ name: 'posted_date', type: 'timestamp', default: function () { return 'CURRENT_TIMESTAMP'; } })];
        _save_count_decorators = [(0, typeorm_1.Column)({ name: 'save_count', default: 0 })];
        _company_decorators = [(0, typeorm_1.ManyToOne)(function () { return company_entity_1.Company; }, function (company) { return company.job_posts; }, { onDelete: 'CASCADE' }), (0, typeorm_1.JoinColumn)({ name: 'company_id' })];
        _applications_decorators = [(0, typeorm_1.OneToMany)(function () { return application_entity_1.Application; }, function (application) { return application.job_post; })];
        _saved_by_users_decorators = [(0, typeorm_1.OneToMany)(function () { return saved_job_entity_1.SavedJob; }, function (savedJob) { return savedJob.job_post; })];
        __esDecorate(null, null, _job_id_decorators, { kind: "field", name: "job_id", static: false, private: false, access: { has: function (obj) { return "job_id" in obj; }, get: function (obj) { return obj.job_id; }, set: function (obj, value) { obj.job_id = value; } }, metadata: _metadata }, _job_id_initializers, _job_id_extraInitializers);
        __esDecorate(null, null, _company_id_decorators, { kind: "field", name: "company_id", static: false, private: false, access: { has: function (obj) { return "company_id" in obj; }, get: function (obj) { return obj.company_id; }, set: function (obj, value) { obj.company_id = value; } }, metadata: _metadata }, _company_id_initializers, _company_id_extraInitializers);
        __esDecorate(null, null, _job_title_decorators, { kind: "field", name: "job_title", static: false, private: false, access: { has: function (obj) { return "job_title" in obj; }, get: function (obj) { return obj.job_title; }, set: function (obj, value) { obj.job_title = value; } }, metadata: _metadata }, _job_title_initializers, _job_title_extraInitializers);
        __esDecorate(null, null, _description_decorators, { kind: "field", name: "description", static: false, private: false, access: { has: function (obj) { return "description" in obj; }, get: function (obj) { return obj.description; }, set: function (obj, value) { obj.description = value; } }, metadata: _metadata }, _description_initializers, _description_extraInitializers);
        __esDecorate(null, null, _location_decorators, { kind: "field", name: "location", static: false, private: false, access: { has: function (obj) { return "location" in obj; }, get: function (obj) { return obj.location; }, set: function (obj, value) { obj.location = value; } }, metadata: _metadata }, _location_initializers, _location_extraInitializers);
        __esDecorate(null, null, _salary_decorators, { kind: "field", name: "salary", static: false, private: false, access: { has: function (obj) { return "salary" in obj; }, get: function (obj) { return obj.salary; }, set: function (obj, value) { obj.salary = value; } }, metadata: _metadata }, _salary_initializers, _salary_extraInitializers);
        __esDecorate(null, null, _job_type_decorators, { kind: "field", name: "job_type", static: false, private: false, access: { has: function (obj) { return "job_type" in obj; }, get: function (obj) { return obj.job_type; }, set: function (obj, value) { obj.job_type = value; } }, metadata: _metadata }, _job_type_initializers, _job_type_extraInitializers);
        __esDecorate(null, null, _status_decorators, { kind: "field", name: "status", static: false, private: false, access: { has: function (obj) { return "status" in obj; }, get: function (obj) { return obj.status; }, set: function (obj, value) { obj.status = value; } }, metadata: _metadata }, _status_initializers, _status_extraInitializers);
        __esDecorate(null, null, _posted_date_decorators, { kind: "field", name: "posted_date", static: false, private: false, access: { has: function (obj) { return "posted_date" in obj; }, get: function (obj) { return obj.posted_date; }, set: function (obj, value) { obj.posted_date = value; } }, metadata: _metadata }, _posted_date_initializers, _posted_date_extraInitializers);
        __esDecorate(null, null, _save_count_decorators, { kind: "field", name: "save_count", static: false, private: false, access: { has: function (obj) { return "save_count" in obj; }, get: function (obj) { return obj.save_count; }, set: function (obj, value) { obj.save_count = value; } }, metadata: _metadata }, _save_count_initializers, _save_count_extraInitializers);
        __esDecorate(null, null, _company_decorators, { kind: "field", name: "company", static: false, private: false, access: { has: function (obj) { return "company" in obj; }, get: function (obj) { return obj.company; }, set: function (obj, value) { obj.company = value; } }, metadata: _metadata }, _company_initializers, _company_extraInitializers);
        __esDecorate(null, null, _applications_decorators, { kind: "field", name: "applications", static: false, private: false, access: { has: function (obj) { return "applications" in obj; }, get: function (obj) { return obj.applications; }, set: function (obj, value) { obj.applications = value; } }, metadata: _metadata }, _applications_initializers, _applications_extraInitializers);
        __esDecorate(null, null, _saved_by_users_decorators, { kind: "field", name: "saved_by_users", static: false, private: false, access: { has: function (obj) { return "saved_by_users" in obj; }, get: function (obj) { return obj.saved_by_users; }, set: function (obj, value) { obj.saved_by_users = value; } }, metadata: _metadata }, _saved_by_users_initializers, _saved_by_users_extraInitializers);
        __esDecorate(null, _classDescriptor = { value: _classThis }, _classDecorators, { kind: "class", name: _classThis.name, metadata: _metadata }, null, _classExtraInitializers);
        JobPost = _classThis = _classDescriptor.value;
        if (_metadata) Object.defineProperty(_classThis, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
        __runInitializers(_classThis, _classExtraInitializers);
    })();
    return JobPost = _classThis;
}();
exports.JobPost = JobPost;
