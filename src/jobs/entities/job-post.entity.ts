import { <PERSON><PERSON><PERSON>, Column, ManyToOne, OneToMany, Jo<PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm';
import { BaseEntity } from '../../common/entities/base.entity';
import { Company } from '../../companies/entities/company.entity';
import { Application } from '../../applications/entities/application.entity';
import { SavedJob } from '../../saved-jobs/entities/saved-job.entity';

@Entity('job_posts')
export class JobPost extends BaseEntity {
  @Column({ name: 'job_id' })
  job_id: number;

  @Column({ name: 'company_id' })
  company_id: number;

  @Column({ name: 'job_title', length: 200 })
  job_title: string;

  @Column({ name: 'description', type: 'text', nullable: true })
  description: string;

  @Column({ name: 'location', length: 200, nullable: true })
  location: string;

  @Column({ name: 'salary', length: 100, nullable: true })
  salary: string;

  @Column({ name: 'job_type', length: 50, nullable: true })
  job_type: string;

  @Column({ name: 'status', length: 20, default: 'active' })
  status: string;

  @Column({ name: 'posted_date', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  posted_date: Date;

  @Column({ name: 'save_count', default: 0 })
  save_count: number;

  // Relations
  @ManyToOne(() => Company, (company) => company.job_posts, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'company_id' })
  company: Company;

  @OneToMany(() => Application, (application) => application.job_post)
  applications: Application[];

  @OneToMany(() => SavedJob, (savedJob) => savedJob.job_post)
  saved_by_users: SavedJob[];
}
