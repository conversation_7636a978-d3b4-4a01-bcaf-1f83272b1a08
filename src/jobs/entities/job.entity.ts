import { Entity, PrimaryGeneratedColumn, Column, ManyToOne, OneToMany, CreateDateColumn, UpdateDateColumn, JoinColumn } from 'typeorm';
import { Company } from '../../companies/entities/company.entity';

export enum JobType {
  FULL_TIME = 'full_time',
  PART_TIME = 'part_time',
  CONTRACT = 'contract',
  INTERNSHIP = 'internship',
  FREELANCE = 'freelance',
}

export enum JobLevel {
  ENTRY = 'entry',
  JUNIOR = 'junior',
  MIDDLE = 'middle',
  SENIOR = 'senior',
  LEAD = 'lead',
  MANAGER = 'manager',
  DIRECTOR = 'director',
}

export enum JobStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  CLOSED = 'closed',
  DRAFT = 'draft',
}

@Entity('jobs')
export class Job {
  @PrimaryGeneratedColumn()
  @Column({ name: 'job_id' })
  job_id: number;

  @Column({ name: 'title', length: 255 })
  title: string;

  @Column({ name: 'description', type: 'text' })
  description: string;

  @Column({ name: 'requirements', type: 'text', nullable: true })
  requirements?: string;

  @Column({ name: 'benefits', type: 'text', nullable: true })
  benefits?: string;

  @Column({ name: 'salary_min', type: 'decimal', precision: 10, scale: 2, nullable: true })
  salary_min?: number;

  @Column({ name: 'salary_max', type: 'decimal', precision: 10, scale: 2, nullable: true })
  salary_max?: number;

  @Column({ name: 'currency', length: 10, default: 'USD' })
  currency: string;

  @Column({
    name: 'job_type',
    type: 'enum',
    enum: JobType,
    default: JobType.FULL_TIME,
  })
  job_type: JobType;

  @Column({
    name: 'job_level',
    type: 'enum',
    enum: JobLevel,
    default: JobLevel.ENTRY,
  })
  job_level: JobLevel;

  @Column({
    name: 'status',
    type: 'enum',
    enum: JobStatus,
    default: JobStatus.ACTIVE,
  })
  status: JobStatus;

  @Column({ name: 'location', length: 255, nullable: true })
  location?: string;

  @Column({ name: 'remote_allowed', type: 'boolean', default: false })
  remote_allowed: boolean;

  @Column({ name: 'skills', type: 'text', nullable: true })
  skills?: string;

  @Column({ name: 'experience_years', type: 'int', nullable: true })
  experience_years?: number;

  @Column({ name: 'deadline', type: 'date', nullable: true })
  deadline?: Date;

  @Column({ name: 'views_count', type: 'int', default: 0 })
  views_count: number;

  @Column({ name: 'applications_count', type: 'int', default: 0 })
  applications_count: number;

  @CreateDateColumn({ name: 'created_at' })
  created_at: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updated_at: Date;

  // Relations
  @Column({ name: 'company_id' })
  company_id: number;

  @ManyToOne(() => Company, { eager: true })
  @JoinColumn({ name: 'company_id' })
  company: Company;

  // @OneToMany(() => Application, (application) => application.job)
  // applications: Application[];

  // @OneToMany(() => SavedJob, (savedJob) => savedJob.job)
  // saved_by: SavedJob[];
}
