"use strict";
var __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {
    var useValue = arguments.length > 2;
    for (var i = 0; i < initializers.length; i++) {
        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
    }
    return useValue ? value : void 0;
};
var __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
    function accept(f) { if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected"); return f; }
    var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
    var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
    var _, done = false;
    for (var i = decorators.length - 1; i >= 0; i--) {
        var context = {};
        for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
        for (var p in contextIn.access) context.access[p] = contextIn.access[p];
        context.addInitializer = function (f) { if (done) throw new TypeError("Cannot add initializers after decoration has completed"); extraInitializers.push(accept(f || null)); };
        var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
        if (kind === "accessor") {
            if (result === void 0) continue;
            if (result === null || typeof result !== "object") throw new TypeError("Object expected");
            if (_ = accept(result.get)) descriptor.get = _;
            if (_ = accept(result.set)) descriptor.set = _;
            if (_ = accept(result.init)) initializers.unshift(_);
        }
        else if (_ = accept(result)) {
            if (kind === "field") initializers.unshift(_);
            else descriptor[key] = _;
        }
    }
    if (target) Object.defineProperty(target, contextIn.name, descriptor);
    done = true;
};
var __setFunctionName = (this && this.__setFunctionName) || function (f, name, prefix) {
    if (typeof name === "symbol") name = name.description ? "[".concat(name.description, "]") : "";
    return Object.defineProperty(f, "name", { configurable: true, value: prefix ? "".concat(prefix, " ", name) : name });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JobsController = void 0;
var openapi = require("@nestjs/swagger");
var common_1 = require("@nestjs/common");
var swagger_1 = require("@nestjs/swagger");
var response_message_decorator_1 = require("../common/decorators/response-message.decorator");
var public_decorator_1 = require("../common/decorators/public.decorator");
var JobsController = function () {
    var _classDecorators = [(0, swagger_1.ApiTags)('jobs'), (0, swagger_1.ApiBearerAuth)('access-token'), (0, common_1.Controller)('jobs')];
    var _classDescriptor;
    var _classExtraInitializers = [];
    var _classThis;
    var _instanceExtraInitializers = [];
    var _create_decorators;
    var _findAll_decorators;
    var _findOne_decorators;
    var _getStats_decorators;
    var _findByCompany_decorators;
    var _update_decorators;
    var _remove_decorators;
    var JobsController = _classThis = /** @class */ (function () {
        function JobsController_1(jobsService) {
            this.jobsService = (__runInitializers(this, _instanceExtraInitializers), jobsService);
        }
        JobsController_1.prototype.create = function (createJobPostDto) {
            return this.jobsService.create(createJobPostDto);
        };
        JobsController_1.prototype.findAll = function (search, location, job_type, company_id, status) {
            return this.jobsService.findAll({
                search: search,
                location: location,
                job_type: job_type,
                company_id: company_id,
                status: status,
            });
        };
        JobsController_1.prototype.findOne = function (id) {
            return this.jobsService.findOne(id);
        };
        JobsController_1.prototype.getStats = function (id) {
            return this.jobsService.getJobStats(id);
        };
        JobsController_1.prototype.findByCompany = function (companyId) {
            return this.jobsService.findByCompany(companyId);
        };
        JobsController_1.prototype.update = function (id, updateJobPostDto) {
            return this.jobsService.update(id, updateJobPostDto);
        };
        JobsController_1.prototype.remove = function (id) {
            return this.jobsService.remove(id);
        };
        return JobsController_1;
    }());
    __setFunctionName(_classThis, "JobsController");
    (function () {
        var _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(null) : void 0;
        _create_decorators = [(0, common_1.Post)(), (0, swagger_1.ApiOperation)({ summary: 'Create a new job post' }), (0, swagger_1.ApiResponse)({ status: 201, description: 'Job post created successfully' }), (0, response_message_decorator_1.ResponseMessage)('Job post created successfully'), openapi.ApiResponse({ status: 201, type: require("./entities/job-post.entity").JobPost })];
        _findAll_decorators = [(0, public_decorator_1.Public)(), (0, common_1.Get)(), (0, swagger_1.ApiOperation)({ summary: 'Get all job posts' }), (0, swagger_1.ApiQuery)({ name: 'search', required: false, description: 'Search jobs by title or description' }), (0, swagger_1.ApiQuery)({ name: 'location', required: false, description: 'Filter by location' }), (0, swagger_1.ApiQuery)({ name: 'job_type', required: false, description: 'Filter by job type' }), (0, swagger_1.ApiQuery)({ name: 'company_id', required: false, description: 'Filter by company ID' }), (0, swagger_1.ApiQuery)({ name: 'status', required: false, description: 'Filter by status' }), (0, swagger_1.ApiResponse)({ status: 200, description: 'Job posts retrieved successfully' }), (0, response_message_decorator_1.ResponseMessage)('Job posts retrieved successfully'), openapi.ApiResponse({ status: 200, type: [require("./entities/job-post.entity").JobPost] })];
        _findOne_decorators = [(0, public_decorator_1.Public)(), (0, common_1.Get)(':id'), (0, swagger_1.ApiOperation)({ summary: 'Get job post by ID' }), (0, swagger_1.ApiResponse)({ status: 200, description: 'Job post retrieved successfully' }), (0, response_message_decorator_1.ResponseMessage)('Job post retrieved successfully'), openapi.ApiResponse({ status: 200, type: require("./entities/job-post.entity").JobPost })];
        _getStats_decorators = [(0, common_1.Get)(':id/stats'), (0, swagger_1.ApiOperation)({ summary: 'Get job post statistics' }), (0, swagger_1.ApiResponse)({ status: 200, description: 'Job post statistics retrieved successfully' }), (0, response_message_decorator_1.ResponseMessage)('Job post statistics retrieved successfully'), openapi.ApiResponse({ status: 200 })];
        _findByCompany_decorators = [(0, common_1.Get)('company/:companyId'), (0, swagger_1.ApiOperation)({ summary: 'Get job posts by company' }), (0, swagger_1.ApiResponse)({ status: 200, description: 'Company job posts retrieved successfully' }), (0, response_message_decorator_1.ResponseMessage)('Company job posts retrieved successfully'), openapi.ApiResponse({ status: 200, type: [require("./entities/job-post.entity").JobPost] })];
        _update_decorators = [(0, common_1.Patch)(':id'), (0, swagger_1.ApiOperation)({ summary: 'Update job post by ID' }), (0, swagger_1.ApiResponse)({ status: 200, description: 'Job post updated successfully' }), (0, response_message_decorator_1.ResponseMessage)('Job post updated successfully'), openapi.ApiResponse({ status: 200, type: require("./entities/job-post.entity").JobPost })];
        _remove_decorators = [(0, common_1.Delete)(':id'), (0, swagger_1.ApiOperation)({ summary: 'Delete job post by ID' }), (0, swagger_1.ApiResponse)({ status: 200, description: 'Job post deleted successfully' }), (0, response_message_decorator_1.ResponseMessage)('Job post deleted successfully'), openapi.ApiResponse({ status: 200 })];
        __esDecorate(_classThis, null, _create_decorators, { kind: "method", name: "create", static: false, private: false, access: { has: function (obj) { return "create" in obj; }, get: function (obj) { return obj.create; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _findAll_decorators, { kind: "method", name: "findAll", static: false, private: false, access: { has: function (obj) { return "findAll" in obj; }, get: function (obj) { return obj.findAll; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _findOne_decorators, { kind: "method", name: "findOne", static: false, private: false, access: { has: function (obj) { return "findOne" in obj; }, get: function (obj) { return obj.findOne; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _getStats_decorators, { kind: "method", name: "getStats", static: false, private: false, access: { has: function (obj) { return "getStats" in obj; }, get: function (obj) { return obj.getStats; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _findByCompany_decorators, { kind: "method", name: "findByCompany", static: false, private: false, access: { has: function (obj) { return "findByCompany" in obj; }, get: function (obj) { return obj.findByCompany; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _update_decorators, { kind: "method", name: "update", static: false, private: false, access: { has: function (obj) { return "update" in obj; }, get: function (obj) { return obj.update; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _remove_decorators, { kind: "method", name: "remove", static: false, private: false, access: { has: function (obj) { return "remove" in obj; }, get: function (obj) { return obj.remove; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(null, _classDescriptor = { value: _classThis }, _classDecorators, { kind: "class", name: _classThis.name, metadata: _metadata }, null, _classExtraInitializers);
        JobsController = _classThis = _classDescriptor.value;
        if (_metadata) Object.defineProperty(_classThis, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
        __runInitializers(_classThis, _classExtraInitializers);
    })();
    return JobsController = _classThis;
}();
exports.JobsController = JobsController;
