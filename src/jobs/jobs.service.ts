import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { JobPost } from './entities/job-post.entity';
import { CreateJobPostDto } from './dto/create-job-post.dto';
import { UpdateJobPostDto } from './dto/update-job-post.dto';
import { CompaniesService } from '../companies/companies.service';

@Injectable()
export class JobsService {
  constructor(
    @InjectRepository(JobPost)
    private jobPostRepository: Repository<JobPost>,
    private companiesService: CompaniesService,
  ) {}

  async create(createJobPostDto: CreateJobPostDto): Promise<JobPost> {
    // Verify company exists
    await this.companiesService.findOne(createJobPostDto.company_id);

    const jobPost = this.jobPostRepository.create({
      ...createJobPostDto,
      job_id: Math.floor(Math.random() * 1000000), // Temporary ID generation
      status: createJobPostDto.status || 'active',
    });

    return this.jobPostRepository.save(jobPost);
  }

  async findAll(filters?: {
    search?: string;
    location?: string;
    job_type?: string;
    company_id?: number;
    status?: string;
  }): Promise<JobPost[]> {
    const queryBuilder = this.jobPostRepository.createQueryBuilder('job_post')
      .leftJoinAndSelect('job_post.company', 'company')
      .leftJoinAndSelect('job_post.applications', 'applications')
      .leftJoinAndSelect('job_post.saved_by_users', 'saved_by_users');

    if (filters?.search) {
      queryBuilder.andWhere(
        'job_post.job_title ILIKE :search OR job_post.description ILIKE :search',
        { search: `%${filters.search}%` },
      );
    }

    if (filters?.location) {
      queryBuilder.andWhere('job_post.location ILIKE :location', {
        location: `%${filters.location}%`,
      });
    }

    if (filters?.job_type) {
      queryBuilder.andWhere('job_post.job_type = :job_type', {
        job_type: filters.job_type,
      });
    }

    if (filters?.company_id) {
      queryBuilder.andWhere('job_post.company_id = :company_id', {
        company_id: filters.company_id,
      });
    }

    if (filters?.status) {
      queryBuilder.andWhere('job_post.status = :status', {
        status: filters.status,
      });
    } else {
      // Default to active jobs only
      queryBuilder.andWhere('job_post.status = :status', { status: 'active' });
    }

    return queryBuilder
      .orderBy('job_post.posted_date', 'DESC')
      .getMany();
  }

  async findOne(id: number): Promise<JobPost> {
    const jobPost = await this.jobPostRepository.findOne({
      where: { id },
      relations: ['company', 'applications', 'saved_by_users'],
    });

    if (!jobPost) {
      throw new NotFoundException('Job post not found');
    }

    return jobPost;
  }

  async findByCompany(companyId: number): Promise<JobPost[]> {
    return this.jobPostRepository.find({
      where: { company_id: companyId },
      relations: ['company', 'applications'],
      order: { posted_date: 'DESC' },
    });
  }

  async update(id: number, updateJobPostDto: UpdateJobPostDto): Promise<JobPost> {
    const jobPost = await this.findOne(id);

    if (updateJobPostDto.company_id && updateJobPostDto.company_id !== jobPost.company_id) {
      // Verify new company exists
      await this.companiesService.findOne(updateJobPostDto.company_id);
    }

    await this.jobPostRepository.update(id, updateJobPostDto);
    return this.findOne(id);
  }

  async remove(id: number): Promise<void> {
    const jobPost = await this.findOne(id);
    await this.jobPostRepository.remove(jobPost);
  }

  async incrementSaveCount(id: number): Promise<void> {
    await this.jobPostRepository.increment({ id }, 'save_count', 1);
  }

  async decrementSaveCount(id: number): Promise<void> {
    await this.jobPostRepository.decrement({ id }, 'save_count', 1);
  }

  async getJobStats(id: number) {
    const jobPost = await this.findOne(id);
    
    const applicationCount = await this.jobPostRepository
      .createQueryBuilder('job_post')
      .leftJoin('job_post.applications', 'applications')
      .where('job_post.id = :id', { id })
      .getCount();

    const savedCount = await this.jobPostRepository
      .createQueryBuilder('job_post')
      .leftJoin('job_post.saved_by_users', 'saved_by_users')
      .where('job_post.id = :id', { id })
      .getCount();

    return {
      jobPost,
      stats: {
        applications: applicationCount,
        saves: savedCount,
      },
    };
  }
}
