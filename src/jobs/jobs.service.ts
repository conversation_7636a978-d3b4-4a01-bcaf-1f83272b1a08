import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Job, JobStatus, JobType, JobLevel } from './entities/job.entity';
import { CreateJobDto } from './dto/create-job.dto';
import { UpdateJobDto } from './dto/update-job.dto';
import { CompaniesService } from '../companies/companies.service';

@Injectable()
export class JobsService {
  constructor(
    @InjectRepository(Job)
    private readonly jobRepository: Repository<Job>,
    private readonly companiesService: CompaniesService,
  ) {}

  async create(createJobDto: CreateJobDto): Promise<Job> {
    // Verify company exists
    await this.companiesService.findOne(createJobDto.company_id);

    // Validate deadline if provided
    if (createJobDto.deadline) {
      const deadline = new Date(createJobDto.deadline);
      if (deadline <= new Date()) {
        throw new BadRequestException('Deadline must be in the future');
      }
    }

    // Create job
    const job = this.jobRepository.create({
      ...createJobDto,
      deadline: createJobDto.deadline ? new Date(createJobDto.deadline) : undefined,
    });

    return this.jobRepository.save(job);
  }

  async findAll(): Promise<Job[]> {
    return this.jobRepository.find({
      where: { status: JobStatus.ACTIVE },
      relations: ['company'],
      order: { created_at: 'DESC' },
    });
  }

  async findOne(id: number): Promise<Job> {
    const job = await this.jobRepository.findOne({
      where: { job_id: id },
      relations: ['company'],
    });

    if (!job) {
      throw new NotFoundException(`Job with ID ${id} not found`);
    }

    // Increment view count
    await this.jobRepository.increment({ job_id: id }, 'views_count', 1);

    return job;
  }

  async update(id: number, updateJobDto: UpdateJobDto): Promise<Job> {
    const job = await this.findOne(id);

    // Validate deadline if provided
    if (updateJobDto.deadline) {
      const deadline = new Date(updateJobDto.deadline);
      if (deadline <= new Date()) {
        throw new BadRequestException('Deadline must be in the future');
      }
    }

    // Verify company exists if company_id is being updated
    if (updateJobDto.company_id) {
      await this.companiesService.findOne(updateJobDto.company_id);
    }

    Object.assign(job, {
      ...updateJobDto,
      deadline: updateJobDto.deadline ? new Date(updateJobDto.deadline) : job.deadline,
    });

    return this.jobRepository.save(job);
  }

  async remove(id: number): Promise<void> {
    const job = await this.findOne(id);
    await this.jobRepository.remove(job);
  }

  async findByCompany(companyId: number): Promise<Job[]> {
    return this.jobRepository.find({
      where: { company_id: companyId, status: JobStatus.ACTIVE },
      relations: ['company'],
      order: { created_at: 'DESC' },
    });
  }

  async findByTitle(title: string): Promise<Job[]> {
    return this.jobRepository
      .createQueryBuilder('job')
      .leftJoinAndSelect('job.company', 'company')
      .where('job.title ILIKE :title', { title: `%${title}%` })
      .andWhere('job.status = :status', { status: JobStatus.ACTIVE })
      .orderBy('job.created_at', 'DESC')
      .getMany();
  }

  async findByLocation(location: string): Promise<Job[]> {
    return this.jobRepository.find({
      where: { location, status: JobStatus.ACTIVE },
      relations: ['company'],
      order: { created_at: 'DESC' },
    });
  }

  async findByType(jobType: JobType): Promise<Job[]> {
    return this.jobRepository.find({
      where: { job_type: jobType, status: JobStatus.ACTIVE },
      relations: ['company'],
      order: { created_at: 'DESC' },
    });
  }

  async findByLevel(jobLevel: JobLevel): Promise<Job[]> {
    return this.jobRepository.find({
      where: { job_level: jobLevel, status: JobStatus.ACTIVE },
      relations: ['company'],
      order: { created_at: 'DESC' },
    });
  }

  async findRemoteJobs(): Promise<Job[]> {
    return this.jobRepository.find({
      where: { remote_allowed: true, status: JobStatus.ACTIVE },
      relations: ['company'],
      order: { created_at: 'DESC' },
    });
  }

  async findBySalaryRange(minSalary: number, maxSalary: number): Promise<Job[]> {
    return this.jobRepository
      .createQueryBuilder('job')
      .leftJoinAndSelect('job.company', 'company')
      .where('job.salary_min >= :minSalary', { minSalary })
      .andWhere('job.salary_max <= :maxSalary', { maxSalary })
      .andWhere('job.status = :status', { status: JobStatus.ACTIVE })
      .orderBy('job.created_at', 'DESC')
      .getMany();
  }

  async searchJobs(query: string): Promise<Job[]> {
    return this.jobRepository
      .createQueryBuilder('job')
      .leftJoinAndSelect('job.company', 'company')
      .where('job.title ILIKE :query', { query: `%${query}%` })
      .orWhere('job.description ILIKE :query', { query: `%${query}%` })
      .orWhere('job.skills ILIKE :query', { query: `%${query}%` })
      .andWhere('job.status = :status', { status: JobStatus.ACTIVE })
      .orderBy('job.created_at', 'DESC')
      .getMany();
  }

  async incrementApplicationsCount(jobId: number): Promise<void> {
    await this.jobRepository.increment({ job_id: jobId }, 'applications_count', 1);
  }

  async decrementApplicationsCount(jobId: number): Promise<void> {
    await this.jobRepository.decrement({ job_id: jobId }, 'applications_count', 1);
  }
}
