import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';

import { JobsService } from './jobs.service';
import { CreateJobPostDto } from './dto/create-job-post.dto';
import { UpdateJobPostDto } from './dto/update-job-post.dto';
import { ResponseMessage } from '../common/decorators/response-message.decorator';
import { Public } from '../common/decorators/public.decorator';

@ApiTags('jobs')
@ApiBearerAuth('access-token')
@Controller('jobs')
export class JobsController {
  constructor(private readonly jobsService: JobsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new job post' })
  @ApiResponse({ status: 201, description: 'Job post created successfully' })
  @ResponseMessage('Job post created successfully')
  create(@Body() createJobPostDto: CreateJobPostDto) {
    return this.jobsService.create(createJobPostDto);
  }

  @Public()
  @Get()
  @ApiOperation({ summary: 'Get all job posts' })
  @ApiQuery({ name: 'search', required: false, description: 'Search jobs by title or description' })
  @ApiQuery({ name: 'location', required: false, description: 'Filter by location' })
  @ApiQuery({ name: 'job_type', required: false, description: 'Filter by job type' })
  @ApiQuery({ name: 'company_id', required: false, description: 'Filter by company ID' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by status' })
  @ApiResponse({ status: 200, description: 'Job posts retrieved successfully' })
  @ResponseMessage('Job posts retrieved successfully')
  findAll(
    @Query('search') search?: string,
    @Query('location') location?: string,
    @Query('job_type') job_type?: string,
    @Query('company_id') company_id?: number,
    @Query('status') status?: string,
  ) {
    return this.jobsService.findAll({
      search,
      location,
      job_type,
      company_id,
      status,
    });
  }

  @Public()
  @Get(':id')
  @ApiOperation({ summary: 'Get job post by ID' })
  @ApiResponse({ status: 200, description: 'Job post retrieved successfully' })
  @ResponseMessage('Job post retrieved successfully')
  findOne(@Param('id', ParseIntPipe) id: number) {
    return this.jobsService.findOne(id);
  }

  @Get(':id/stats')
  @ApiOperation({ summary: 'Get job post statistics' })
  @ApiResponse({ status: 200, description: 'Job post statistics retrieved successfully' })
  @ResponseMessage('Job post statistics retrieved successfully')
  getStats(@Param('id', ParseIntPipe) id: number) {
    return this.jobsService.getJobStats(id);
  }

  @Get('company/:companyId')
  @ApiOperation({ summary: 'Get job posts by company' })
  @ApiResponse({ status: 200, description: 'Company job posts retrieved successfully' })
  @ResponseMessage('Company job posts retrieved successfully')
  findByCompany(@Param('companyId', ParseIntPipe) companyId: number) {
    return this.jobsService.findByCompany(companyId);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update job post by ID' })
  @ApiResponse({ status: 200, description: 'Job post updated successfully' })
  @ResponseMessage('Job post updated successfully')
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateJobPostDto: UpdateJobPostDto,
  ) {
    return this.jobsService.update(id, updateJobPostDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete job post by ID' })
  @ApiResponse({ status: 200, description: 'Job post deleted successfully' })
  @ResponseMessage('Job post deleted successfully')
  remove(@Param('id', ParseIntPipe) id: number) {
    return this.jobsService.remove(id);
  }
}
