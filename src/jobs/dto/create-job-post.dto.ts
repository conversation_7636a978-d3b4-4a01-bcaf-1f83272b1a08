import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, IsNumber, IsIn } from 'class-validator';

export class CreateJobPostDto {
  @ApiProperty({ example: 1, description: 'Company ID' })
  @IsNotEmpty()
  @IsNumber()
  company_id: number;

  @ApiProperty({ example: 'Senior Software Engineer', description: 'Job title' })
  @IsNotEmpty()
  @IsString()
  job_title: string;

  @ApiProperty({ 
    example: 'We are looking for a senior software engineer with 5+ years of experience...', 
    description: 'Job description' 
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ example: 'New York, NY', description: 'Job location' })
  @IsOptional()
  @IsString()
  location?: string;

  @ApiProperty({ example: '$80,000 - $120,000', description: 'Salary range' })
  @IsOptional()
  @IsString()
  salary?: string;

  @ApiProperty({ 
    example: 'full-time', 
    description: 'Job type',
    enum: ['full-time', 'part-time', 'contract', 'internship', 'remote']
  })
  @IsOptional()
  @IsString()
  job_type?: string;

  @ApiProperty({ 
    example: 'active', 
    description: 'Job status',
    enum: ['active', 'inactive', 'closed']
  })
  @IsOptional()
  @IsIn(['active', 'inactive', 'closed'])
  status?: string;
}
