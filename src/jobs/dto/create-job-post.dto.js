"use strict";
var __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
    function accept(f) { if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected"); return f; }
    var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
    var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
    var _, done = false;
    for (var i = decorators.length - 1; i >= 0; i--) {
        var context = {};
        for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
        for (var p in contextIn.access) context.access[p] = contextIn.access[p];
        context.addInitializer = function (f) { if (done) throw new TypeError("Cannot add initializers after decoration has completed"); extraInitializers.push(accept(f || null)); };
        var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
        if (kind === "accessor") {
            if (result === void 0) continue;
            if (result === null || typeof result !== "object") throw new TypeError("Object expected");
            if (_ = accept(result.get)) descriptor.get = _;
            if (_ = accept(result.set)) descriptor.set = _;
            if (_ = accept(result.init)) initializers.unshift(_);
        }
        else if (_ = accept(result)) {
            if (kind === "field") initializers.unshift(_);
            else descriptor[key] = _;
        }
    }
    if (target) Object.defineProperty(target, contextIn.name, descriptor);
    done = true;
};
var __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {
    var useValue = arguments.length > 2;
    for (var i = 0; i < initializers.length; i++) {
        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
    }
    return useValue ? value : void 0;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateJobPostDto = void 0;
var openapi = require("@nestjs/swagger");
var swagger_1 = require("@nestjs/swagger");
var class_validator_1 = require("class-validator");
var CreateJobPostDto = function () {
    var _a;
    var _company_id_decorators;
    var _company_id_initializers = [];
    var _company_id_extraInitializers = [];
    var _job_title_decorators;
    var _job_title_initializers = [];
    var _job_title_extraInitializers = [];
    var _description_decorators;
    var _description_initializers = [];
    var _description_extraInitializers = [];
    var _location_decorators;
    var _location_initializers = [];
    var _location_extraInitializers = [];
    var _salary_decorators;
    var _salary_initializers = [];
    var _salary_extraInitializers = [];
    var _job_type_decorators;
    var _job_type_initializers = [];
    var _job_type_extraInitializers = [];
    var _status_decorators;
    var _status_initializers = [];
    var _status_extraInitializers = [];
    return _a = /** @class */ (function () {
            function CreateJobPostDto() {
                this.company_id = __runInitializers(this, _company_id_initializers, void 0);
                this.job_title = (__runInitializers(this, _company_id_extraInitializers), __runInitializers(this, _job_title_initializers, void 0));
                this.description = (__runInitializers(this, _job_title_extraInitializers), __runInitializers(this, _description_initializers, void 0));
                this.location = (__runInitializers(this, _description_extraInitializers), __runInitializers(this, _location_initializers, void 0));
                this.salary = (__runInitializers(this, _location_extraInitializers), __runInitializers(this, _salary_initializers, void 0));
                this.job_type = (__runInitializers(this, _salary_extraInitializers), __runInitializers(this, _job_type_initializers, void 0));
                this.status = (__runInitializers(this, _job_type_extraInitializers), __runInitializers(this, _status_initializers, void 0));
                __runInitializers(this, _status_extraInitializers);
            }
            CreateJobPostDto._OPENAPI_METADATA_FACTORY = function () {
                return { company_id: { required: true, type: function () { return Number; } }, job_title: { required: true, type: function () { return String; } }, description: { required: false, type: function () { return String; } }, location: { required: false, type: function () { return String; } }, salary: { required: false, type: function () { return String; } }, job_type: { required: false, type: function () { return String; } }, status: { required: false, type: function () { return String; }, enum: ['active', 'inactive', 'closed'] } };
            };
            return CreateJobPostDto;
        }()),
        (function () {
            var _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(null) : void 0;
            _company_id_decorators = [(0, swagger_1.ApiProperty)({ example: 1, description: 'Company ID' }), (0, class_validator_1.IsNotEmpty)(), (0, class_validator_1.IsNumber)()];
            _job_title_decorators = [(0, swagger_1.ApiProperty)({ example: 'Senior Software Engineer', description: 'Job title' }), (0, class_validator_1.IsNotEmpty)(), (0, class_validator_1.IsString)()];
            _description_decorators = [(0, swagger_1.ApiProperty)({
                    example: 'We are looking for a senior software engineer with 5+ years of experience...',
                    description: 'Job description'
                }), (0, class_validator_1.IsOptional)(), (0, class_validator_1.IsString)()];
            _location_decorators = [(0, swagger_1.ApiProperty)({ example: 'New York, NY', description: 'Job location' }), (0, class_validator_1.IsOptional)(), (0, class_validator_1.IsString)()];
            _salary_decorators = [(0, swagger_1.ApiProperty)({ example: '$80,000 - $120,000', description: 'Salary range' }), (0, class_validator_1.IsOptional)(), (0, class_validator_1.IsString)()];
            _job_type_decorators = [(0, swagger_1.ApiProperty)({
                    example: 'full-time',
                    description: 'Job type',
                    enum: ['full-time', 'part-time', 'contract', 'internship', 'remote']
                }), (0, class_validator_1.IsOptional)(), (0, class_validator_1.IsString)()];
            _status_decorators = [(0, swagger_1.ApiProperty)({
                    example: 'active',
                    description: 'Job status',
                    enum: ['active', 'inactive', 'closed']
                }), (0, class_validator_1.IsOptional)(), (0, class_validator_1.IsIn)(['active', 'inactive', 'closed'])];
            __esDecorate(null, null, _company_id_decorators, { kind: "field", name: "company_id", static: false, private: false, access: { has: function (obj) { return "company_id" in obj; }, get: function (obj) { return obj.company_id; }, set: function (obj, value) { obj.company_id = value; } }, metadata: _metadata }, _company_id_initializers, _company_id_extraInitializers);
            __esDecorate(null, null, _job_title_decorators, { kind: "field", name: "job_title", static: false, private: false, access: { has: function (obj) { return "job_title" in obj; }, get: function (obj) { return obj.job_title; }, set: function (obj, value) { obj.job_title = value; } }, metadata: _metadata }, _job_title_initializers, _job_title_extraInitializers);
            __esDecorate(null, null, _description_decorators, { kind: "field", name: "description", static: false, private: false, access: { has: function (obj) { return "description" in obj; }, get: function (obj) { return obj.description; }, set: function (obj, value) { obj.description = value; } }, metadata: _metadata }, _description_initializers, _description_extraInitializers);
            __esDecorate(null, null, _location_decorators, { kind: "field", name: "location", static: false, private: false, access: { has: function (obj) { return "location" in obj; }, get: function (obj) { return obj.location; }, set: function (obj, value) { obj.location = value; } }, metadata: _metadata }, _location_initializers, _location_extraInitializers);
            __esDecorate(null, null, _salary_decorators, { kind: "field", name: "salary", static: false, private: false, access: { has: function (obj) { return "salary" in obj; }, get: function (obj) { return obj.salary; }, set: function (obj, value) { obj.salary = value; } }, metadata: _metadata }, _salary_initializers, _salary_extraInitializers);
            __esDecorate(null, null, _job_type_decorators, { kind: "field", name: "job_type", static: false, private: false, access: { has: function (obj) { return "job_type" in obj; }, get: function (obj) { return obj.job_type; }, set: function (obj, value) { obj.job_type = value; } }, metadata: _metadata }, _job_type_initializers, _job_type_extraInitializers);
            __esDecorate(null, null, _status_decorators, { kind: "field", name: "status", static: false, private: false, access: { has: function (obj) { return "status" in obj; }, get: function (obj) { return obj.status; }, set: function (obj, value) { obj.status = value; } }, metadata: _metadata }, _status_initializers, _status_extraInitializers);
            if (_metadata) Object.defineProperty(_a, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
        })(),
        _a;
}();
exports.CreateJobPostDto = CreateJobPostDto;
