import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { Interview } from './entities/interview.entity';
import { CreateInterviewDto } from './dto/create-interview.dto';
import { UpdateInterviewDto } from './dto/update-interview.dto';
import { ApplicationsService } from '../applications/applications.service';

@Injectable()
export class InterviewsService {
  constructor(
    @InjectRepository(Interview)
    private interviewRepository: Repository<Interview>,
    private applicationsService: ApplicationsService,
  ) {}

  async create(createInterviewDto: CreateInterviewDto): Promise<Interview> {
    // Verify application exists
    await this.applicationsService.findOne(createInterviewDto.application_id);

    const interview = this.interviewRepository.create({
      ...createInterviewDto,
      interview_id: Math.floor(Math.random() * 1000000),
      interview_time: createInterviewDto.interview_time ? new Date(createInterviewDto.interview_time) : null,
    });

    return this.interviewRepository.save(interview);
  }

  async findAll(): Promise<Interview[]> {
    return this.interviewRepository.find({
      relations: ['application', 'application.job_post', 'application.resume', 'application.resume.user'],
      order: { interview_time: 'ASC' },
    });
  }

  async findByApplication(applicationId: number): Promise<Interview[]> {
    return this.interviewRepository.find({
      where: { application_id: applicationId },
      relations: ['application'],
      order: { interview_time: 'ASC' },
    });
  }

  async findOne(id: number): Promise<Interview> {
    const interview = await this.interviewRepository.findOne({
      where: { id },
      relations: ['application', 'application.job_post', 'application.resume', 'application.resume.user'],
    });

    if (!interview) {
      throw new NotFoundException('Interview not found');
    }

    return interview;
  }

  async update(id: number, updateInterviewDto: UpdateInterviewDto): Promise<Interview> {
    const interview = await this.findOne(id);

    if (updateInterviewDto.application_id && updateInterviewDto.application_id !== interview.application_id) {
      await this.applicationsService.findOne(updateInterviewDto.application_id);
    }

    const updateData = {
      ...updateInterviewDto,
      interview_time: updateInterviewDto.interview_time ? new Date(updateInterviewDto.interview_time) : undefined,
    };

    await this.interviewRepository.update(id, updateData);
    return this.findOne(id);
  }

  async remove(id: number): Promise<void> {
    const interview = await this.findOne(id);
    await this.interviewRepository.remove(interview);
  }
}
