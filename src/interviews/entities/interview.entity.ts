import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { BaseEntity } from '../../common/entities/base.entity';
import { Application } from '../../applications/entities/application.entity';

@Entity('interviews')
export class Interview extends BaseEntity {
  @Column({ name: 'interview_id' })
  interview_id: number;

  @Column({ name: 'application_id' })
  application_id: number;

  @Column({ name: 'interview_time', type: 'timestamp', nullable: true })
  interview_time: Date;

  @Column({ name: 'interview_type', length: 50, nullable: true })
  interview_type: string;

  @Column({ name: 'notes', type: 'text', nullable: true })
  notes: string;

  // Relations
  @ManyToOne(() => Application, (application) => application.interviews, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'application_id' })
  application: Application;
}
