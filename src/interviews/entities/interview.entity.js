"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
    function accept(f) { if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected"); return f; }
    var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
    var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
    var _, done = false;
    for (var i = decorators.length - 1; i >= 0; i--) {
        var context = {};
        for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
        for (var p in contextIn.access) context.access[p] = contextIn.access[p];
        context.addInitializer = function (f) { if (done) throw new TypeError("Cannot add initializers after decoration has completed"); extraInitializers.push(accept(f || null)); };
        var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
        if (kind === "accessor") {
            if (result === void 0) continue;
            if (result === null || typeof result !== "object") throw new TypeError("Object expected");
            if (_ = accept(result.get)) descriptor.get = _;
            if (_ = accept(result.set)) descriptor.set = _;
            if (_ = accept(result.init)) initializers.unshift(_);
        }
        else if (_ = accept(result)) {
            if (kind === "field") initializers.unshift(_);
            else descriptor[key] = _;
        }
    }
    if (target) Object.defineProperty(target, contextIn.name, descriptor);
    done = true;
};
var __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {
    var useValue = arguments.length > 2;
    for (var i = 0; i < initializers.length; i++) {
        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
    }
    return useValue ? value : void 0;
};
var __setFunctionName = (this && this.__setFunctionName) || function (f, name, prefix) {
    if (typeof name === "symbol") name = name.description ? "[".concat(name.description, "]") : "";
    return Object.defineProperty(f, "name", { configurable: true, value: prefix ? "".concat(prefix, " ", name) : name });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Interview = void 0;
var openapi = require("@nestjs/swagger");
var typeorm_1 = require("typeorm");
var base_entity_1 = require("../../common/entities/base.entity");
var application_entity_1 = require("../../applications/entities/application.entity");
var Interview = function () {
    var _classDecorators = [(0, typeorm_1.Entity)('interviews')];
    var _classDescriptor;
    var _classExtraInitializers = [];
    var _classThis;
    var _classSuper = base_entity_1.BaseEntity;
    var _interview_id_decorators;
    var _interview_id_initializers = [];
    var _interview_id_extraInitializers = [];
    var _application_id_decorators;
    var _application_id_initializers = [];
    var _application_id_extraInitializers = [];
    var _interview_time_decorators;
    var _interview_time_initializers = [];
    var _interview_time_extraInitializers = [];
    var _interview_type_decorators;
    var _interview_type_initializers = [];
    var _interview_type_extraInitializers = [];
    var _notes_decorators;
    var _notes_initializers = [];
    var _notes_extraInitializers = [];
    var _application_decorators;
    var _application_initializers = [];
    var _application_extraInitializers = [];
    var Interview = _classThis = /** @class */ (function (_super) {
        __extends(Interview_1, _super);
        function Interview_1() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.interview_id = __runInitializers(_this, _interview_id_initializers, void 0);
            _this.application_id = (__runInitializers(_this, _interview_id_extraInitializers), __runInitializers(_this, _application_id_initializers, void 0));
            _this.interview_time = (__runInitializers(_this, _application_id_extraInitializers), __runInitializers(_this, _interview_time_initializers, void 0));
            _this.interview_type = (__runInitializers(_this, _interview_time_extraInitializers), __runInitializers(_this, _interview_type_initializers, void 0));
            _this.notes = (__runInitializers(_this, _interview_type_extraInitializers), __runInitializers(_this, _notes_initializers, void 0));
            // Relations
            _this.application = (__runInitializers(_this, _notes_extraInitializers), __runInitializers(_this, _application_initializers, void 0));
            __runInitializers(_this, _application_extraInitializers);
            return _this;
        }
        Interview_1._OPENAPI_METADATA_FACTORY = function () {
            return { interview_id: { required: true, type: function () { return Number; } }, application_id: { required: true, type: function () { return Number; } }, interview_time: { required: true, type: function () { return Date; } }, interview_type: { required: true, type: function () { return String; } }, notes: { required: true, type: function () { return String; } }, application: { required: true, type: function () { return require("../../applications/entities/application.entity").Application; } } };
        };
        return Interview_1;
    }(_classSuper));
    __setFunctionName(_classThis, "Interview");
    (function () {
        var _a;
        var _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create((_a = _classSuper[Symbol.metadata]) !== null && _a !== void 0 ? _a : null) : void 0;
        _interview_id_decorators = [(0, typeorm_1.Column)({ name: 'interview_id' })];
        _application_id_decorators = [(0, typeorm_1.Column)({ name: 'application_id' })];
        _interview_time_decorators = [(0, typeorm_1.Column)({ name: 'interview_time', type: 'timestamp', nullable: true })];
        _interview_type_decorators = [(0, typeorm_1.Column)({ name: 'interview_type', length: 50, nullable: true })];
        _notes_decorators = [(0, typeorm_1.Column)({ name: 'notes', type: 'text', nullable: true })];
        _application_decorators = [(0, typeorm_1.ManyToOne)(function () { return application_entity_1.Application; }, function (application) { return application.interviews; }, { onDelete: 'CASCADE' }), (0, typeorm_1.JoinColumn)({ name: 'application_id' })];
        __esDecorate(null, null, _interview_id_decorators, { kind: "field", name: "interview_id", static: false, private: false, access: { has: function (obj) { return "interview_id" in obj; }, get: function (obj) { return obj.interview_id; }, set: function (obj, value) { obj.interview_id = value; } }, metadata: _metadata }, _interview_id_initializers, _interview_id_extraInitializers);
        __esDecorate(null, null, _application_id_decorators, { kind: "field", name: "application_id", static: false, private: false, access: { has: function (obj) { return "application_id" in obj; }, get: function (obj) { return obj.application_id; }, set: function (obj, value) { obj.application_id = value; } }, metadata: _metadata }, _application_id_initializers, _application_id_extraInitializers);
        __esDecorate(null, null, _interview_time_decorators, { kind: "field", name: "interview_time", static: false, private: false, access: { has: function (obj) { return "interview_time" in obj; }, get: function (obj) { return obj.interview_time; }, set: function (obj, value) { obj.interview_time = value; } }, metadata: _metadata }, _interview_time_initializers, _interview_time_extraInitializers);
        __esDecorate(null, null, _interview_type_decorators, { kind: "field", name: "interview_type", static: false, private: false, access: { has: function (obj) { return "interview_type" in obj; }, get: function (obj) { return obj.interview_type; }, set: function (obj, value) { obj.interview_type = value; } }, metadata: _metadata }, _interview_type_initializers, _interview_type_extraInitializers);
        __esDecorate(null, null, _notes_decorators, { kind: "field", name: "notes", static: false, private: false, access: { has: function (obj) { return "notes" in obj; }, get: function (obj) { return obj.notes; }, set: function (obj, value) { obj.notes = value; } }, metadata: _metadata }, _notes_initializers, _notes_extraInitializers);
        __esDecorate(null, null, _application_decorators, { kind: "field", name: "application", static: false, private: false, access: { has: function (obj) { return "application" in obj; }, get: function (obj) { return obj.application; }, set: function (obj, value) { obj.application = value; } }, metadata: _metadata }, _application_initializers, _application_extraInitializers);
        __esDecorate(null, _classDescriptor = { value: _classThis }, _classDecorators, { kind: "class", name: _classThis.name, metadata: _metadata }, null, _classExtraInitializers);
        Interview = _classThis = _classDescriptor.value;
        if (_metadata) Object.defineProperty(_classThis, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
        __runInitializers(_classThis, _classExtraInitializers);
    })();
    return Interview = _classThis;
}();
exports.Interview = Interview;
