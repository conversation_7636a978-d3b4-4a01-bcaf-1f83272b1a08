"use strict";
var __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {
    var useValue = arguments.length > 2;
    for (var i = 0; i < initializers.length; i++) {
        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
    }
    return useValue ? value : void 0;
};
var __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
    function accept(f) { if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected"); return f; }
    var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
    var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
    var _, done = false;
    for (var i = decorators.length - 1; i >= 0; i--) {
        var context = {};
        for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
        for (var p in contextIn.access) context.access[p] = contextIn.access[p];
        context.addInitializer = function (f) { if (done) throw new TypeError("Cannot add initializers after decoration has completed"); extraInitializers.push(accept(f || null)); };
        var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
        if (kind === "accessor") {
            if (result === void 0) continue;
            if (result === null || typeof result !== "object") throw new TypeError("Object expected");
            if (_ = accept(result.get)) descriptor.get = _;
            if (_ = accept(result.set)) descriptor.set = _;
            if (_ = accept(result.init)) initializers.unshift(_);
        }
        else if (_ = accept(result)) {
            if (kind === "field") initializers.unshift(_);
            else descriptor[key] = _;
        }
    }
    if (target) Object.defineProperty(target, contextIn.name, descriptor);
    done = true;
};
var __setFunctionName = (this && this.__setFunctionName) || function (f, name, prefix) {
    if (typeof name === "symbol") name = name.description ? "[".concat(name.description, "]") : "";
    return Object.defineProperty(f, "name", { configurable: true, value: prefix ? "".concat(prefix, " ", name) : name });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InterviewsController = void 0;
var openapi = require("@nestjs/swagger");
var common_1 = require("@nestjs/common");
var swagger_1 = require("@nestjs/swagger");
var response_message_decorator_1 = require("../common/decorators/response-message.decorator");
var InterviewsController = function () {
    var _classDecorators = [(0, swagger_1.ApiTags)('interviews'), (0, swagger_1.ApiBearerAuth)('access-token'), (0, common_1.Controller)('interviews')];
    var _classDescriptor;
    var _classExtraInitializers = [];
    var _classThis;
    var _instanceExtraInitializers = [];
    var _create_decorators;
    var _findAll_decorators;
    var _findByApplication_decorators;
    var _findOne_decorators;
    var _update_decorators;
    var _remove_decorators;
    var InterviewsController = _classThis = /** @class */ (function () {
        function InterviewsController_1(interviewsService) {
            this.interviewsService = (__runInitializers(this, _instanceExtraInitializers), interviewsService);
        }
        InterviewsController_1.prototype.create = function (createInterviewDto) {
            return this.interviewsService.create(createInterviewDto);
        };
        InterviewsController_1.prototype.findAll = function () {
            return this.interviewsService.findAll();
        };
        InterviewsController_1.prototype.findByApplication = function (applicationId) {
            return this.interviewsService.findByApplication(applicationId);
        };
        InterviewsController_1.prototype.findOne = function (id) {
            return this.interviewsService.findOne(id);
        };
        InterviewsController_1.prototype.update = function (id, updateInterviewDto) {
            return this.interviewsService.update(id, updateInterviewDto);
        };
        InterviewsController_1.prototype.remove = function (id) {
            return this.interviewsService.remove(id);
        };
        return InterviewsController_1;
    }());
    __setFunctionName(_classThis, "InterviewsController");
    (function () {
        var _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(null) : void 0;
        _create_decorators = [(0, common_1.Post)(), (0, swagger_1.ApiOperation)({ summary: 'Create a new interview' }), (0, swagger_1.ApiResponse)({ status: 201, description: 'Interview created successfully' }), (0, response_message_decorator_1.ResponseMessage)('Interview created successfully'), openapi.ApiResponse({ status: 201, type: require("./entities/interview.entity").Interview })];
        _findAll_decorators = [(0, common_1.Get)(), (0, swagger_1.ApiOperation)({ summary: 'Get all interviews' }), (0, swagger_1.ApiResponse)({ status: 200, description: 'Interviews retrieved successfully' }), (0, response_message_decorator_1.ResponseMessage)('Interviews retrieved successfully'), openapi.ApiResponse({ status: 200, type: [require("./entities/interview.entity").Interview] })];
        _findByApplication_decorators = [(0, common_1.Get)('application/:applicationId'), (0, swagger_1.ApiOperation)({ summary: 'Get interviews by application ID' }), (0, swagger_1.ApiResponse)({ status: 200, description: 'Application interviews retrieved successfully' }), (0, response_message_decorator_1.ResponseMessage)('Application interviews retrieved successfully'), openapi.ApiResponse({ status: 200, type: [require("./entities/interview.entity").Interview] })];
        _findOne_decorators = [(0, common_1.Get)(':id'), (0, swagger_1.ApiOperation)({ summary: 'Get interview by ID' }), (0, swagger_1.ApiResponse)({ status: 200, description: 'Interview retrieved successfully' }), (0, response_message_decorator_1.ResponseMessage)('Interview retrieved successfully'), openapi.ApiResponse({ status: 200, type: require("./entities/interview.entity").Interview })];
        _update_decorators = [(0, common_1.Patch)(':id'), (0, swagger_1.ApiOperation)({ summary: 'Update interview by ID' }), (0, swagger_1.ApiResponse)({ status: 200, description: 'Interview updated successfully' }), (0, response_message_decorator_1.ResponseMessage)('Interview updated successfully'), openapi.ApiResponse({ status: 200, type: require("./entities/interview.entity").Interview })];
        _remove_decorators = [(0, common_1.Delete)(':id'), (0, swagger_1.ApiOperation)({ summary: 'Delete interview by ID' }), (0, swagger_1.ApiResponse)({ status: 200, description: 'Interview deleted successfully' }), (0, response_message_decorator_1.ResponseMessage)('Interview deleted successfully'), openapi.ApiResponse({ status: 200 })];
        __esDecorate(_classThis, null, _create_decorators, { kind: "method", name: "create", static: false, private: false, access: { has: function (obj) { return "create" in obj; }, get: function (obj) { return obj.create; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _findAll_decorators, { kind: "method", name: "findAll", static: false, private: false, access: { has: function (obj) { return "findAll" in obj; }, get: function (obj) { return obj.findAll; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _findByApplication_decorators, { kind: "method", name: "findByApplication", static: false, private: false, access: { has: function (obj) { return "findByApplication" in obj; }, get: function (obj) { return obj.findByApplication; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _findOne_decorators, { kind: "method", name: "findOne", static: false, private: false, access: { has: function (obj) { return "findOne" in obj; }, get: function (obj) { return obj.findOne; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _update_decorators, { kind: "method", name: "update", static: false, private: false, access: { has: function (obj) { return "update" in obj; }, get: function (obj) { return obj.update; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _remove_decorators, { kind: "method", name: "remove", static: false, private: false, access: { has: function (obj) { return "remove" in obj; }, get: function (obj) { return obj.remove; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(null, _classDescriptor = { value: _classThis }, _classDecorators, { kind: "class", name: _classThis.name, metadata: _metadata }, null, _classExtraInitializers);
        InterviewsController = _classThis = _classDescriptor.value;
        if (_metadata) Object.defineProperty(_classThis, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
        __runInitializers(_classThis, _classExtraInitializers);
    })();
    return InterviewsController = _classThis;
}();
exports.InterviewsController = InterviewsController;
