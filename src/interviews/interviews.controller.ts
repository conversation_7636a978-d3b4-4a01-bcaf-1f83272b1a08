import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ParseIntPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';

import { InterviewsService } from './interviews.service';
import { CreateInterviewDto } from './dto/create-interview.dto';
import { UpdateInterviewDto } from './dto/update-interview.dto';
import { ResponseMessage } from '../common/decorators/response-message.decorator';

@ApiTags('interviews')
@ApiBearerAuth('access-token')
@Controller('interviews')
export class InterviewsController {
  constructor(private readonly interviewsService: InterviewsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new interview' })
  @ApiResponse({ status: 201, description: 'Interview created successfully' })
  @ResponseMessage('Interview created successfully')
  create(@Body() createInterviewDto: CreateInterviewDto) {
    return this.interviewsService.create(createInterviewDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all interviews' })
  @ApiResponse({ status: 200, description: 'Interviews retrieved successfully' })
  @ResponseMessage('Interviews retrieved successfully')
  findAll() {
    return this.interviewsService.findAll();
  }

  @Get('application/:applicationId')
  @ApiOperation({ summary: 'Get interviews by application ID' })
  @ApiResponse({ status: 200, description: 'Application interviews retrieved successfully' })
  @ResponseMessage('Application interviews retrieved successfully')
  findByApplication(@Param('applicationId', ParseIntPipe) applicationId: number) {
    return this.interviewsService.findByApplication(applicationId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get interview by ID' })
  @ApiResponse({ status: 200, description: 'Interview retrieved successfully' })
  @ResponseMessage('Interview retrieved successfully')
  findOne(@Param('id', ParseIntPipe) id: number) {
    return this.interviewsService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update interview by ID' })
  @ApiResponse({ status: 200, description: 'Interview updated successfully' })
  @ResponseMessage('Interview updated successfully')
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateInterviewDto: UpdateInterviewDto,
  ) {
    return this.interviewsService.update(id, updateInterviewDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete interview by ID' })
  @ApiResponse({ status: 200, description: 'Interview deleted successfully' })
  @ResponseMessage('Interview deleted successfully')
  remove(@Param('id', ParseIntPipe) id: number) {
    return this.interviewsService.remove(id);
  }
}
