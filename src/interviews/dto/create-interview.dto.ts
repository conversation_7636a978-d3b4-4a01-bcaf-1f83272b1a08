import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsN<PERSON>ber, IsOptional, IsString, IsDateString } from 'class-validator';

export class CreateInterviewDto {
  @ApiProperty({ example: 1, description: 'Application ID' })
  @IsNotEmpty()
  @IsNumber()
  application_id: number;

  @ApiProperty({ example: '2024-01-15T10:00:00Z', description: 'Interview time' })
  @IsOptional()
  @IsDateString()
  interview_time?: string;

  @ApiProperty({ example: 'phone', description: 'Interview type' })
  @IsOptional()
  @IsString()
  interview_type?: string;

  @ApiProperty({ example: 'Initial screening interview', description: 'Interview notes' })
  @IsOptional()
  @IsString()
  notes?: string;
}
