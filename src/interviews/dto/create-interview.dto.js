"use strict";
var __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
    function accept(f) { if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected"); return f; }
    var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
    var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
    var _, done = false;
    for (var i = decorators.length - 1; i >= 0; i--) {
        var context = {};
        for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
        for (var p in contextIn.access) context.access[p] = contextIn.access[p];
        context.addInitializer = function (f) { if (done) throw new TypeError("Cannot add initializers after decoration has completed"); extraInitializers.push(accept(f || null)); };
        var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
        if (kind === "accessor") {
            if (result === void 0) continue;
            if (result === null || typeof result !== "object") throw new TypeError("Object expected");
            if (_ = accept(result.get)) descriptor.get = _;
            if (_ = accept(result.set)) descriptor.set = _;
            if (_ = accept(result.init)) initializers.unshift(_);
        }
        else if (_ = accept(result)) {
            if (kind === "field") initializers.unshift(_);
            else descriptor[key] = _;
        }
    }
    if (target) Object.defineProperty(target, contextIn.name, descriptor);
    done = true;
};
var __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {
    var useValue = arguments.length > 2;
    for (var i = 0; i < initializers.length; i++) {
        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
    }
    return useValue ? value : void 0;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateInterviewDto = void 0;
var openapi = require("@nestjs/swagger");
var swagger_1 = require("@nestjs/swagger");
var class_validator_1 = require("class-validator");
var CreateInterviewDto = function () {
    var _a;
    var _application_id_decorators;
    var _application_id_initializers = [];
    var _application_id_extraInitializers = [];
    var _interview_time_decorators;
    var _interview_time_initializers = [];
    var _interview_time_extraInitializers = [];
    var _interview_type_decorators;
    var _interview_type_initializers = [];
    var _interview_type_extraInitializers = [];
    var _notes_decorators;
    var _notes_initializers = [];
    var _notes_extraInitializers = [];
    return _a = /** @class */ (function () {
            function CreateInterviewDto() {
                this.application_id = __runInitializers(this, _application_id_initializers, void 0);
                this.interview_time = (__runInitializers(this, _application_id_extraInitializers), __runInitializers(this, _interview_time_initializers, void 0));
                this.interview_type = (__runInitializers(this, _interview_time_extraInitializers), __runInitializers(this, _interview_type_initializers, void 0));
                this.notes = (__runInitializers(this, _interview_type_extraInitializers), __runInitializers(this, _notes_initializers, void 0));
                __runInitializers(this, _notes_extraInitializers);
            }
            CreateInterviewDto._OPENAPI_METADATA_FACTORY = function () {
                return { application_id: { required: true, type: function () { return Number; } }, interview_time: { required: false, type: function () { return String; } }, interview_type: { required: false, type: function () { return String; } }, notes: { required: false, type: function () { return String; } } };
            };
            return CreateInterviewDto;
        }()),
        (function () {
            var _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(null) : void 0;
            _application_id_decorators = [(0, swagger_1.ApiProperty)({ example: 1, description: 'Application ID' }), (0, class_validator_1.IsNotEmpty)(), (0, class_validator_1.IsNumber)()];
            _interview_time_decorators = [(0, swagger_1.ApiProperty)({ example: '2024-01-15T10:00:00Z', description: 'Interview time' }), (0, class_validator_1.IsOptional)(), (0, class_validator_1.IsDateString)()];
            _interview_type_decorators = [(0, swagger_1.ApiProperty)({ example: 'phone', description: 'Interview type' }), (0, class_validator_1.IsOptional)(), (0, class_validator_1.IsString)()];
            _notes_decorators = [(0, swagger_1.ApiProperty)({ example: 'Initial screening interview', description: 'Interview notes' }), (0, class_validator_1.IsOptional)(), (0, class_validator_1.IsString)()];
            __esDecorate(null, null, _application_id_decorators, { kind: "field", name: "application_id", static: false, private: false, access: { has: function (obj) { return "application_id" in obj; }, get: function (obj) { return obj.application_id; }, set: function (obj, value) { obj.application_id = value; } }, metadata: _metadata }, _application_id_initializers, _application_id_extraInitializers);
            __esDecorate(null, null, _interview_time_decorators, { kind: "field", name: "interview_time", static: false, private: false, access: { has: function (obj) { return "interview_time" in obj; }, get: function (obj) { return obj.interview_time; }, set: function (obj, value) { obj.interview_time = value; } }, metadata: _metadata }, _interview_time_initializers, _interview_time_extraInitializers);
            __esDecorate(null, null, _interview_type_decorators, { kind: "field", name: "interview_type", static: false, private: false, access: { has: function (obj) { return "interview_type" in obj; }, get: function (obj) { return obj.interview_type; }, set: function (obj, value) { obj.interview_type = value; } }, metadata: _metadata }, _interview_type_initializers, _interview_type_extraInitializers);
            __esDecorate(null, null, _notes_decorators, { kind: "field", name: "notes", static: false, private: false, access: { has: function (obj) { return "notes" in obj; }, get: function (obj) { return obj.notes; }, set: function (obj, value) { obj.notes = value; } }, metadata: _metadata }, _notes_initializers, _notes_extraInitializers);
            if (_metadata) Object.defineProperty(_a, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
        })(),
        _a;
}();
exports.CreateInterviewDto = CreateInterviewDto;
