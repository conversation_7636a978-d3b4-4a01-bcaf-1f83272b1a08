"use strict";
var __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {
    var useValue = arguments.length > 2;
    for (var i = 0; i < initializers.length; i++) {
        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
    }
    return useValue ? value : void 0;
};
var __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
    function accept(f) { if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected"); return f; }
    var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
    var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
    var _, done = false;
    for (var i = decorators.length - 1; i >= 0; i--) {
        var context = {};
        for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
        for (var p in contextIn.access) context.access[p] = contextIn.access[p];
        context.addInitializer = function (f) { if (done) throw new TypeError("Cannot add initializers after decoration has completed"); extraInitializers.push(accept(f || null)); };
        var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
        if (kind === "accessor") {
            if (result === void 0) continue;
            if (result === null || typeof result !== "object") throw new TypeError("Object expected");
            if (_ = accept(result.get)) descriptor.get = _;
            if (_ = accept(result.set)) descriptor.set = _;
            if (_ = accept(result.init)) initializers.unshift(_);
        }
        else if (_ = accept(result)) {
            if (kind === "field") initializers.unshift(_);
            else descriptor[key] = _;
        }
    }
    if (target) Object.defineProperty(target, contextIn.name, descriptor);
    done = true;
};
var __setFunctionName = (this && this.__setFunctionName) || function (f, name, prefix) {
    if (typeof name === "symbol") name = name.description ? "[".concat(name.description, "]") : "";
    return Object.defineProperty(f, "name", { configurable: true, value: prefix ? "".concat(prefix, " ", name) : name });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CompaniesController = void 0;
var openapi = require("@nestjs/swagger");
var common_1 = require("@nestjs/common");
var swagger_1 = require("@nestjs/swagger");
var response_message_decorator_1 = require("../common/decorators/response-message.decorator");
var roles_decorator_1 = require("../common/decorators/roles.decorator");
var user_role_enum_1 = require("../common/enums/user-role.enum");
var CompaniesController = function () {
    var _classDecorators = [(0, swagger_1.ApiTags)('companies'), (0, swagger_1.ApiBearerAuth)('access-token'), (0, common_1.Controller)('companies')];
    var _classDescriptor;
    var _classExtraInitializers = [];
    var _classThis;
    var _instanceExtraInitializers = [];
    var _create_decorators;
    var _findAll_decorators;
    var _findOne_decorators;
    var _getStats_decorators;
    var _update_decorators;
    var _remove_decorators;
    var _followCompany_decorators;
    var _unfollowCompany_decorators;
    var _getMyFollowedCompanies_decorators;
    var CompaniesController = _classThis = /** @class */ (function () {
        function CompaniesController_1(companiesService) {
            this.companiesService = (__runInitializers(this, _instanceExtraInitializers), companiesService);
        }
        CompaniesController_1.prototype.create = function (createCompanyDto) {
            return this.companiesService.create(createCompanyDto);
        };
        CompaniesController_1.prototype.findAll = function (search) {
            return this.companiesService.findAll(search);
        };
        CompaniesController_1.prototype.findOne = function (id) {
            return this.companiesService.findOne(id);
        };
        CompaniesController_1.prototype.getStats = function (id) {
            return this.companiesService.getCompanyStats(id);
        };
        CompaniesController_1.prototype.update = function (id, updateCompanyDto) {
            return this.companiesService.update(id, updateCompanyDto);
        };
        CompaniesController_1.prototype.remove = function (id) {
            return this.companiesService.remove(id);
        };
        // Follow/Unfollow endpoints
        CompaniesController_1.prototype.followCompany = function (user, companyId) {
            return this.companiesService.followCompany(user.id, companyId);
        };
        CompaniesController_1.prototype.unfollowCompany = function (user, companyId) {
            return this.companiesService.unfollowCompany(user.id, companyId);
        };
        CompaniesController_1.prototype.getMyFollowedCompanies = function (user) {
            return this.companiesService.getFollowedCompanies(user.id);
        };
        return CompaniesController_1;
    }());
    __setFunctionName(_classThis, "CompaniesController");
    (function () {
        var _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(null) : void 0;
        _create_decorators = [(0, roles_decorator_1.Roles)(user_role_enum_1.UserRole.ADMIN, user_role_enum_1.UserRole.HR), (0, common_1.Post)(), (0, swagger_1.ApiOperation)({ summary: 'Create a new company' }), (0, swagger_1.ApiResponse)({ status: 201, description: 'Company created successfully' }), (0, response_message_decorator_1.ResponseMessage)('Company created successfully'), openapi.ApiResponse({ status: 201, type: require("./entities/company.entity").Company })];
        _findAll_decorators = [(0, common_1.Get)(), (0, swagger_1.ApiOperation)({ summary: 'Get all companies' }), (0, swagger_1.ApiQuery)({ name: 'search', required: false, description: 'Search companies by name or industry' }), (0, swagger_1.ApiResponse)({ status: 200, description: 'Companies retrieved successfully' }), (0, response_message_decorator_1.ResponseMessage)('Companies retrieved successfully'), openapi.ApiResponse({ status: 200, type: [require("./entities/company.entity").Company] })];
        _findOne_decorators = [(0, common_1.Get)(':id'), (0, swagger_1.ApiOperation)({ summary: 'Get company by ID' }), (0, swagger_1.ApiResponse)({ status: 200, description: 'Company retrieved successfully' }), (0, response_message_decorator_1.ResponseMessage)('Company retrieved successfully'), openapi.ApiResponse({ status: 200, type: require("./entities/company.entity").Company })];
        _getStats_decorators = [(0, common_1.Get)(':id/stats'), (0, swagger_1.ApiOperation)({ summary: 'Get company statistics' }), (0, swagger_1.ApiResponse)({ status: 200, description: 'Company statistics retrieved successfully' }), (0, response_message_decorator_1.ResponseMessage)('Company statistics retrieved successfully'), openapi.ApiResponse({ status: 200 })];
        _update_decorators = [(0, roles_decorator_1.Roles)(user_role_enum_1.UserRole.ADMIN, user_role_enum_1.UserRole.HR), (0, common_1.Patch)(':id'), (0, swagger_1.ApiOperation)({ summary: 'Update company by ID' }), (0, swagger_1.ApiResponse)({ status: 200, description: 'Company updated successfully' }), (0, response_message_decorator_1.ResponseMessage)('Company updated successfully'), openapi.ApiResponse({ status: 200, type: require("./entities/company.entity").Company })];
        _remove_decorators = [(0, roles_decorator_1.Roles)(user_role_enum_1.UserRole.ADMIN), (0, common_1.Delete)(':id'), (0, swagger_1.ApiOperation)({ summary: 'Delete company by ID' }), (0, swagger_1.ApiResponse)({ status: 200, description: 'Company deleted successfully' }), (0, response_message_decorator_1.ResponseMessage)('Company deleted successfully'), openapi.ApiResponse({ status: 200 })];
        _followCompany_decorators = [(0, common_1.Post)(':id/follow'), (0, swagger_1.ApiOperation)({ summary: 'Follow a company' }), (0, swagger_1.ApiResponse)({ status: 201, description: 'Company followed successfully' }), (0, response_message_decorator_1.ResponseMessage)('Company followed successfully'), openapi.ApiResponse({ status: 201, type: require("../followed-companies/entities/followed-company.entity").FollowedCompany })];
        _unfollowCompany_decorators = [(0, common_1.Delete)(':id/unfollow'), (0, swagger_1.ApiOperation)({ summary: 'Unfollow a company' }), (0, swagger_1.ApiResponse)({ status: 200, description: 'Company unfollowed successfully' }), (0, response_message_decorator_1.ResponseMessage)('Company unfollowed successfully'), openapi.ApiResponse({ status: 200 })];
        _getMyFollowedCompanies_decorators = [(0, common_1.Get)('my-followed'), (0, swagger_1.ApiOperation)({ summary: 'Get companies I follow' }), (0, swagger_1.ApiResponse)({ status: 200, description: 'Followed companies retrieved successfully' }), (0, response_message_decorator_1.ResponseMessage)('Followed companies retrieved successfully'), openapi.ApiResponse({ status: 200, type: [require("../followed-companies/entities/followed-company.entity").FollowedCompany] })];
        __esDecorate(_classThis, null, _create_decorators, { kind: "method", name: "create", static: false, private: false, access: { has: function (obj) { return "create" in obj; }, get: function (obj) { return obj.create; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _findAll_decorators, { kind: "method", name: "findAll", static: false, private: false, access: { has: function (obj) { return "findAll" in obj; }, get: function (obj) { return obj.findAll; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _findOne_decorators, { kind: "method", name: "findOne", static: false, private: false, access: { has: function (obj) { return "findOne" in obj; }, get: function (obj) { return obj.findOne; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _getStats_decorators, { kind: "method", name: "getStats", static: false, private: false, access: { has: function (obj) { return "getStats" in obj; }, get: function (obj) { return obj.getStats; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _update_decorators, { kind: "method", name: "update", static: false, private: false, access: { has: function (obj) { return "update" in obj; }, get: function (obj) { return obj.update; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _remove_decorators, { kind: "method", name: "remove", static: false, private: false, access: { has: function (obj) { return "remove" in obj; }, get: function (obj) { return obj.remove; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _followCompany_decorators, { kind: "method", name: "followCompany", static: false, private: false, access: { has: function (obj) { return "followCompany" in obj; }, get: function (obj) { return obj.followCompany; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _unfollowCompany_decorators, { kind: "method", name: "unfollowCompany", static: false, private: false, access: { has: function (obj) { return "unfollowCompany" in obj; }, get: function (obj) { return obj.unfollowCompany; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _getMyFollowedCompanies_decorators, { kind: "method", name: "getMyFollowedCompanies", static: false, private: false, access: { has: function (obj) { return "getMyFollowedCompanies" in obj; }, get: function (obj) { return obj.getMyFollowedCompanies; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(null, _classDescriptor = { value: _classThis }, _classDecorators, { kind: "class", name: _classThis.name, metadata: _metadata }, null, _classExtraInitializers);
        CompaniesController = _classThis = _classDescriptor.value;
        if (_metadata) Object.defineProperty(_classThis, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
        __runInitializers(_classThis, _classExtraInitializers);
    })();
    return CompaniesController = _classThis;
}();
exports.CompaniesController = CompaniesController;
