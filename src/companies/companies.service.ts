import {
  Injectable,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like } from 'typeorm';

import { Company } from './entities/company.entity';
import { CreateCompanyDto } from './dto/create-company.dto';
import { UpdateCompanyDto } from './dto/update-company.dto';

@Injectable()
export class CompaniesService {
  constructor(
    @InjectRepository(Company)
    private companyRepository: Repository<Company>,
  ) {}

  async create(createCompanyDto: CreateCompanyDto): Promise<Company> {
    // Check if company name already exists
    const existingCompany = await this.companyRepository.findOne({
      where: { company_name: createCompanyDto.company_name },
    });

    if (existingCompany) {
      throw new ConflictException('Company name already exists');
    }

    const company = this.companyRepository.create({
      ...createCompanyDto,
      company_id: Math.floor(Math.random() * 1000000), // Temporary ID generation
    });

    return this.companyRepository.save(company);
  }

  async findAll(search?: string): Promise<Company[]> {
    const queryBuilder = this.companyRepository.createQueryBuilder('company');

    if (search) {
      queryBuilder.where(
        'company.company_name ILIKE :search OR company.industry ILIKE :search',
        { search: `%${search}%` },
      );
    }

    return queryBuilder
      .leftJoinAndSelect('company.job_posts', 'job_posts')
      .leftJoinAndSelect('company.followers', 'followers')
      .orderBy('company.created_at', 'DESC')
      .getMany();
  }

  async findOne(id: number): Promise<Company> {
    const company = await this.companyRepository.findOne({
      where: { id },
      relations: ['job_posts', 'followers'],
    });

    if (!company) {
      throw new NotFoundException('Company not found');
    }

    return company;
  }

  async findByName(name: string): Promise<Company> {
    return this.companyRepository.findOne({
      where: { company_name: name },
      relations: ['job_posts', 'followers'],
    });
  }

  async update(id: number, updateCompanyDto: UpdateCompanyDto): Promise<Company> {
    const company = await this.findOne(id);

    if (updateCompanyDto.company_name && updateCompanyDto.company_name !== company.company_name) {
      const existingCompany = await this.companyRepository.findOne({
        where: { company_name: updateCompanyDto.company_name },
      });
      if (existingCompany) {
        throw new ConflictException('Company name already exists');
      }
    }

    await this.companyRepository.update(id, updateCompanyDto);
    return this.findOne(id);
  }

  async remove(id: number): Promise<void> {
    const company = await this.findOne(id);
    await this.companyRepository.remove(company);
  }

  async getCompanyStats(id: number) {
    const company = await this.findOne(id);
    
    const jobCount = await this.companyRepository
      .createQueryBuilder('company')
      .leftJoin('company.job_posts', 'job_posts')
      .where('company.id = :id', { id })
      .andWhere('job_posts.status = :status', { status: 'active' })
      .getCount();

    const followerCount = await this.companyRepository
      .createQueryBuilder('company')
      .leftJoin('company.followers', 'followers')
      .where('company.id = :id', { id })
      .getCount();

    return {
      company,
      stats: {
        active_jobs: jobCount,
        followers: followerCount,
      },
    };
  }
}
