"use strict";
var __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
    function accept(f) { if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected"); return f; }
    var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
    var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
    var _, done = false;
    for (var i = decorators.length - 1; i >= 0; i--) {
        var context = {};
        for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
        for (var p in contextIn.access) context.access[p] = contextIn.access[p];
        context.addInitializer = function (f) { if (done) throw new TypeError("Cannot add initializers after decoration has completed"); extraInitializers.push(accept(f || null)); };
        var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
        if (kind === "accessor") {
            if (result === void 0) continue;
            if (result === null || typeof result !== "object") throw new TypeError("Object expected");
            if (_ = accept(result.get)) descriptor.get = _;
            if (_ = accept(result.set)) descriptor.set = _;
            if (_ = accept(result.init)) initializers.unshift(_);
        }
        else if (_ = accept(result)) {
            if (kind === "field") initializers.unshift(_);
            else descriptor[key] = _;
        }
    }
    if (target) Object.defineProperty(target, contextIn.name, descriptor);
    done = true;
};
var __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {
    var useValue = arguments.length > 2;
    for (var i = 0; i < initializers.length; i++) {
        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
    }
    return useValue ? value : void 0;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateCompanyDto = void 0;
var openapi = require("@nestjs/swagger");
var swagger_1 = require("@nestjs/swagger");
var class_validator_1 = require("class-validator");
var CreateCompanyDto = function () {
    var _a;
    var _company_name_decorators;
    var _company_name_initializers = [];
    var _company_name_extraInitializers = [];
    var _description_decorators;
    var _description_initializers = [];
    var _description_extraInitializers = [];
    var _industry_decorators;
    var _industry_initializers = [];
    var _industry_extraInitializers = [];
    var _website_decorators;
    var _website_initializers = [];
    var _website_extraInitializers = [];
    var _company_image_decorators;
    var _company_image_initializers = [];
    var _company_image_extraInitializers = [];
    return _a = /** @class */ (function () {
            function CreateCompanyDto() {
                this.company_name = __runInitializers(this, _company_name_initializers, void 0);
                this.description = (__runInitializers(this, _company_name_extraInitializers), __runInitializers(this, _description_initializers, void 0));
                this.industry = (__runInitializers(this, _description_extraInitializers), __runInitializers(this, _industry_initializers, void 0));
                this.website = (__runInitializers(this, _industry_extraInitializers), __runInitializers(this, _website_initializers, void 0));
                this.company_image = (__runInitializers(this, _website_extraInitializers), __runInitializers(this, _company_image_initializers, void 0));
                __runInitializers(this, _company_image_extraInitializers);
            }
            CreateCompanyDto._OPENAPI_METADATA_FACTORY = function () {
                return { company_name: { required: true, type: function () { return String; } }, description: { required: false, type: function () { return String; } }, industry: { required: false, type: function () { return String; } }, website: { required: false, type: function () { return String; }, format: "uri" }, company_image: { required: false, type: function () { return String; } } };
            };
            return CreateCompanyDto;
        }()),
        (function () {
            var _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(null) : void 0;
            _company_name_decorators = [(0, swagger_1.ApiProperty)({ example: 'Tech Corp Inc.', description: 'Company name' }), (0, class_validator_1.IsNotEmpty)(), (0, class_validator_1.IsString)()];
            _description_decorators = [(0, swagger_1.ApiProperty)({
                    example: 'A leading technology company specializing in software development',
                    description: 'Company description'
                }), (0, class_validator_1.IsOptional)(), (0, class_validator_1.IsString)()];
            _industry_decorators = [(0, swagger_1.ApiProperty)({ example: 'Technology', description: 'Industry sector' }), (0, class_validator_1.IsOptional)(), (0, class_validator_1.IsString)()];
            _website_decorators = [(0, swagger_1.ApiProperty)({ example: 'https://techcorp.com', description: 'Company website' }), (0, class_validator_1.IsOptional)(), (0, class_validator_1.IsUrl)()];
            _company_image_decorators = [(0, swagger_1.ApiProperty)({ example: 'company-logo.jpg', description: 'Company image/logo filename' }), (0, class_validator_1.IsOptional)(), (0, class_validator_1.IsString)()];
            __esDecorate(null, null, _company_name_decorators, { kind: "field", name: "company_name", static: false, private: false, access: { has: function (obj) { return "company_name" in obj; }, get: function (obj) { return obj.company_name; }, set: function (obj, value) { obj.company_name = value; } }, metadata: _metadata }, _company_name_initializers, _company_name_extraInitializers);
            __esDecorate(null, null, _description_decorators, { kind: "field", name: "description", static: false, private: false, access: { has: function (obj) { return "description" in obj; }, get: function (obj) { return obj.description; }, set: function (obj, value) { obj.description = value; } }, metadata: _metadata }, _description_initializers, _description_extraInitializers);
            __esDecorate(null, null, _industry_decorators, { kind: "field", name: "industry", static: false, private: false, access: { has: function (obj) { return "industry" in obj; }, get: function (obj) { return obj.industry; }, set: function (obj, value) { obj.industry = value; } }, metadata: _metadata }, _industry_initializers, _industry_extraInitializers);
            __esDecorate(null, null, _website_decorators, { kind: "field", name: "website", static: false, private: false, access: { has: function (obj) { return "website" in obj; }, get: function (obj) { return obj.website; }, set: function (obj, value) { obj.website = value; } }, metadata: _metadata }, _website_initializers, _website_extraInitializers);
            __esDecorate(null, null, _company_image_decorators, { kind: "field", name: "company_image", static: false, private: false, access: { has: function (obj) { return "company_image" in obj; }, get: function (obj) { return obj.company_image; }, set: function (obj, value) { obj.company_image = value; } }, metadata: _metadata }, _company_image_initializers, _company_image_extraInitializers);
            if (_metadata) Object.defineProperty(_a, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
        })(),
        _a;
}();
exports.CreateCompanyDto = CreateCompanyDto;
