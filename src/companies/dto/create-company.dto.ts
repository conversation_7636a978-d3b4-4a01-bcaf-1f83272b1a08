import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, IsUrl } from 'class-validator';

export class CreateCompanyDto {
  @ApiProperty({ example: 'Tech Corp Inc.', description: 'Company name' })
  @IsNotEmpty()
  @IsString()
  company_name: string;

  @ApiProperty({ 
    example: 'A leading technology company specializing in software development', 
    description: 'Company description' 
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ example: 'Technology', description: 'Industry sector' })
  @IsOptional()
  @IsString()
  industry?: string;

  @ApiProperty({ example: 'https://techcorp.com', description: 'Company website' })
  @IsOptional()
  @IsUrl()
  website?: string;

  @ApiProperty({ example: 'company-logo.jpg', description: 'Company image/logo filename' })
  @IsOptional()
  @IsString()
  company_image?: string;
}
