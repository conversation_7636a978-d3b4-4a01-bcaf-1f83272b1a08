import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';

import { CompaniesService } from './companies.service';
import { CreateCompanyDto } from './dto/create-company.dto';
import { UpdateCompanyDto } from './dto/update-company.dto';
import { ResponseMessage } from '../common/decorators/response-message.decorator';
import { User } from '../common/decorators/user.decorator';
import { IUser } from '../auth/interfaces/user.interface';
import { Roles } from '../common/decorators/roles.decorator';
import { UserRole } from '../common/enums/user-role.enum';

@ApiTags('companies')
@ApiBearerAuth('access-token')
@Controller('companies')
export class CompaniesController {
  constructor(private readonly companiesService: CompaniesService) {}

  @Roles(UserRole.ADMIN, UserRole.HR)
  @Post()
  @ApiOperation({ summary: 'Create a new company' })
  @ApiResponse({ status: 201, description: 'Company created successfully' })
  @ResponseMessage('Company created successfully')
  create(@Body() createCompanyDto: CreateCompanyDto) {
    return this.companiesService.create(createCompanyDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all companies' })
  @ApiQuery({ name: 'search', required: false, description: 'Search companies by name or industry' })
  @ApiResponse({ status: 200, description: 'Companies retrieved successfully' })
  @ResponseMessage('Companies retrieved successfully')
  findAll(@Query('search') search?: string) {
    return this.companiesService.findAll(search);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get company by ID' })
  @ApiResponse({ status: 200, description: 'Company retrieved successfully' })
  @ResponseMessage('Company retrieved successfully')
  findOne(@Param('id', ParseIntPipe) id: number) {
    return this.companiesService.findOne(id);
  }

  @Get(':id/stats')
  @ApiOperation({ summary: 'Get company statistics' })
  @ApiResponse({ status: 200, description: 'Company statistics retrieved successfully' })
  @ResponseMessage('Company statistics retrieved successfully')
  getStats(@Param('id', ParseIntPipe) id: number) {
    return this.companiesService.getCompanyStats(id);
  }

  @Roles(UserRole.ADMIN, UserRole.HR)
  @Patch(':id')
  @ApiOperation({ summary: 'Update company by ID' })
  @ApiResponse({ status: 200, description: 'Company updated successfully' })
  @ResponseMessage('Company updated successfully')
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateCompanyDto: UpdateCompanyDto,
  ) {
    return this.companiesService.update(id, updateCompanyDto);
  }

  @Roles(UserRole.ADMIN)
  @Delete(':id')
  @ApiOperation({ summary: 'Delete company by ID' })
  @ApiResponse({ status: 200, description: 'Company deleted successfully' })
  @ResponseMessage('Company deleted successfully')
  remove(@Param('id', ParseIntPipe) id: number) {
    return this.companiesService.remove(id);
  }

  // Follow/Unfollow endpoints
  @Post(':id/follow')
  @ApiOperation({ summary: 'Follow a company' })
  @ApiResponse({ status: 201, description: 'Company followed successfully' })
  @ResponseMessage('Company followed successfully')
  followCompany(@User() user: IUser, @Param('id', ParseIntPipe) companyId: number) {
    return this.companiesService.followCompany(user.id, companyId);
  }

  @Delete(':id/unfollow')
  @ApiOperation({ summary: 'Unfollow a company' })
  @ApiResponse({ status: 200, description: 'Company unfollowed successfully' })
  @ResponseMessage('Company unfollowed successfully')
  unfollowCompany(@User() user: IUser, @Param('id', ParseIntPipe) companyId: number) {
    return this.companiesService.unfollowCompany(user.id, companyId);
  }

  @Get('my-followed')
  @ApiOperation({ summary: 'Get companies I follow' })
  @ApiResponse({ status: 200, description: 'Followed companies retrieved successfully' })
  @ResponseMessage('Followed companies retrieved successfully')
  getMyFollowedCompanies(@User() user: IUser) {
    return this.companiesService.getFollowedCompanies(user.id);
  }
}
