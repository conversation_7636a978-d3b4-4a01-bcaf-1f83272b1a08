"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
    function accept(f) { if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected"); return f; }
    var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
    var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
    var _, done = false;
    for (var i = decorators.length - 1; i >= 0; i--) {
        var context = {};
        for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
        for (var p in contextIn.access) context.access[p] = contextIn.access[p];
        context.addInitializer = function (f) { if (done) throw new TypeError("Cannot add initializers after decoration has completed"); extraInitializers.push(accept(f || null)); };
        var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
        if (kind === "accessor") {
            if (result === void 0) continue;
            if (result === null || typeof result !== "object") throw new TypeError("Object expected");
            if (_ = accept(result.get)) descriptor.get = _;
            if (_ = accept(result.set)) descriptor.set = _;
            if (_ = accept(result.init)) initializers.unshift(_);
        }
        else if (_ = accept(result)) {
            if (kind === "field") initializers.unshift(_);
            else descriptor[key] = _;
        }
    }
    if (target) Object.defineProperty(target, contextIn.name, descriptor);
    done = true;
};
var __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {
    var useValue = arguments.length > 2;
    for (var i = 0; i < initializers.length; i++) {
        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
    }
    return useValue ? value : void 0;
};
var __setFunctionName = (this && this.__setFunctionName) || function (f, name, prefix) {
    if (typeof name === "symbol") name = name.description ? "[".concat(name.description, "]") : "";
    return Object.defineProperty(f, "name", { configurable: true, value: prefix ? "".concat(prefix, " ", name) : name });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Company = void 0;
var openapi = require("@nestjs/swagger");
var typeorm_1 = require("typeorm");
var base_entity_1 = require("../../common/entities/base.entity");
var job_post_entity_1 = require("../../jobs/entities/job-post.entity");
var followed_company_entity_1 = require("../../followed-companies/entities/followed-company.entity");
var Company = function () {
    var _classDecorators = [(0, typeorm_1.Entity)('companies')];
    var _classDescriptor;
    var _classExtraInitializers = [];
    var _classThis;
    var _classSuper = base_entity_1.BaseEntity;
    var _company_id_decorators;
    var _company_id_initializers = [];
    var _company_id_extraInitializers = [];
    var _company_name_decorators;
    var _company_name_initializers = [];
    var _company_name_extraInitializers = [];
    var _description_decorators;
    var _description_initializers = [];
    var _description_extraInitializers = [];
    var _company_image_decorators;
    var _company_image_initializers = [];
    var _company_image_extraInitializers = [];
    var _industry_decorators;
    var _industry_initializers = [];
    var _industry_extraInitializers = [];
    var _website_decorators;
    var _website_initializers = [];
    var _website_extraInitializers = [];
    var _job_posts_decorators;
    var _job_posts_initializers = [];
    var _job_posts_extraInitializers = [];
    var _followers_decorators;
    var _followers_initializers = [];
    var _followers_extraInitializers = [];
    var Company = _classThis = /** @class */ (function (_super) {
        __extends(Company_1, _super);
        function Company_1() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.company_id = __runInitializers(_this, _company_id_initializers, void 0);
            _this.company_name = (__runInitializers(_this, _company_id_extraInitializers), __runInitializers(_this, _company_name_initializers, void 0));
            _this.description = (__runInitializers(_this, _company_name_extraInitializers), __runInitializers(_this, _description_initializers, void 0));
            _this.company_image = (__runInitializers(_this, _description_extraInitializers), __runInitializers(_this, _company_image_initializers, void 0));
            _this.industry = (__runInitializers(_this, _company_image_extraInitializers), __runInitializers(_this, _industry_initializers, void 0));
            _this.website = (__runInitializers(_this, _industry_extraInitializers), __runInitializers(_this, _website_initializers, void 0));
            // Relations
            _this.job_posts = (__runInitializers(_this, _website_extraInitializers), __runInitializers(_this, _job_posts_initializers, void 0));
            _this.followers = (__runInitializers(_this, _job_posts_extraInitializers), __runInitializers(_this, _followers_initializers, void 0));
            __runInitializers(_this, _followers_extraInitializers);
            return _this;
        }
        Company_1._OPENAPI_METADATA_FACTORY = function () {
            return { company_id: { required: true, type: function () { return Number; } }, company_name: { required: true, type: function () { return String; } }, description: { required: true, type: function () { return String; } }, company_image: { required: true, type: function () { return String; } }, industry: { required: true, type: function () { return String; } }, website: { required: true, type: function () { return String; } }, job_posts: { required: true, type: function () { return [require("../../jobs/entities/job-post.entity").JobPost]; } }, followers: { required: true, type: function () { return [require("../../followed-companies/entities/followed-company.entity").FollowedCompany]; } } };
        };
        return Company_1;
    }(_classSuper));
    __setFunctionName(_classThis, "Company");
    (function () {
        var _a;
        var _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create((_a = _classSuper[Symbol.metadata]) !== null && _a !== void 0 ? _a : null) : void 0;
        _company_id_decorators = [(0, typeorm_1.Column)({ name: 'company_id' })];
        _company_name_decorators = [(0, typeorm_1.Column)({ name: 'company_name', length: 200 })];
        _description_decorators = [(0, typeorm_1.Column)({ name: 'description', type: 'text', nullable: true })];
        _company_image_decorators = [(0, typeorm_1.Column)({ name: 'company_image', length: 255, nullable: true })];
        _industry_decorators = [(0, typeorm_1.Column)({ name: 'industry', length: 100, nullable: true })];
        _website_decorators = [(0, typeorm_1.Column)({ name: 'website', length: 255, nullable: true })];
        _job_posts_decorators = [(0, typeorm_1.OneToMany)(function () { return job_post_entity_1.JobPost; }, function (jobPost) { return jobPost.company; })];
        _followers_decorators = [(0, typeorm_1.OneToMany)(function () { return followed_company_entity_1.FollowedCompany; }, function (followedCompany) { return followedCompany.company; })];
        __esDecorate(null, null, _company_id_decorators, { kind: "field", name: "company_id", static: false, private: false, access: { has: function (obj) { return "company_id" in obj; }, get: function (obj) { return obj.company_id; }, set: function (obj, value) { obj.company_id = value; } }, metadata: _metadata }, _company_id_initializers, _company_id_extraInitializers);
        __esDecorate(null, null, _company_name_decorators, { kind: "field", name: "company_name", static: false, private: false, access: { has: function (obj) { return "company_name" in obj; }, get: function (obj) { return obj.company_name; }, set: function (obj, value) { obj.company_name = value; } }, metadata: _metadata }, _company_name_initializers, _company_name_extraInitializers);
        __esDecorate(null, null, _description_decorators, { kind: "field", name: "description", static: false, private: false, access: { has: function (obj) { return "description" in obj; }, get: function (obj) { return obj.description; }, set: function (obj, value) { obj.description = value; } }, metadata: _metadata }, _description_initializers, _description_extraInitializers);
        __esDecorate(null, null, _company_image_decorators, { kind: "field", name: "company_image", static: false, private: false, access: { has: function (obj) { return "company_image" in obj; }, get: function (obj) { return obj.company_image; }, set: function (obj, value) { obj.company_image = value; } }, metadata: _metadata }, _company_image_initializers, _company_image_extraInitializers);
        __esDecorate(null, null, _industry_decorators, { kind: "field", name: "industry", static: false, private: false, access: { has: function (obj) { return "industry" in obj; }, get: function (obj) { return obj.industry; }, set: function (obj, value) { obj.industry = value; } }, metadata: _metadata }, _industry_initializers, _industry_extraInitializers);
        __esDecorate(null, null, _website_decorators, { kind: "field", name: "website", static: false, private: false, access: { has: function (obj) { return "website" in obj; }, get: function (obj) { return obj.website; }, set: function (obj, value) { obj.website = value; } }, metadata: _metadata }, _website_initializers, _website_extraInitializers);
        __esDecorate(null, null, _job_posts_decorators, { kind: "field", name: "job_posts", static: false, private: false, access: { has: function (obj) { return "job_posts" in obj; }, get: function (obj) { return obj.job_posts; }, set: function (obj, value) { obj.job_posts = value; } }, metadata: _metadata }, _job_posts_initializers, _job_posts_extraInitializers);
        __esDecorate(null, null, _followers_decorators, { kind: "field", name: "followers", static: false, private: false, access: { has: function (obj) { return "followers" in obj; }, get: function (obj) { return obj.followers; }, set: function (obj, value) { obj.followers = value; } }, metadata: _metadata }, _followers_initializers, _followers_extraInitializers);
        __esDecorate(null, _classDescriptor = { value: _classThis }, _classDecorators, { kind: "class", name: _classThis.name, metadata: _metadata }, null, _classExtraInitializers);
        Company = _classThis = _classDescriptor.value;
        if (_metadata) Object.defineProperty(_classThis, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
        __runInitializers(_classThis, _classExtraInitializers);
    })();
    return Company = _classThis;
}();
exports.Company = Company;
