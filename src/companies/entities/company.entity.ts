import { Entity, PrimaryGeneratedColumn, Column, OneToMany, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('companies')
export class Company {
  @PrimaryGeneratedColumn()
  @Column({ name: 'company_id' })
  company_id: number;

  @Column({ name: 'name', length: 255 })
  name: string;

  @Column({ name: 'description', type: 'text', nullable: true })
  description?: string;

  @Column({ name: 'industry', length: 100, nullable: true })
  industry?: string;

  @Column({ name: 'size', length: 50, nullable: true })
  size?: string;

  @Column({ name: 'website', length: 255, nullable: true })
  website?: string;

  @Column({ name: 'email', length: 100, nullable: true })
  email?: string;

  @Column({ name: 'phone', length: 20, nullable: true })
  phone?: string;

  @Column({ name: 'address', type: 'text', nullable: true })
  address?: string;

  @Column({ name: 'city', length: 100, nullable: true })
  city?: string;

  @Column({ name: 'country', length: 100, nullable: true })
  country?: string;

  @Column({ name: 'logo', length: 255, nullable: true })
  logo?: string;

  @Column({ name: 'banner', length: 255, nullable: true })
  banner?: string;

  @Column({ name: 'founded_year', type: 'int', nullable: true })
  founded_year?: number;

  @Column({ name: 'is_verified', type: 'boolean', default: false })
  is_verified: boolean;

  @CreateDateColumn({ name: 'created_at' })
  created_at: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updated_at: Date;

  // Relations will be added later
  // @OneToMany(() => Job, (job) => job.company)
  // jobs: Job[];

  // @OneToMany(() => FollowedCompany, (followedCompany) => followedCompany.company)
  // followers: FollowedCompany[];
}
