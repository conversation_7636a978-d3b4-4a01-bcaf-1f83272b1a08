import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { DatabaseModule } from './database/database.module';
import { UsersModule } from './users/users.module';
import { CompaniesModule } from './companies/companies.module';
import { JobsModule } from './jobs/jobs.module';
import { ApplicationsModule } from './applications/applications.module';
import { SavedJobsModule } from './saved-jobs/saved-jobs.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    // DatabaseModule, // Enable when PostgreSQL is installed
    // UsersModule,    // Enable when PostgreSQL is installed
    // CompaniesModule, // Temporarily disabled due to TypeORM dependency
    // JobsModule,      // Temporarily disabled due to TypeORM dependency
    // ApplicationsModule, // Temporarily disabled due to TypeORM dependency
    // SavedJobsModule,    // Temporarily disabled due to TypeORM dependency
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
