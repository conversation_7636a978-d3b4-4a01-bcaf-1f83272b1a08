import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { DatabaseModule } from './database/database.module';
import { UsersModule } from './users/users.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    // DatabaseModule, // Temporarily disabled for testing
    // UsersModule,   // Temporarily disabled for testing
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
