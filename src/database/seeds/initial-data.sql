-- Initial seed data for Work Finder API

-- Insert default permissions
INSERT INTO permissions (permission_id, permission_name) VALUES
(1, 'USER'),
(2, 'ADMIN'),
(3, 'HR_MANAGER'),
(4, 'COMPANY_ADMIN')
ON CONFLICT (permission_name) DO NOTHING;

-- Insert sample companies
INSERT INTO companies (company_id, company_name, description, industry, website) VALUES
(1, 'Tech Innovations Inc.', 'Leading technology company specializing in software development and AI solutions.', 'Technology', 'https://techinnovations.com'),
(2, 'Global Marketing Solutions', 'Full-service marketing agency helping businesses grow their online presence.', 'Marketing', 'https://globalmarketing.com'),
(3, 'Healthcare Plus', 'Modern healthcare solutions provider with focus on patient care and technology.', 'Healthcare', 'https://healthcareplus.com'),
(4, 'Green Energy Corp', 'Renewable energy company focused on sustainable solutions for the future.', 'Energy', 'https://greenenergy.com'),
(5, 'Financial Services Group', 'Comprehensive financial services including banking, investments, and insurance.', 'Finance', 'https://financialservices.com')
ON CONFLICT (company_name) DO NOTHING;

-- Insert sample job posts
INSERT INTO job_posts (job_id, company_id, job_title, description, location, salary, job_type, status) VALUES
(1, 1, 'Senior Software Engineer', 'We are looking for an experienced software engineer to join our development team. You will be responsible for designing, developing, and maintaining high-quality software applications.', 'San Francisco, CA', '$120,000 - $160,000', 'full-time', 'active'),
(2, 1, 'Frontend Developer', 'Join our frontend team to create amazing user experiences. Experience with React, TypeScript, and modern CSS frameworks required.', 'Remote', '$90,000 - $130,000', 'full-time', 'active'),
(3, 2, 'Digital Marketing Specialist', 'Drive our digital marketing campaigns across multiple channels. Experience with SEO, SEM, and social media marketing required.', 'New York, NY', '$60,000 - $80,000', 'full-time', 'active'),
(4, 3, 'Healthcare Data Analyst', 'Analyze healthcare data to improve patient outcomes and operational efficiency. Strong SQL and Python skills required.', 'Chicago, IL', '$75,000 - $95,000', 'full-time', 'active'),
(5, 4, 'Renewable Energy Engineer', 'Design and implement renewable energy solutions. Engineering degree and experience with solar/wind systems required.', 'Austin, TX', '$85,000 - $110,000', 'full-time', 'active'),
(6, 5, 'Financial Analyst', 'Perform financial analysis and modeling to support business decisions. CFA or similar certification preferred.', 'Boston, MA', '$70,000 - $90,000', 'full-time', 'active'),
(7, 1, 'DevOps Engineer', 'Manage our cloud infrastructure and CI/CD pipelines. Experience with AWS, Docker, and Kubernetes required.', 'Seattle, WA', '$110,000 - $140,000', 'full-time', 'active'),
(8, 2, 'Content Marketing Manager', 'Lead our content marketing strategy and team. Experience in B2B content marketing and team management required.', 'Los Angeles, CA', '$80,000 - $100,000', 'full-time', 'active')
ON CONFLICT (job_id) DO NOTHING;

-- Note: Users, resumes, applications, and other user-generated content should be created through the API
-- to ensure proper password hashing and validation.
