"use strict";
var __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {
    var useValue = arguments.length > 2;
    for (var i = 0; i < initializers.length; i++) {
        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
    }
    return useValue ? value : void 0;
};
var __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
    function accept(f) { if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected"); return f; }
    var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
    var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
    var _, done = false;
    for (var i = decorators.length - 1; i >= 0; i--) {
        var context = {};
        for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
        for (var p in contextIn.access) context.access[p] = contextIn.access[p];
        context.addInitializer = function (f) { if (done) throw new TypeError("Cannot add initializers after decoration has completed"); extraInitializers.push(accept(f || null)); };
        var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
        if (kind === "accessor") {
            if (result === void 0) continue;
            if (result === null || typeof result !== "object") throw new TypeError("Object expected");
            if (_ = accept(result.get)) descriptor.get = _;
            if (_ = accept(result.set)) descriptor.set = _;
            if (_ = accept(result.init)) initializers.unshift(_);
        }
        else if (_ = accept(result)) {
            if (kind === "field") initializers.unshift(_);
            else descriptor[key] = _;
        }
    }
    if (target) Object.defineProperty(target, contextIn.name, descriptor);
    done = true;
};
var __setFunctionName = (this && this.__setFunctionName) || function (f, name, prefix) {
    if (typeof name === "symbol") name = name.description ? "[".concat(name.description, "]") : "";
    return Object.defineProperty(f, "name", { configurable: true, value: prefix ? "".concat(prefix, " ", name) : name });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResumesController = void 0;
var openapi = require("@nestjs/swagger");
var common_1 = require("@nestjs/common");
var platform_express_1 = require("@nestjs/platform-express");
var swagger_1 = require("@nestjs/swagger");
var multer_1 = require("multer");
var path_1 = require("path");
var response_message_decorator_1 = require("../common/decorators/response-message.decorator");
var ResumesController = function () {
    var _classDecorators = [(0, swagger_1.ApiTags)('resumes'), (0, swagger_1.ApiBearerAuth)('access-token'), (0, common_1.Controller)('resumes')];
    var _classDescriptor;
    var _classExtraInitializers = [];
    var _classThis;
    var _instanceExtraInitializers = [];
    var _create_decorators;
    var _uploadResume_decorators;
    var _findAll_decorators;
    var _getMyResumes_decorators;
    var _findByUser_decorators;
    var _getMyStats_decorators;
    var _findOne_decorators;
    var _update_decorators;
    var _remove_decorators;
    var ResumesController = _classThis = /** @class */ (function () {
        function ResumesController_1(resumesService) {
            this.resumesService = (__runInitializers(this, _instanceExtraInitializers), resumesService);
        }
        ResumesController_1.prototype.create = function (createResumeDto) {
            return this.resumesService.create(createResumeDto);
        };
        ResumesController_1.prototype.uploadResume = function (user, file) {
            if (!file) {
                throw new common_1.BadRequestException('No file uploaded');
            }
            return this.resumesService.uploadResume(user.id, file);
        };
        ResumesController_1.prototype.findAll = function () {
            return this.resumesService.findAll();
        };
        ResumesController_1.prototype.getMyResumes = function (user) {
            return this.resumesService.findByUser(user.id);
        };
        ResumesController_1.prototype.findByUser = function (userId) {
            return this.resumesService.findByUser(userId);
        };
        ResumesController_1.prototype.getMyStats = function (user) {
            return this.resumesService.getResumeStats(user.id);
        };
        ResumesController_1.prototype.findOne = function (id) {
            return this.resumesService.findOne(id);
        };
        ResumesController_1.prototype.update = function (id, updateResumeDto) {
            return this.resumesService.update(id, updateResumeDto);
        };
        ResumesController_1.prototype.remove = function (id) {
            return this.resumesService.remove(id);
        };
        return ResumesController_1;
    }());
    __setFunctionName(_classThis, "ResumesController");
    (function () {
        var _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(null) : void 0;
        _create_decorators = [(0, common_1.Post)(), (0, swagger_1.ApiOperation)({ summary: 'Create a new resume record' }), (0, swagger_1.ApiResponse)({ status: 201, description: 'Resume created successfully' }), (0, response_message_decorator_1.ResponseMessage)('Resume created successfully'), openapi.ApiResponse({ status: 201, type: require("./entities/resume.entity").Resume })];
        _uploadResume_decorators = [(0, common_1.Post)('upload'), (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file', {
                storage: (0, multer_1.diskStorage)({
                    destination: './uploads/resumes',
                    filename: function (req, file, cb) {
                        var uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
                        cb(null, "".concat(file.fieldname, "-").concat(uniqueSuffix).concat((0, path_1.extname)(file.originalname)));
                    },
                }),
                fileFilter: function (req, file, cb) {
                    if (file.mimetype.match(/\/(pdf|doc|docx)$/)) {
                        cb(null, true);
                    }
                    else {
                        cb(new common_1.BadRequestException('Only PDF, DOC, and DOCX files are allowed'), false);
                    }
                },
                limits: {
                    fileSize: 5 * 1024 * 1024, // 5MB
                },
            })), (0, swagger_1.ApiOperation)({ summary: 'Upload a resume file' }), (0, swagger_1.ApiConsumes)('multipart/form-data'), (0, swagger_1.ApiResponse)({ status: 201, description: 'Resume uploaded successfully' }), (0, response_message_decorator_1.ResponseMessage)('Resume uploaded successfully'), openapi.ApiResponse({ status: 201, type: require("./entities/resume.entity").Resume })];
        _findAll_decorators = [(0, common_1.Get)(), (0, swagger_1.ApiOperation)({ summary: 'Get all resumes' }), (0, swagger_1.ApiResponse)({ status: 200, description: 'Resumes retrieved successfully' }), (0, response_message_decorator_1.ResponseMessage)('Resumes retrieved successfully'), openapi.ApiResponse({ status: 200, type: [require("./entities/resume.entity").Resume] })];
        _getMyResumes_decorators = [(0, common_1.Get)('my-resumes'), (0, swagger_1.ApiOperation)({ summary: 'Get current user resumes' }), (0, swagger_1.ApiResponse)({ status: 200, description: 'User resumes retrieved successfully' }), (0, response_message_decorator_1.ResponseMessage)('User resumes retrieved successfully'), openapi.ApiResponse({ status: 200, type: [require("./entities/resume.entity").Resume] })];
        _findByUser_decorators = [(0, common_1.Get)('user/:userId'), (0, swagger_1.ApiOperation)({ summary: 'Get resumes by user ID' }), (0, swagger_1.ApiResponse)({ status: 200, description: 'User resumes retrieved successfully' }), (0, response_message_decorator_1.ResponseMessage)('User resumes retrieved successfully'), openapi.ApiResponse({ status: 200, type: [require("./entities/resume.entity").Resume] })];
        _getMyStats_decorators = [(0, common_1.Get)('stats'), (0, swagger_1.ApiOperation)({ summary: 'Get current user resume statistics' }), (0, swagger_1.ApiResponse)({ status: 200, description: 'Resume statistics retrieved successfully' }), (0, response_message_decorator_1.ResponseMessage)('Resume statistics retrieved successfully'), openapi.ApiResponse({ status: 200 })];
        _findOne_decorators = [(0, common_1.Get)(':id'), (0, swagger_1.ApiOperation)({ summary: 'Get resume by ID' }), (0, swagger_1.ApiResponse)({ status: 200, description: 'Resume retrieved successfully' }), (0, response_message_decorator_1.ResponseMessage)('Resume retrieved successfully'), openapi.ApiResponse({ status: 200, type: require("./entities/resume.entity").Resume })];
        _update_decorators = [(0, common_1.Patch)(':id'), (0, swagger_1.ApiOperation)({ summary: 'Update resume by ID' }), (0, swagger_1.ApiResponse)({ status: 200, description: 'Resume updated successfully' }), (0, response_message_decorator_1.ResponseMessage)('Resume updated successfully'), openapi.ApiResponse({ status: 200, type: require("./entities/resume.entity").Resume })];
        _remove_decorators = [(0, common_1.Delete)(':id'), (0, swagger_1.ApiOperation)({ summary: 'Delete resume by ID' }), (0, swagger_1.ApiResponse)({ status: 200, description: 'Resume deleted successfully' }), (0, response_message_decorator_1.ResponseMessage)('Resume deleted successfully'), openapi.ApiResponse({ status: 200 })];
        __esDecorate(_classThis, null, _create_decorators, { kind: "method", name: "create", static: false, private: false, access: { has: function (obj) { return "create" in obj; }, get: function (obj) { return obj.create; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _uploadResume_decorators, { kind: "method", name: "uploadResume", static: false, private: false, access: { has: function (obj) { return "uploadResume" in obj; }, get: function (obj) { return obj.uploadResume; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _findAll_decorators, { kind: "method", name: "findAll", static: false, private: false, access: { has: function (obj) { return "findAll" in obj; }, get: function (obj) { return obj.findAll; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _getMyResumes_decorators, { kind: "method", name: "getMyResumes", static: false, private: false, access: { has: function (obj) { return "getMyResumes" in obj; }, get: function (obj) { return obj.getMyResumes; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _findByUser_decorators, { kind: "method", name: "findByUser", static: false, private: false, access: { has: function (obj) { return "findByUser" in obj; }, get: function (obj) { return obj.findByUser; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _getMyStats_decorators, { kind: "method", name: "getMyStats", static: false, private: false, access: { has: function (obj) { return "getMyStats" in obj; }, get: function (obj) { return obj.getMyStats; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _findOne_decorators, { kind: "method", name: "findOne", static: false, private: false, access: { has: function (obj) { return "findOne" in obj; }, get: function (obj) { return obj.findOne; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _update_decorators, { kind: "method", name: "update", static: false, private: false, access: { has: function (obj) { return "update" in obj; }, get: function (obj) { return obj.update; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _remove_decorators, { kind: "method", name: "remove", static: false, private: false, access: { has: function (obj) { return "remove" in obj; }, get: function (obj) { return obj.remove; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(null, _classDescriptor = { value: _classThis }, _classDecorators, { kind: "class", name: _classThis.name, metadata: _metadata }, null, _classExtraInitializers);
        ResumesController = _classThis = _classDescriptor.value;
        if (_metadata) Object.defineProperty(_classThis, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
        __runInitializers(_classThis, _classExtraInitializers);
    })();
    return ResumesController = _classThis;
}();
exports.ResumesController = ResumesController;
