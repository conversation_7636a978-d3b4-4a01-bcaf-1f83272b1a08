import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsN<PERSON>ber } from 'class-validator';

export class CreateResumeDto {
  @ApiProperty({ example: 1, description: 'User ID' })
  @IsNotEmpty()
  @IsNumber()
  user_id: number;

  @ApiProperty({ example: 'john_doe_resume.pdf', description: 'Resume file name' })
  @IsNotEmpty()
  @IsString()
  file_name: string;

  @ApiProperty({ example: '/uploads/resumes/john_doe_resume.pdf', description: 'File path' })
  @IsNotEmpty()
  @IsString()
  file_path: string;
}
