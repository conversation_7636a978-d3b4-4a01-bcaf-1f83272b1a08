"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
    function accept(f) { if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected"); return f; }
    var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
    var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
    var _, done = false;
    for (var i = decorators.length - 1; i >= 0; i--) {
        var context = {};
        for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
        for (var p in contextIn.access) context.access[p] = contextIn.access[p];
        context.addInitializer = function (f) { if (done) throw new TypeError("Cannot add initializers after decoration has completed"); extraInitializers.push(accept(f || null)); };
        var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
        if (kind === "accessor") {
            if (result === void 0) continue;
            if (result === null || typeof result !== "object") throw new TypeError("Object expected");
            if (_ = accept(result.get)) descriptor.get = _;
            if (_ = accept(result.set)) descriptor.set = _;
            if (_ = accept(result.init)) initializers.unshift(_);
        }
        else if (_ = accept(result)) {
            if (kind === "field") initializers.unshift(_);
            else descriptor[key] = _;
        }
    }
    if (target) Object.defineProperty(target, contextIn.name, descriptor);
    done = true;
};
var __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {
    var useValue = arguments.length > 2;
    for (var i = 0; i < initializers.length; i++) {
        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
    }
    return useValue ? value : void 0;
};
var __setFunctionName = (this && this.__setFunctionName) || function (f, name, prefix) {
    if (typeof name === "symbol") name = name.description ? "[".concat(name.description, "]") : "";
    return Object.defineProperty(f, "name", { configurable: true, value: prefix ? "".concat(prefix, " ", name) : name });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Resume = void 0;
var openapi = require("@nestjs/swagger");
var typeorm_1 = require("typeorm");
var base_entity_1 = require("../../common/entities/base.entity");
var user_entity_1 = require("../../users/entities/user.entity");
var application_entity_1 = require("../../applications/entities/application.entity");
var Resume = function () {
    var _classDecorators = [(0, typeorm_1.Entity)('resumes')];
    var _classDescriptor;
    var _classExtraInitializers = [];
    var _classThis;
    var _classSuper = base_entity_1.BaseEntity;
    var _resume_id_decorators;
    var _resume_id_initializers = [];
    var _resume_id_extraInitializers = [];
    var _user_id_decorators;
    var _user_id_initializers = [];
    var _user_id_extraInitializers = [];
    var _file_name_decorators;
    var _file_name_initializers = [];
    var _file_name_extraInitializers = [];
    var _file_path_decorators;
    var _file_path_initializers = [];
    var _file_path_extraInitializers = [];
    var _upload_time_decorators;
    var _upload_time_initializers = [];
    var _upload_time_extraInitializers = [];
    var _user_decorators;
    var _user_initializers = [];
    var _user_extraInitializers = [];
    var _applications_decorators;
    var _applications_initializers = [];
    var _applications_extraInitializers = [];
    var Resume = _classThis = /** @class */ (function (_super) {
        __extends(Resume_1, _super);
        function Resume_1() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.resume_id = __runInitializers(_this, _resume_id_initializers, void 0);
            _this.user_id = (__runInitializers(_this, _resume_id_extraInitializers), __runInitializers(_this, _user_id_initializers, void 0));
            _this.file_name = (__runInitializers(_this, _user_id_extraInitializers), __runInitializers(_this, _file_name_initializers, void 0));
            _this.file_path = (__runInitializers(_this, _file_name_extraInitializers), __runInitializers(_this, _file_path_initializers, void 0));
            _this.upload_time = (__runInitializers(_this, _file_path_extraInitializers), __runInitializers(_this, _upload_time_initializers, void 0));
            // Relations
            _this.user = (__runInitializers(_this, _upload_time_extraInitializers), __runInitializers(_this, _user_initializers, void 0));
            _this.applications = (__runInitializers(_this, _user_extraInitializers), __runInitializers(_this, _applications_initializers, void 0));
            __runInitializers(_this, _applications_extraInitializers);
            return _this;
        }
        Resume_1._OPENAPI_METADATA_FACTORY = function () {
            return { resume_id: { required: true, type: function () { return Number; } }, user_id: { required: true, type: function () { return Number; } }, file_name: { required: true, type: function () { return String; } }, file_path: { required: true, type: function () { return String; } }, upload_time: { required: true, type: function () { return Date; } }, user: { required: true, type: function () { return require("../../users/entities/user.entity").User; } }, applications: { required: true, type: function () { return [require("../../applications/entities/application.entity").Application]; } } };
        };
        return Resume_1;
    }(_classSuper));
    __setFunctionName(_classThis, "Resume");
    (function () {
        var _a;
        var _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create((_a = _classSuper[Symbol.metadata]) !== null && _a !== void 0 ? _a : null) : void 0;
        _resume_id_decorators = [(0, typeorm_1.Column)({ name: 'resume_id' })];
        _user_id_decorators = [(0, typeorm_1.Column)({ name: 'user_id' })];
        _file_name_decorators = [(0, typeorm_1.Column)({ name: 'file_name', length: 255 })];
        _file_path_decorators = [(0, typeorm_1.Column)({ name: 'file_path', length: 500 })];
        _upload_time_decorators = [(0, typeorm_1.Column)({ name: 'upload_time', type: 'timestamp', default: function () { return 'CURRENT_TIMESTAMP'; } })];
        _user_decorators = [(0, typeorm_1.ManyToOne)(function () { return user_entity_1.User; }, function (user) { return user.resumes; }, { onDelete: 'CASCADE' }), (0, typeorm_1.JoinColumn)({ name: 'user_id' })];
        _applications_decorators = [(0, typeorm_1.OneToMany)(function () { return application_entity_1.Application; }, function (application) { return application.resume; })];
        __esDecorate(null, null, _resume_id_decorators, { kind: "field", name: "resume_id", static: false, private: false, access: { has: function (obj) { return "resume_id" in obj; }, get: function (obj) { return obj.resume_id; }, set: function (obj, value) { obj.resume_id = value; } }, metadata: _metadata }, _resume_id_initializers, _resume_id_extraInitializers);
        __esDecorate(null, null, _user_id_decorators, { kind: "field", name: "user_id", static: false, private: false, access: { has: function (obj) { return "user_id" in obj; }, get: function (obj) { return obj.user_id; }, set: function (obj, value) { obj.user_id = value; } }, metadata: _metadata }, _user_id_initializers, _user_id_extraInitializers);
        __esDecorate(null, null, _file_name_decorators, { kind: "field", name: "file_name", static: false, private: false, access: { has: function (obj) { return "file_name" in obj; }, get: function (obj) { return obj.file_name; }, set: function (obj, value) { obj.file_name = value; } }, metadata: _metadata }, _file_name_initializers, _file_name_extraInitializers);
        __esDecorate(null, null, _file_path_decorators, { kind: "field", name: "file_path", static: false, private: false, access: { has: function (obj) { return "file_path" in obj; }, get: function (obj) { return obj.file_path; }, set: function (obj, value) { obj.file_path = value; } }, metadata: _metadata }, _file_path_initializers, _file_path_extraInitializers);
        __esDecorate(null, null, _upload_time_decorators, { kind: "field", name: "upload_time", static: false, private: false, access: { has: function (obj) { return "upload_time" in obj; }, get: function (obj) { return obj.upload_time; }, set: function (obj, value) { obj.upload_time = value; } }, metadata: _metadata }, _upload_time_initializers, _upload_time_extraInitializers);
        __esDecorate(null, null, _user_decorators, { kind: "field", name: "user", static: false, private: false, access: { has: function (obj) { return "user" in obj; }, get: function (obj) { return obj.user; }, set: function (obj, value) { obj.user = value; } }, metadata: _metadata }, _user_initializers, _user_extraInitializers);
        __esDecorate(null, null, _applications_decorators, { kind: "field", name: "applications", static: false, private: false, access: { has: function (obj) { return "applications" in obj; }, get: function (obj) { return obj.applications; }, set: function (obj, value) { obj.applications = value; } }, metadata: _metadata }, _applications_initializers, _applications_extraInitializers);
        __esDecorate(null, _classDescriptor = { value: _classThis }, _classDecorators, { kind: "class", name: _classThis.name, metadata: _metadata }, null, _classExtraInitializers);
        Resume = _classThis = _classDescriptor.value;
        if (_metadata) Object.defineProperty(_classThis, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
        __runInitializers(_classThis, _classExtraInitializers);
    })();
    return Resume = _classThis;
}();
exports.Resume = Resume;
