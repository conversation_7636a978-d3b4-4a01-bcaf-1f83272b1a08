import { <PERSON><PERSON><PERSON>, Col<PERSON><PERSON>, ManyToOne, OneToMany, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { BaseEntity } from '../../common/entities/base.entity';
import { User } from '../../users/entities/user.entity';
import { Application } from '../../applications/entities/application.entity';

@Entity('resumes')
export class Resume extends BaseEntity {
  @Column({ name: 'resume_id' })
  resume_id: number;

  @Column({ name: 'user_id' })
  user_id: number;

  @Column({ name: 'file_name', length: 255 })
  file_name: string;

  @Column({ name: 'file_path', length: 500 })
  file_path: string;

  @Column({ name: 'upload_time', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  upload_time: Date;

  // Relations
  @ManyToOne(() => User, (user) => user.resumes, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @OneToMany(() => Application, (application) => application.resume)
  applications: Application[];
}
