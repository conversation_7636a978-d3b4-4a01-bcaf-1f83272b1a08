import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ParseIntPipe,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiConsumes } from '@nestjs/swagger';
import { diskStorage } from 'multer';
import { extname } from 'path';

import { ResumesService } from './resumes.service';
import { CreateResumeDto } from './dto/create-resume.dto';
import { UpdateResumeDto } from './dto/update-resume.dto';
import { ResponseMessage } from '../common/decorators/response-message.decorator';
import { User } from '../common/decorators/user.decorator';
import { IUser } from '../auth/interfaces/user.interface';

@ApiTags('resumes')
@ApiBearerAuth('access-token')
@Controller('resumes')
export class ResumesController {
  constructor(private readonly resumesService: ResumesService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new resume record' })
  @ApiResponse({ status: 201, description: 'Resume created successfully' })
  @ResponseMessage('Resume created successfully')
  create(@Body() createResumeDto: CreateResumeDto) {
    return this.resumesService.create(createResumeDto);
  }

  @Post('upload')
  @UseInterceptors(
    FileInterceptor('file', {
      storage: diskStorage({
        destination: './uploads/resumes',
        filename: (req, file, cb) => {
          const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
          cb(null, `${file.fieldname}-${uniqueSuffix}${extname(file.originalname)}`);
        },
      }),
      fileFilter: (req, file, cb) => {
        if (file.mimetype.match(/\/(pdf|doc|docx)$/)) {
          cb(null, true);
        } else {
          cb(new BadRequestException('Only PDF, DOC, and DOCX files are allowed'), false);
        }
      },
      limits: {
        fileSize: 5 * 1024 * 1024, // 5MB
      },
    }),
  )
  @ApiOperation({ summary: 'Upload a resume file' })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({ status: 201, description: 'Resume uploaded successfully' })
  @ResponseMessage('Resume uploaded successfully')
  uploadResume(
    @User() user: IUser,
    @UploadedFile() file: Express.Multer.File,
  ) {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }
    return this.resumesService.uploadResume(user.id, file);
  }

  @Get()
  @ApiOperation({ summary: 'Get all resumes' })
  @ApiResponse({ status: 200, description: 'Resumes retrieved successfully' })
  @ResponseMessage('Resumes retrieved successfully')
  findAll() {
    return this.resumesService.findAll();
  }

  @Get('my-resumes')
  @ApiOperation({ summary: 'Get current user resumes' })
  @ApiResponse({ status: 200, description: 'User resumes retrieved successfully' })
  @ResponseMessage('User resumes retrieved successfully')
  getMyResumes(@User() user: IUser) {
    return this.resumesService.findByUser(user.id);
  }

  @Get('user/:userId')
  @ApiOperation({ summary: 'Get resumes by user ID' })
  @ApiResponse({ status: 200, description: 'User resumes retrieved successfully' })
  @ResponseMessage('User resumes retrieved successfully')
  findByUser(@Param('userId', ParseIntPipe) userId: number) {
    return this.resumesService.findByUser(userId);
  }

  @Get('stats')
  @ApiOperation({ summary: 'Get current user resume statistics' })
  @ApiResponse({ status: 200, description: 'Resume statistics retrieved successfully' })
  @ResponseMessage('Resume statistics retrieved successfully')
  getMyStats(@User() user: IUser) {
    return this.resumesService.getResumeStats(user.id);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get resume by ID' })
  @ApiResponse({ status: 200, description: 'Resume retrieved successfully' })
  @ResponseMessage('Resume retrieved successfully')
  findOne(@Param('id', ParseIntPipe) id: number) {
    return this.resumesService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update resume by ID' })
  @ApiResponse({ status: 200, description: 'Resume updated successfully' })
  @ResponseMessage('Resume updated successfully')
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateResumeDto: UpdateResumeDto,
  ) {
    return this.resumesService.update(id, updateResumeDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete resume by ID' })
  @ApiResponse({ status: 200, description: 'Resume deleted successfully' })
  @ResponseMessage('Resume deleted successfully')
  remove(@Param('id', ParseIntPipe) id: number) {
    return this.resumesService.remove(id);
  }
}
