import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { unlink } from 'fs/promises';
import { join } from 'path';

import { Resume } from './entities/resume.entity';
import { CreateResumeDto } from './dto/create-resume.dto';
import { UpdateResumeDto } from './dto/update-resume.dto';
import { UsersService } from '../users/users.service';

@Injectable()
export class ResumesService {
  constructor(
    @InjectRepository(Resume)
    private resumeRepository: Repository<Resume>,
    private usersService: UsersService,
  ) {}

  async create(createResumeDto: CreateResumeDto): Promise<Resume> {
    // Verify user exists
    await this.usersService.findOne(createResumeDto.user_id);

    const resume = this.resumeRepository.create({
      ...createResumeDto,
      resume_id: Math.floor(Math.random() * 1000000),
    });

    return this.resumeRepository.save(resume);
  }

  async uploadResume(userId: number, file: Express.Multer.File): Promise<Resume> {
    // Verify user exists
    await this.usersService.findOne(userId);

    const filePath = `/uploads/resumes/${file.filename}`;
    
    const createResumeDto: CreateResumeDto = {
      user_id: userId,
      file_name: file.originalname,
      file_path: filePath,
    };

    return this.create(createResumeDto);
  }

  async findAll(): Promise<Resume[]> {
    return this.resumeRepository.find({
      relations: ['user'],
      order: { upload_time: 'DESC' },
    });
  }

  async findByUser(userId: number): Promise<Resume[]> {
    return this.resumeRepository.find({
      where: { user_id: userId },
      relations: ['user'],
      order: { upload_time: 'DESC' },
    });
  }

  async findOne(id: number): Promise<Resume> {
    const resume = await this.resumeRepository.findOne({
      where: { id },
      relations: ['user', 'applications'],
    });

    if (!resume) {
      throw new NotFoundException('Resume not found');
    }

    return resume;
  }

  async update(id: number, updateResumeDto: UpdateResumeDto): Promise<Resume> {
    const resume = await this.findOne(id);

    if (updateResumeDto.user_id && updateResumeDto.user_id !== resume.user_id) {
      // Verify new user exists
      await this.usersService.findOne(updateResumeDto.user_id);
    }

    await this.resumeRepository.update(id, updateResumeDto);
    return this.findOne(id);
  }

  async remove(id: number): Promise<void> {
    const resume = await this.findOne(id);
    
    // Delete file from filesystem
    try {
      const fullPath = join(process.cwd(), 'uploads', resume.file_path.replace('/uploads/', ''));
      await unlink(fullPath);
    } catch (error) {
      console.warn(`Could not delete file: ${resume.file_path}`, error);
    }

    await this.resumeRepository.remove(resume);
  }

  async getResumeStats(userId: number) {
    const resumes = await this.findByUser(userId);
    
    const totalApplications = await this.resumeRepository
      .createQueryBuilder('resume')
      .leftJoin('resume.applications', 'applications')
      .where('resume.user_id = :userId', { userId })
      .getCount();

    return {
      total_resumes: resumes.length,
      total_applications: totalApplications,
      resumes,
    };
  }
}
