import { Controller, Get, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../guards/roles.guard';
import { Roles } from '../decorators/roles.decorator';
import { UserRole } from '../enums/user-role.enum';

/**
 * Example usage of Role-based Authorization
 * 
 * This is just an example file showing how to use roles in controllers.
 * You can apply these patterns to your actual controllers.
 */

@ApiTags('example')
@ApiBearerAuth('access-token')
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('example')
export class ExampleController {

  // Only ADMIN can access
  @Roles(UserRole.ADMIN)
  @Get('admin-only')
  adminOnly() {
    return { message: 'Only admin can see this' };
  }

  // Only HR can access
  @Roles(UserRole.HR)
  @Post('hr-only')
  hrOnly() {
    return { message: 'Only H<PERSON> can do this' };
  }

  // Both ADMIN and HR can access
  @Roles(UserRole.ADMIN, UserRole.HR)
  @Get('admin-or-hr')
  adminOrHr() {
    return { message: 'Admin or HR can see this' };
  }

  // All authenticated users can access (no @Roles decorator)
  @Get('all-users')
  allUsers() {
    return { message: 'All authenticated users can see this' };
  }
}

/**
 * Role Definitions:
 * 
 * - UserRole.ADMIN: Full system access
 * - UserRole.HR: Company management, job posting, application review
 * - UserRole.JOB_SEEKER: Job search, application, profile management
 */
