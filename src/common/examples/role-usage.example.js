"use strict";
var __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {
    var useValue = arguments.length > 2;
    for (var i = 0; i < initializers.length; i++) {
        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
    }
    return useValue ? value : void 0;
};
var __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
    function accept(f) { if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected"); return f; }
    var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
    var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
    var _, done = false;
    for (var i = decorators.length - 1; i >= 0; i--) {
        var context = {};
        for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
        for (var p in contextIn.access) context.access[p] = contextIn.access[p];
        context.addInitializer = function (f) { if (done) throw new TypeError("Cannot add initializers after decoration has completed"); extraInitializers.push(accept(f || null)); };
        var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
        if (kind === "accessor") {
            if (result === void 0) continue;
            if (result === null || typeof result !== "object") throw new TypeError("Object expected");
            if (_ = accept(result.get)) descriptor.get = _;
            if (_ = accept(result.set)) descriptor.set = _;
            if (_ = accept(result.init)) initializers.unshift(_);
        }
        else if (_ = accept(result)) {
            if (kind === "field") initializers.unshift(_);
            else descriptor[key] = _;
        }
    }
    if (target) Object.defineProperty(target, contextIn.name, descriptor);
    done = true;
};
var __setFunctionName = (this && this.__setFunctionName) || function (f, name, prefix) {
    if (typeof name === "symbol") name = name.description ? "[".concat(name.description, "]") : "";
    return Object.defineProperty(f, "name", { configurable: true, value: prefix ? "".concat(prefix, " ", name) : name });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExampleController = void 0;
var common_1 = require("@nestjs/common");
var swagger_1 = require("@nestjs/swagger");
var jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
var roles_guard_1 = require("../guards/roles.guard");
var roles_decorator_1 = require("../decorators/roles.decorator");
var user_role_enum_1 = require("../enums/user-role.enum");
/**
 * Example usage of Role-based Authorization
 *
 * This is just an example file showing how to use roles in controllers.
 * You can apply these patterns to your actual controllers.
 */
var ExampleController = function () {
    var _classDecorators = [(0, swagger_1.ApiTags)('example'), (0, swagger_1.ApiBearerAuth)('access-token'), (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard), (0, common_1.Controller)('example')];
    var _classDescriptor;
    var _classExtraInitializers = [];
    var _classThis;
    var _instanceExtraInitializers = [];
    var _adminOnly_decorators;
    var _hrOnly_decorators;
    var _adminOrHr_decorators;
    var _allUsers_decorators;
    var ExampleController = _classThis = /** @class */ (function () {
        function ExampleController_1() {
            __runInitializers(this, _instanceExtraInitializers);
        }
        // Only ADMIN can access
        ExampleController_1.prototype.adminOnly = function () {
            return { message: 'Only admin can see this' };
        };
        // Only HR can access
        ExampleController_1.prototype.hrOnly = function () {
            return { message: 'Only HR can do this' };
        };
        // Both ADMIN and HR can access
        ExampleController_1.prototype.adminOrHr = function () {
            return { message: 'Admin or HR can see this' };
        };
        // All authenticated users can access (no @Roles decorator)
        ExampleController_1.prototype.allUsers = function () {
            return { message: 'All authenticated users can see this' };
        };
        return ExampleController_1;
    }());
    __setFunctionName(_classThis, "ExampleController");
    (function () {
        var _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(null) : void 0;
        _adminOnly_decorators = [(0, roles_decorator_1.Roles)(user_role_enum_1.UserRole.ADMIN), (0, common_1.Get)('admin-only')];
        _hrOnly_decorators = [(0, roles_decorator_1.Roles)(user_role_enum_1.UserRole.HR), (0, common_1.Post)('hr-only')];
        _adminOrHr_decorators = [(0, roles_decorator_1.Roles)(user_role_enum_1.UserRole.ADMIN, user_role_enum_1.UserRole.HR), (0, common_1.Get)('admin-or-hr')];
        _allUsers_decorators = [(0, common_1.Get)('all-users')];
        __esDecorate(_classThis, null, _adminOnly_decorators, { kind: "method", name: "adminOnly", static: false, private: false, access: { has: function (obj) { return "adminOnly" in obj; }, get: function (obj) { return obj.adminOnly; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _hrOnly_decorators, { kind: "method", name: "hrOnly", static: false, private: false, access: { has: function (obj) { return "hrOnly" in obj; }, get: function (obj) { return obj.hrOnly; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _adminOrHr_decorators, { kind: "method", name: "adminOrHr", static: false, private: false, access: { has: function (obj) { return "adminOrHr" in obj; }, get: function (obj) { return obj.adminOrHr; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _allUsers_decorators, { kind: "method", name: "allUsers", static: false, private: false, access: { has: function (obj) { return "allUsers" in obj; }, get: function (obj) { return obj.allUsers; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(null, _classDescriptor = { value: _classThis }, _classDecorators, { kind: "class", name: _classThis.name, metadata: _metadata }, null, _classExtraInitializers);
        ExampleController = _classThis = _classDescriptor.value;
        if (_metadata) Object.defineProperty(_classThis, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
        __runInitializers(_classThis, _classExtraInitializers);
    })();
    return ExampleController = _classThis;
}();
exports.ExampleController = ExampleController;
/**
 * Role Definitions:
 *
 * - UserRole.ADMIN: Full system access
 * - UserRole.HR: Company management, job posting, application review
 * - UserRole.JOB_SEEKER: Job search, application, profile management
 */
