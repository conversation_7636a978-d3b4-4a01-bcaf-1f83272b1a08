import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ParseIntPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { SavedJobsService } from './saved-jobs.service';
import { CreateSavedJobDto } from './dto/create-saved-job.dto';
import { UpdateSavedJobDto } from './dto/update-saved-job.dto';
import { SavedJob } from './entities/saved-job.entity';
import { User } from '../common/decorators/user.decorator';
import { IUser } from '../common/interfaces/user.interface';

@ApiTags('saved-jobs')
@Controller('saved-jobs')
export class SavedJobsController {
  constructor(private readonly savedJobsService: SavedJobsService) {}

  @Post()
  @ApiOperation({ summary: 'Save a job' })
  @ApiResponse({ status: 201, description: 'Job saved successfully', type: SavedJob })
  @ApiResponse({ status: 409, description: 'Job is already saved' })
  create(
    @Body() createSavedJobDto: CreateSavedJobDto,
    @User() user: IUser,
  ): Promise<SavedJob> {
    return this.savedJobsService.create(createSavedJobDto, user.user_id);
  }

  @Get('my-saved-jobs')
  @ApiOperation({ summary: 'Get current user saved jobs' })
  @ApiResponse({ status: 200, description: 'User saved jobs', type: [SavedJob] })
  findMySavedJobs(@User() user: IUser): Promise<SavedJob[]> {
    return this.savedJobsService.findByUser(user.user_id);
  }

  @Get('count')
  @ApiOperation({ summary: 'Get saved jobs count for current user' })
  @ApiResponse({ status: 200, description: 'Saved jobs count' })
  getSavedJobsCount(@User() user: IUser): Promise<number> {
    return this.savedJobsService.getSavedJobsCount(user.user_id);
  }

  @Get('check/:jobId')
  @ApiOperation({ summary: 'Check if job is saved by current user' })
  @ApiResponse({ status: 200, description: 'Job saved status' })
  checkIfJobSaved(
    @Param('jobId', ParseIntPipe) jobId: number,
    @User() user: IUser,
  ): Promise<boolean> {
    return this.savedJobsService.isJobSavedByUser(jobId, user.user_id);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get saved job by ID' })
  @ApiResponse({ status: 200, description: 'Saved job found', type: SavedJob })
  @ApiResponse({ status: 404, description: 'Saved job not found' })
  findOne(@Param('id', ParseIntPipe) id: number): Promise<SavedJob> {
    return this.savedJobsService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update saved job by ID' })
  @ApiResponse({ status: 200, description: 'Saved job updated successfully', type: SavedJob })
  @ApiResponse({ status: 404, description: 'Saved job not found' })
  @ApiResponse({ status: 403, description: 'Forbidden - can only update own saved jobs' })
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateSavedJobDto: UpdateSavedJobDto,
    @User() user: IUser,
  ): Promise<SavedJob> {
    return this.savedJobsService.update(id, updateSavedJobDto, user.user_id);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Remove saved job by ID' })
  @ApiResponse({ status: 200, description: 'Saved job removed successfully' })
  @ApiResponse({ status: 404, description: 'Saved job not found' })
  @ApiResponse({ status: 403, description: 'Forbidden - can only delete own saved jobs' })
  remove(
    @Param('id', ParseIntPipe) id: number,
    @User() user: IUser,
  ): Promise<void> {
    return this.savedJobsService.remove(id, user.user_id);
  }

  @Delete('job/:jobId')
  @ApiOperation({ summary: 'Remove saved job by job ID' })
  @ApiResponse({ status: 200, description: 'Saved job removed successfully' })
  @ApiResponse({ status: 404, description: 'Saved job not found' })
  removeByJobId(
    @Param('jobId', ParseIntPipe) jobId: number,
    @User() user: IUser,
  ): Promise<void> {
    return this.savedJobsService.removeByJobId(jobId, user.user_id);
  }
}
