import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  ParseIntPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';

import { SavedJobsService } from './saved-jobs.service';
import { CreateSavedJobDto } from './dto/create-saved-job.dto';
import { ResponseMessage } from '../common/decorators/response-message.decorator';
import { User } from '../common/decorators/user.decorator';
import { IUser } from '../auth/interfaces/user.interface';

@ApiTags('saved-jobs')
@ApiBearerAuth('access-token')
@Controller('saved-jobs')
export class SavedJobsController {
  constructor(private readonly savedJobsService: SavedJobsService) {}

  @Post()
  @ApiOperation({ summary: 'Save a job' })
  @ApiResponse({ status: 201, description: 'Job saved successfully' })
  @ResponseMessage('Job saved successfully')
  create(@Body() createSavedJobDto: CreateSavedJobDto) {
    return this.savedJobsService.create(createSavedJobDto);
  }

  @Post(':jobId')
  @ApiOperation({ summary: 'Save a job by job ID' })
  @ApiResponse({ status: 201, description: 'Job saved successfully' })
  @ResponseMessage('Job saved successfully')
  saveJob(@User() user: IUser, @Param('jobId', ParseIntPipe) jobId: number) {
    return this.savedJobsService.create({ user_id: user.id, job_id: jobId });
  }

  @Get('my-saved-jobs')
  @ApiOperation({ summary: 'Get current user saved jobs' })
  @ApiResponse({ status: 200, description: 'Saved jobs retrieved successfully' })
  @ResponseMessage('Saved jobs retrieved successfully')
  getMySavedJobs(@User() user: IUser) {
    return this.savedJobsService.findByUser(user.id);
  }

  @Get('user/:userId')
  @ApiOperation({ summary: 'Get saved jobs by user ID' })
  @ApiResponse({ status: 200, description: 'User saved jobs retrieved successfully' })
  @ResponseMessage('User saved jobs retrieved successfully')
  findByUser(@Param('userId', ParseIntPipe) userId: number) {
    return this.savedJobsService.findByUser(userId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get saved job by ID' })
  @ApiResponse({ status: 200, description: 'Saved job retrieved successfully' })
  @ResponseMessage('Saved job retrieved successfully')
  findOne(@Param('id', ParseIntPipe) id: number) {
    return this.savedJobsService.findOne(id);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Remove saved job by ID' })
  @ApiResponse({ status: 200, description: 'Saved job removed successfully' })
  @ResponseMessage('Saved job removed successfully')
  remove(@Param('id', ParseIntPipe) id: number) {
    return this.savedJobsService.remove(id);
  }

  @Delete('job/:jobId')
  @ApiOperation({ summary: 'Unsave a job by job ID' })
  @ApiResponse({ status: 200, description: 'Job unsaved successfully' })
  @ResponseMessage('Job unsaved successfully')
  unsaveJob(@User() user: IUser, @Param('jobId', ParseIntPipe) jobId: number) {
    return this.savedJobsService.removeByUserAndJob(user.id, jobId);
  }
}
