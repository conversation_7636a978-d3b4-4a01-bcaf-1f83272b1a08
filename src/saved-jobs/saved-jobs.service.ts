import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { SavedJob } from './entities/saved-job.entity';
import { CreateSavedJobDto } from './dto/create-saved-job.dto';
import { JobsService } from '../jobs/jobs.service';
import { UsersService } from '../users/users.service';

@Injectable()
export class SavedJobsService {
  constructor(
    @InjectRepository(SavedJob)
    private savedJobRepository: Repository<SavedJob>,
    private jobsService: JobsService,
    private usersService: UsersService,
  ) {}

  async create(createSavedJobDto: CreateSavedJobDto): Promise<SavedJob> {
    // Verify user and job exist
    await this.usersService.findOne(createSavedJobDto.user_id);
    await this.jobsService.findOne(createSavedJobDto.job_id);

    // Check if already saved
    const existingSavedJob = await this.savedJobRepository.findOne({
      where: {
        user_id: createSavedJobDto.user_id,
        job_id: createSavedJobDto.job_id,
      },
    });

    if (existingSavedJob) {
      throw new ConflictException('Job already saved by user');
    }

    const savedJob = this.savedJobRepository.create({
      ...createSavedJobDto,
      saved_job_id: Math.floor(Math.random() * 1000000),
    });

    // Increment save count on job
    await this.jobsService.incrementSaveCount(createSavedJobDto.job_id);

    return this.savedJobRepository.save(savedJob);
  }

  async findByUser(userId: number): Promise<SavedJob[]> {
    return this.savedJobRepository.find({
      where: { user_id: userId },
      relations: ['job_post', 'job_post.company'],
      order: { saved_at: 'DESC' },
    });
  }

  async findOne(id: number): Promise<SavedJob> {
    const savedJob = await this.savedJobRepository.findOne({
      where: { id },
      relations: ['user', 'job_post', 'job_post.company'],
    });

    if (!savedJob) {
      throw new NotFoundException('Saved job not found');
    }

    return savedJob;
  }

  async remove(id: number): Promise<void> {
    const savedJob = await this.findOne(id);
    
    // Decrement save count on job
    await this.jobsService.decrementSaveCount(savedJob.job_id);
    
    await this.savedJobRepository.remove(savedJob);
  }

  async removeByUserAndJob(userId: number, jobId: number): Promise<void> {
    const savedJob = await this.savedJobRepository.findOne({
      where: { user_id: userId, job_id: jobId },
    });

    if (!savedJob) {
      throw new NotFoundException('Saved job not found');
    }

    // Decrement save count on job
    await this.jobsService.decrementSaveCount(jobId);
    
    await this.savedJobRepository.remove(savedJob);
  }
}
