import {
  Injectable,
  NotFoundException,
  ConflictException,
  ForbiddenException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SavedJob } from './entities/saved-job.entity';
import { CreateSavedJobDto } from './dto/create-saved-job.dto';
import { UpdateSavedJobDto } from './dto/update-saved-job.dto';
import { JobsService } from '../jobs/jobs.service';

@Injectable()
export class SavedJobsService {
  constructor(
    @InjectRepository(SavedJob)
    private readonly savedJobRepository: Repository<SavedJob>,
    private readonly jobsService: JobsService,
  ) {}

  async create(createSavedJobDto: CreateSavedJobDto, userId: number): Promise<SavedJob> {
    // Verify job exists
    await this.jobsService.findOne(createSavedJobDto.job_id);

    // Check if job is already saved by user
    const existingSavedJob = await this.savedJobRepository.findOne({
      where: {
        user_id: userId,
        job_id: createSavedJobDto.job_id,
      },
    });

    if (existingSavedJob) {
      throw new ConflictException('Job is already saved');
    }

    // Create saved job
    const savedJob = this.savedJobRepository.create({
      ...createSavedJobDto,
      user_id: userId,
    });

    return this.savedJobRepository.save(savedJob);
  }

  async findByUser(userId: number): Promise<SavedJob[]> {
    return this.savedJobRepository.find({
      where: { user_id: userId },
      relations: ['job', 'job.company'],
      order: { saved_at: 'DESC' },
    });
  }

  async findOne(id: number): Promise<SavedJob> {
    const savedJob = await this.savedJobRepository.findOne({
      where: { saved_job_id: id },
      relations: ['user', 'job', 'job.company'],
    });

    if (!savedJob) {
      throw new NotFoundException(`Saved job with ID ${id} not found`);
    }

    return savedJob;
  }

  async update(id: number, updateSavedJobDto: UpdateSavedJobDto, userId: number): Promise<SavedJob> {
    const savedJob = await this.findOne(id);

    // Check if user owns this saved job
    if (savedJob.user_id !== userId) {
      throw new ForbiddenException('You can only update your own saved jobs');
    }

    Object.assign(savedJob, updateSavedJobDto);
    return this.savedJobRepository.save(savedJob);
  }

  async remove(id: number, userId: number): Promise<void> {
    const savedJob = await this.findOne(id);

    // Check if user owns this saved job
    if (savedJob.user_id !== userId) {
      throw new ForbiddenException('You can only delete your own saved jobs');
    }

    await this.savedJobRepository.remove(savedJob);
  }

  async removeByJobId(jobId: number, userId: number): Promise<void> {
    const savedJob = await this.savedJobRepository.findOne({
      where: {
        user_id: userId,
        job_id: jobId,
      },
    });

    if (!savedJob) {
      throw new NotFoundException('Saved job not found');
    }

    await this.savedJobRepository.remove(savedJob);
  }

  async isJobSavedByUser(jobId: number, userId: number): Promise<boolean> {
    const savedJob = await this.savedJobRepository.findOne({
      where: {
        user_id: userId,
        job_id: jobId,
      },
    });

    return !!savedJob;
  }

  async getSavedJobsCount(userId: number): Promise<number> {
    return this.savedJobRepository.count({
      where: { user_id: userId },
    });
  }
}
