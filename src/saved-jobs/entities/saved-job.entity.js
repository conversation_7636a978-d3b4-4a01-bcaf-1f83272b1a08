"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
    function accept(f) { if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected"); return f; }
    var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
    var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
    var _, done = false;
    for (var i = decorators.length - 1; i >= 0; i--) {
        var context = {};
        for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
        for (var p in contextIn.access) context.access[p] = contextIn.access[p];
        context.addInitializer = function (f) { if (done) throw new TypeError("Cannot add initializers after decoration has completed"); extraInitializers.push(accept(f || null)); };
        var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
        if (kind === "accessor") {
            if (result === void 0) continue;
            if (result === null || typeof result !== "object") throw new TypeError("Object expected");
            if (_ = accept(result.get)) descriptor.get = _;
            if (_ = accept(result.set)) descriptor.set = _;
            if (_ = accept(result.init)) initializers.unshift(_);
        }
        else if (_ = accept(result)) {
            if (kind === "field") initializers.unshift(_);
            else descriptor[key] = _;
        }
    }
    if (target) Object.defineProperty(target, contextIn.name, descriptor);
    done = true;
};
var __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {
    var useValue = arguments.length > 2;
    for (var i = 0; i < initializers.length; i++) {
        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
    }
    return useValue ? value : void 0;
};
var __setFunctionName = (this && this.__setFunctionName) || function (f, name, prefix) {
    if (typeof name === "symbol") name = name.description ? "[".concat(name.description, "]") : "";
    return Object.defineProperty(f, "name", { configurable: true, value: prefix ? "".concat(prefix, " ", name) : name });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SavedJob = void 0;
var openapi = require("@nestjs/swagger");
var typeorm_1 = require("typeorm");
var base_entity_1 = require("../../common/entities/base.entity");
var user_entity_1 = require("../../users/entities/user.entity");
var job_post_entity_1 = require("../../jobs/entities/job-post.entity");
var SavedJob = function () {
    var _classDecorators = [(0, typeorm_1.Entity)('saved_jobs'), (0, typeorm_1.Unique)(['user_id', 'job_id'])];
    var _classDescriptor;
    var _classExtraInitializers = [];
    var _classThis;
    var _classSuper = base_entity_1.BaseEntity;
    var _saved_job_id_decorators;
    var _saved_job_id_initializers = [];
    var _saved_job_id_extraInitializers = [];
    var _user_id_decorators;
    var _user_id_initializers = [];
    var _user_id_extraInitializers = [];
    var _job_id_decorators;
    var _job_id_initializers = [];
    var _job_id_extraInitializers = [];
    var _saved_at_decorators;
    var _saved_at_initializers = [];
    var _saved_at_extraInitializers = [];
    var _user_decorators;
    var _user_initializers = [];
    var _user_extraInitializers = [];
    var _job_post_decorators;
    var _job_post_initializers = [];
    var _job_post_extraInitializers = [];
    var SavedJob = _classThis = /** @class */ (function (_super) {
        __extends(SavedJob_1, _super);
        function SavedJob_1() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.saved_job_id = __runInitializers(_this, _saved_job_id_initializers, void 0);
            _this.user_id = (__runInitializers(_this, _saved_job_id_extraInitializers), __runInitializers(_this, _user_id_initializers, void 0));
            _this.job_id = (__runInitializers(_this, _user_id_extraInitializers), __runInitializers(_this, _job_id_initializers, void 0));
            _this.saved_at = (__runInitializers(_this, _job_id_extraInitializers), __runInitializers(_this, _saved_at_initializers, void 0));
            // Relations
            _this.user = (__runInitializers(_this, _saved_at_extraInitializers), __runInitializers(_this, _user_initializers, void 0));
            _this.job_post = (__runInitializers(_this, _user_extraInitializers), __runInitializers(_this, _job_post_initializers, void 0));
            __runInitializers(_this, _job_post_extraInitializers);
            return _this;
        }
        SavedJob_1._OPENAPI_METADATA_FACTORY = function () {
            return { saved_job_id: { required: true, type: function () { return Number; } }, user_id: { required: true, type: function () { return Number; } }, job_id: { required: true, type: function () { return Number; } }, saved_at: { required: true, type: function () { return Date; } }, user: { required: true, type: function () { return require("../../users/entities/user.entity").User; } }, job_post: { required: true, type: function () { return require("../../jobs/entities/job-post.entity").JobPost; } } };
        };
        return SavedJob_1;
    }(_classSuper));
    __setFunctionName(_classThis, "SavedJob");
    (function () {
        var _a;
        var _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create((_a = _classSuper[Symbol.metadata]) !== null && _a !== void 0 ? _a : null) : void 0;
        _saved_job_id_decorators = [(0, typeorm_1.Column)({ name: 'saved_job_id' })];
        _user_id_decorators = [(0, typeorm_1.Column)({ name: 'user_id' })];
        _job_id_decorators = [(0, typeorm_1.Column)({ name: 'job_id' })];
        _saved_at_decorators = [(0, typeorm_1.Column)({ name: 'saved_at', type: 'timestamp', default: function () { return 'CURRENT_TIMESTAMP'; } })];
        _user_decorators = [(0, typeorm_1.ManyToOne)(function () { return user_entity_1.User; }, function (user) { return user.saved_jobs; }, { onDelete: 'CASCADE' }), (0, typeorm_1.JoinColumn)({ name: 'user_id' })];
        _job_post_decorators = [(0, typeorm_1.ManyToOne)(function () { return job_post_entity_1.JobPost; }, function (jobPost) { return jobPost.saved_by_users; }, { onDelete: 'CASCADE' }), (0, typeorm_1.JoinColumn)({ name: 'job_id' })];
        __esDecorate(null, null, _saved_job_id_decorators, { kind: "field", name: "saved_job_id", static: false, private: false, access: { has: function (obj) { return "saved_job_id" in obj; }, get: function (obj) { return obj.saved_job_id; }, set: function (obj, value) { obj.saved_job_id = value; } }, metadata: _metadata }, _saved_job_id_initializers, _saved_job_id_extraInitializers);
        __esDecorate(null, null, _user_id_decorators, { kind: "field", name: "user_id", static: false, private: false, access: { has: function (obj) { return "user_id" in obj; }, get: function (obj) { return obj.user_id; }, set: function (obj, value) { obj.user_id = value; } }, metadata: _metadata }, _user_id_initializers, _user_id_extraInitializers);
        __esDecorate(null, null, _job_id_decorators, { kind: "field", name: "job_id", static: false, private: false, access: { has: function (obj) { return "job_id" in obj; }, get: function (obj) { return obj.job_id; }, set: function (obj, value) { obj.job_id = value; } }, metadata: _metadata }, _job_id_initializers, _job_id_extraInitializers);
        __esDecorate(null, null, _saved_at_decorators, { kind: "field", name: "saved_at", static: false, private: false, access: { has: function (obj) { return "saved_at" in obj; }, get: function (obj) { return obj.saved_at; }, set: function (obj, value) { obj.saved_at = value; } }, metadata: _metadata }, _saved_at_initializers, _saved_at_extraInitializers);
        __esDecorate(null, null, _user_decorators, { kind: "field", name: "user", static: false, private: false, access: { has: function (obj) { return "user" in obj; }, get: function (obj) { return obj.user; }, set: function (obj, value) { obj.user = value; } }, metadata: _metadata }, _user_initializers, _user_extraInitializers);
        __esDecorate(null, null, _job_post_decorators, { kind: "field", name: "job_post", static: false, private: false, access: { has: function (obj) { return "job_post" in obj; }, get: function (obj) { return obj.job_post; }, set: function (obj, value) { obj.job_post = value; } }, metadata: _metadata }, _job_post_initializers, _job_post_extraInitializers);
        __esDecorate(null, _classDescriptor = { value: _classThis }, _classDecorators, { kind: "class", name: _classThis.name, metadata: _metadata }, null, _classExtraInitializers);
        SavedJob = _classThis = _classDescriptor.value;
        if (_metadata) Object.defineProperty(_classThis, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
        __runInitializers(_classThis, _classExtraInitializers);
    })();
    return SavedJob = _classThis;
}();
exports.SavedJob = SavedJob;
