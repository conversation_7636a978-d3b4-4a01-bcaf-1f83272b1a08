import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, ManyToOne, CreateDateColumn, JoinColumn, Unique } from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { Job } from '../../jobs/entities/job.entity';

@Entity('saved_jobs')
@Unique(['user_id', 'job_id'])
export class SavedJob {
  @PrimaryGeneratedColumn()
  @Column({ name: 'saved_job_id' })
  saved_job_id: number;

  @Column({ name: 'notes', type: 'text', nullable: true })
  notes?: string;

  @CreateDateColumn({ name: 'saved_at' })
  saved_at: Date;

  // Relations
  @Column({ name: 'user_id' })
  user_id: number;

  @Column({ name: 'job_id' })
  job_id: number;

  @ManyToOne(() => User, { eager: true })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @ManyToOne(() => Job, { eager: true })
  @JoinColumn({ name: 'job_id' })
  job: Job;
}
