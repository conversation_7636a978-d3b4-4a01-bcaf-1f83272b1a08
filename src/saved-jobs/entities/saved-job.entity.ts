import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from 'typeorm';
import { BaseEntity } from '../../common/entities/base.entity';
import { User } from '../../users/entities/user.entity';
import { JobPost } from '../../jobs/entities/job-post.entity';

@Entity('saved_jobs')
@Unique(['user_id', 'job_id'])
export class SavedJob extends BaseEntity {
  @Column({ name: 'saved_job_id' })
  saved_job_id: number;

  @Column({ name: 'user_id' })
  user_id: number;

  @Column({ name: 'job_id' })
  job_id: number;

  @Column({ name: 'saved_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  saved_at: Date;

  // Relations
  @ManyToOne(() => User, (user) => user.saved_jobs, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @ManyToOne(() => JobPost, (jobPost) => jobPost.saved_by_users, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'job_id' })
  job_post: JobPost;
}
