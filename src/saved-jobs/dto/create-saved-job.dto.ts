import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';

export class CreateSavedJobDto {
  @ApiProperty({ example: 1, description: 'Job ID' })
  @IsNotEmpty()
  @IsNumber()
  job_id: number;

  @ApiProperty({ example: 'Interesting position for future reference', description: 'Notes about the saved job' })
  @IsOptional()
  @IsString()
  notes?: string;
}
