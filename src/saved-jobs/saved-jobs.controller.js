"use strict";
var __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {
    var useValue = arguments.length > 2;
    for (var i = 0; i < initializers.length; i++) {
        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
    }
    return useValue ? value : void 0;
};
var __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
    function accept(f) { if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected"); return f; }
    var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
    var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
    var _, done = false;
    for (var i = decorators.length - 1; i >= 0; i--) {
        var context = {};
        for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
        for (var p in contextIn.access) context.access[p] = contextIn.access[p];
        context.addInitializer = function (f) { if (done) throw new TypeError("Cannot add initializers after decoration has completed"); extraInitializers.push(accept(f || null)); };
        var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
        if (kind === "accessor") {
            if (result === void 0) continue;
            if (result === null || typeof result !== "object") throw new TypeError("Object expected");
            if (_ = accept(result.get)) descriptor.get = _;
            if (_ = accept(result.set)) descriptor.set = _;
            if (_ = accept(result.init)) initializers.unshift(_);
        }
        else if (_ = accept(result)) {
            if (kind === "field") initializers.unshift(_);
            else descriptor[key] = _;
        }
    }
    if (target) Object.defineProperty(target, contextIn.name, descriptor);
    done = true;
};
var __setFunctionName = (this && this.__setFunctionName) || function (f, name, prefix) {
    if (typeof name === "symbol") name = name.description ? "[".concat(name.description, "]") : "";
    return Object.defineProperty(f, "name", { configurable: true, value: prefix ? "".concat(prefix, " ", name) : name });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SavedJobsController = void 0;
var openapi = require("@nestjs/swagger");
var common_1 = require("@nestjs/common");
var swagger_1 = require("@nestjs/swagger");
var response_message_decorator_1 = require("../common/decorators/response-message.decorator");
var SavedJobsController = function () {
    var _classDecorators = [(0, swagger_1.ApiTags)('saved-jobs'), (0, swagger_1.ApiBearerAuth)('access-token'), (0, common_1.Controller)('saved-jobs')];
    var _classDescriptor;
    var _classExtraInitializers = [];
    var _classThis;
    var _instanceExtraInitializers = [];
    var _create_decorators;
    var _saveJob_decorators;
    var _getMySavedJobs_decorators;
    var _findByUser_decorators;
    var _findOne_decorators;
    var _remove_decorators;
    var _unsaveJob_decorators;
    var SavedJobsController = _classThis = /** @class */ (function () {
        function SavedJobsController_1(savedJobsService) {
            this.savedJobsService = (__runInitializers(this, _instanceExtraInitializers), savedJobsService);
        }
        SavedJobsController_1.prototype.create = function (createSavedJobDto) {
            return this.savedJobsService.create(createSavedJobDto);
        };
        SavedJobsController_1.prototype.saveJob = function (user, jobId) {
            return this.savedJobsService.create({ user_id: user.id, job_id: jobId });
        };
        SavedJobsController_1.prototype.getMySavedJobs = function (user) {
            return this.savedJobsService.findByUser(user.id);
        };
        SavedJobsController_1.prototype.findByUser = function (userId) {
            return this.savedJobsService.findByUser(userId);
        };
        SavedJobsController_1.prototype.findOne = function (id) {
            return this.savedJobsService.findOne(id);
        };
        SavedJobsController_1.prototype.remove = function (id) {
            return this.savedJobsService.remove(id);
        };
        SavedJobsController_1.prototype.unsaveJob = function (user, jobId) {
            return this.savedJobsService.removeByUserAndJob(user.id, jobId);
        };
        return SavedJobsController_1;
    }());
    __setFunctionName(_classThis, "SavedJobsController");
    (function () {
        var _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(null) : void 0;
        _create_decorators = [(0, common_1.Post)(), (0, swagger_1.ApiOperation)({ summary: 'Save a job' }), (0, swagger_1.ApiResponse)({ status: 201, description: 'Job saved successfully' }), (0, response_message_decorator_1.ResponseMessage)('Job saved successfully'), openapi.ApiResponse({ status: 201, type: require("./entities/saved-job.entity").SavedJob })];
        _saveJob_decorators = [(0, common_1.Post)(':jobId'), (0, swagger_1.ApiOperation)({ summary: 'Save a job by job ID' }), (0, swagger_1.ApiResponse)({ status: 201, description: 'Job saved successfully' }), (0, response_message_decorator_1.ResponseMessage)('Job saved successfully'), openapi.ApiResponse({ status: 201, type: require("./entities/saved-job.entity").SavedJob })];
        _getMySavedJobs_decorators = [(0, common_1.Get)('my-saved-jobs'), (0, swagger_1.ApiOperation)({ summary: 'Get current user saved jobs' }), (0, swagger_1.ApiResponse)({ status: 200, description: 'Saved jobs retrieved successfully' }), (0, response_message_decorator_1.ResponseMessage)('Saved jobs retrieved successfully'), openapi.ApiResponse({ status: 200, type: [require("./entities/saved-job.entity").SavedJob] })];
        _findByUser_decorators = [(0, common_1.Get)('user/:userId'), (0, swagger_1.ApiOperation)({ summary: 'Get saved jobs by user ID' }), (0, swagger_1.ApiResponse)({ status: 200, description: 'User saved jobs retrieved successfully' }), (0, response_message_decorator_1.ResponseMessage)('User saved jobs retrieved successfully'), openapi.ApiResponse({ status: 200, type: [require("./entities/saved-job.entity").SavedJob] })];
        _findOne_decorators = [(0, common_1.Get)(':id'), (0, swagger_1.ApiOperation)({ summary: 'Get saved job by ID' }), (0, swagger_1.ApiResponse)({ status: 200, description: 'Saved job retrieved successfully' }), (0, response_message_decorator_1.ResponseMessage)('Saved job retrieved successfully'), openapi.ApiResponse({ status: 200, type: require("./entities/saved-job.entity").SavedJob })];
        _remove_decorators = [(0, common_1.Delete)(':id'), (0, swagger_1.ApiOperation)({ summary: 'Remove saved job by ID' }), (0, swagger_1.ApiResponse)({ status: 200, description: 'Saved job removed successfully' }), (0, response_message_decorator_1.ResponseMessage)('Saved job removed successfully'), openapi.ApiResponse({ status: 200 })];
        _unsaveJob_decorators = [(0, common_1.Delete)('job/:jobId'), (0, swagger_1.ApiOperation)({ summary: 'Unsave a job by job ID' }), (0, swagger_1.ApiResponse)({ status: 200, description: 'Job unsaved successfully' }), (0, response_message_decorator_1.ResponseMessage)('Job unsaved successfully'), openapi.ApiResponse({ status: 200 })];
        __esDecorate(_classThis, null, _create_decorators, { kind: "method", name: "create", static: false, private: false, access: { has: function (obj) { return "create" in obj; }, get: function (obj) { return obj.create; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _saveJob_decorators, { kind: "method", name: "saveJob", static: false, private: false, access: { has: function (obj) { return "saveJob" in obj; }, get: function (obj) { return obj.saveJob; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _getMySavedJobs_decorators, { kind: "method", name: "getMySavedJobs", static: false, private: false, access: { has: function (obj) { return "getMySavedJobs" in obj; }, get: function (obj) { return obj.getMySavedJobs; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _findByUser_decorators, { kind: "method", name: "findByUser", static: false, private: false, access: { has: function (obj) { return "findByUser" in obj; }, get: function (obj) { return obj.findByUser; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _findOne_decorators, { kind: "method", name: "findOne", static: false, private: false, access: { has: function (obj) { return "findOne" in obj; }, get: function (obj) { return obj.findOne; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _remove_decorators, { kind: "method", name: "remove", static: false, private: false, access: { has: function (obj) { return "remove" in obj; }, get: function (obj) { return obj.remove; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _unsaveJob_decorators, { kind: "method", name: "unsaveJob", static: false, private: false, access: { has: function (obj) { return "unsaveJob" in obj; }, get: function (obj) { return obj.unsaveJob; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(null, _classDescriptor = { value: _classThis }, _classDecorators, { kind: "class", name: _classThis.name, metadata: _metadata }, null, _classExtraInitializers);
        SavedJobsController = _classThis = _classDescriptor.value;
        if (_metadata) Object.defineProperty(_classThis, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
        __runInitializers(_classThis, _classExtraInitializers);
    })();
    return SavedJobsController = _classThis;
}();
exports.SavedJobsController = SavedJobsController;
