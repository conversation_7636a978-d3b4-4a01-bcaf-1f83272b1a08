"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
    function accept(f) { if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected"); return f; }
    var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
    var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
    var _, done = false;
    for (var i = decorators.length - 1; i >= 0; i--) {
        var context = {};
        for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
        for (var p in contextIn.access) context.access[p] = contextIn.access[p];
        context.addInitializer = function (f) { if (done) throw new TypeError("Cannot add initializers after decoration has completed"); extraInitializers.push(accept(f || null)); };
        var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
        if (kind === "accessor") {
            if (result === void 0) continue;
            if (result === null || typeof result !== "object") throw new TypeError("Object expected");
            if (_ = accept(result.get)) descriptor.get = _;
            if (_ = accept(result.set)) descriptor.set = _;
            if (_ = accept(result.init)) initializers.unshift(_);
        }
        else if (_ = accept(result)) {
            if (kind === "field") initializers.unshift(_);
            else descriptor[key] = _;
        }
    }
    if (target) Object.defineProperty(target, contextIn.name, descriptor);
    done = true;
};
var __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {
    var useValue = arguments.length > 2;
    for (var i = 0; i < initializers.length; i++) {
        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
    }
    return useValue ? value : void 0;
};
var __setFunctionName = (this && this.__setFunctionName) || function (f, name, prefix) {
    if (typeof name === "symbol") name = name.description ? "[".concat(name.description, "]") : "";
    return Object.defineProperty(f, "name", { configurable: true, value: prefix ? "".concat(prefix, " ", name) : name });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.User = void 0;
var openapi = require("@nestjs/swagger");
var typeorm_1 = require("typeorm");
var base_entity_1 = require("../../common/entities/base.entity");
var user_role_enum_1 = require("../../common/enums/user-role.enum");
var resume_entity_1 = require("../../resumes/entities/resume.entity");
var application_entity_1 = require("../../applications/entities/application.entity");
var saved_job_entity_1 = require("../../saved-jobs/entities/saved-job.entity");
var followed_company_entity_1 = require("../../followed-companies/entities/followed-company.entity");
var notification_entity_1 = require("../../notifications/entities/notification.entity");
var User = function () {
    var _classDecorators = [(0, typeorm_1.Entity)('users')];
    var _classDescriptor;
    var _classExtraInitializers = [];
    var _classThis;
    var _classSuper = base_entity_1.BaseEntity;
    var _user_id_decorators;
    var _user_id_initializers = [];
    var _user_id_extraInitializers = [];
    var _username_decorators;
    var _username_initializers = [];
    var _username_extraInitializers = [];
    var _password_decorators;
    var _password_initializers = [];
    var _password_extraInitializers = [];
    var _full_name_decorators;
    var _full_name_initializers = [];
    var _full_name_extraInitializers = [];
    var _email_decorators;
    var _email_initializers = [];
    var _email_extraInitializers = [];
    var _phone_decorators;
    var _phone_initializers = [];
    var _phone_extraInitializers = [];
    var _address_decorators;
    var _address_initializers = [];
    var _address_extraInitializers = [];
    var _avatar_decorators;
    var _avatar_initializers = [];
    var _avatar_extraInitializers = [];
    var _role_decorators;
    var _role_initializers = [];
    var _role_extraInitializers = [];
    var _refresh_token_decorators;
    var _refresh_token_initializers = [];
    var _refresh_token_extraInitializers = [];
    var _resumes_decorators;
    var _resumes_initializers = [];
    var _resumes_extraInitializers = [];
    var _applications_decorators;
    var _applications_initializers = [];
    var _applications_extraInitializers = [];
    var _saved_jobs_decorators;
    var _saved_jobs_initializers = [];
    var _saved_jobs_extraInitializers = [];
    var _followed_companies_decorators;
    var _followed_companies_initializers = [];
    var _followed_companies_extraInitializers = [];
    var _notifications_decorators;
    var _notifications_initializers = [];
    var _notifications_extraInitializers = [];
    var User = _classThis = /** @class */ (function (_super) {
        __extends(User_1, _super);
        function User_1() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.user_id = __runInitializers(_this, _user_id_initializers, void 0);
            _this.username = (__runInitializers(_this, _user_id_extraInitializers), __runInitializers(_this, _username_initializers, void 0));
            _this.password = (__runInitializers(_this, _username_extraInitializers), __runInitializers(_this, _password_initializers, void 0));
            _this.full_name = (__runInitializers(_this, _password_extraInitializers), __runInitializers(_this, _full_name_initializers, void 0));
            _this.email = (__runInitializers(_this, _full_name_extraInitializers), __runInitializers(_this, _email_initializers, void 0));
            _this.phone = (__runInitializers(_this, _email_extraInitializers), __runInitializers(_this, _phone_initializers, void 0));
            _this.address = (__runInitializers(_this, _phone_extraInitializers), __runInitializers(_this, _address_initializers, void 0));
            _this.avatar = (__runInitializers(_this, _address_extraInitializers), __runInitializers(_this, _avatar_initializers, void 0));
            _this.role = (__runInitializers(_this, _avatar_extraInitializers), __runInitializers(_this, _role_initializers, void 0));
            _this.refresh_token = (__runInitializers(_this, _role_extraInitializers), __runInitializers(_this, _refresh_token_initializers, void 0));
            // Relations
            _this.resumes = (__runInitializers(_this, _refresh_token_extraInitializers), __runInitializers(_this, _resumes_initializers, void 0));
            _this.applications = (__runInitializers(_this, _resumes_extraInitializers), __runInitializers(_this, _applications_initializers, void 0));
            _this.saved_jobs = (__runInitializers(_this, _applications_extraInitializers), __runInitializers(_this, _saved_jobs_initializers, void 0));
            _this.followed_companies = (__runInitializers(_this, _saved_jobs_extraInitializers), __runInitializers(_this, _followed_companies_initializers, void 0));
            _this.notifications = (__runInitializers(_this, _followed_companies_extraInitializers), __runInitializers(_this, _notifications_initializers, void 0));
            __runInitializers(_this, _notifications_extraInitializers);
            return _this;
        }
        User_1._OPENAPI_METADATA_FACTORY = function () {
            return { user_id: { required: true, type: function () { return Number; } }, username: { required: true, type: function () { return String; } }, password: { required: true, type: function () { return String; } }, full_name: { required: true, type: function () { return String; } }, email: { required: true, type: function () { return String; } }, phone: { required: true, type: function () { return String; } }, address: { required: true, type: function () { return String; } }, avatar: { required: true, type: function () { return String; } }, role: { required: true, enum: require("../../common/enums/user-role.enum").UserRole }, refresh_token: { required: true, type: function () { return String; } }, resumes: { required: true, type: function () { return [require("../../resumes/entities/resume.entity").Resume]; } }, applications: { required: true, type: function () { return [require("../../applications/entities/application.entity").Application]; } }, saved_jobs: { required: true, type: function () { return [require("../../saved-jobs/entities/saved-job.entity").SavedJob]; } }, followed_companies: { required: true, type: function () { return [require("../../followed-companies/entities/followed-company.entity").FollowedCompany]; } }, notifications: { required: true, type: function () { return [require("../../notifications/entities/notification.entity").Notification]; } } };
        };
        return User_1;
    }(_classSuper));
    __setFunctionName(_classThis, "User");
    (function () {
        var _a;
        var _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create((_a = _classSuper[Symbol.metadata]) !== null && _a !== void 0 ? _a : null) : void 0;
        _user_id_decorators = [(0, typeorm_1.Column)({ name: 'user_id' })];
        _username_decorators = [(0, typeorm_1.Column)({ name: 'username', length: 50, unique: true })];
        _password_decorators = [(0, typeorm_1.Column)({ name: 'password', length: 255 })];
        _full_name_decorators = [(0, typeorm_1.Column)({ name: 'full_name', length: 100, nullable: true })];
        _email_decorators = [(0, typeorm_1.Column)({ name: 'email', length: 100, unique: true, nullable: true })];
        _phone_decorators = [(0, typeorm_1.Column)({ name: 'phone', length: 20, nullable: true })];
        _address_decorators = [(0, typeorm_1.Column)({ name: 'address', type: 'text', nullable: true })];
        _avatar_decorators = [(0, typeorm_1.Column)({ name: 'avatar', length: 255, nullable: true })];
        _role_decorators = [(0, typeorm_1.Column)({
                name: 'role',
                type: 'enum',
                enum: user_role_enum_1.UserRole,
                default: user_role_enum_1.UserRole.JOB_SEEKER,
            })];
        _refresh_token_decorators = [(0, typeorm_1.Column)({ name: 'refresh_token', nullable: true })];
        _resumes_decorators = [(0, typeorm_1.OneToMany)(function () { return resume_entity_1.Resume; }, function (resume) { return resume.user; })];
        _applications_decorators = [(0, typeorm_1.OneToMany)(function () { return application_entity_1.Application; }, function (application) { return application.user; })];
        _saved_jobs_decorators = [(0, typeorm_1.OneToMany)(function () { return saved_job_entity_1.SavedJob; }, function (savedJob) { return savedJob.user; })];
        _followed_companies_decorators = [(0, typeorm_1.OneToMany)(function () { return followed_company_entity_1.FollowedCompany; }, function (followedCompany) { return followedCompany.user; })];
        _notifications_decorators = [(0, typeorm_1.OneToMany)(function () { return notification_entity_1.Notification; }, function (notification) { return notification.recipient; })];
        __esDecorate(null, null, _user_id_decorators, { kind: "field", name: "user_id", static: false, private: false, access: { has: function (obj) { return "user_id" in obj; }, get: function (obj) { return obj.user_id; }, set: function (obj, value) { obj.user_id = value; } }, metadata: _metadata }, _user_id_initializers, _user_id_extraInitializers);
        __esDecorate(null, null, _username_decorators, { kind: "field", name: "username", static: false, private: false, access: { has: function (obj) { return "username" in obj; }, get: function (obj) { return obj.username; }, set: function (obj, value) { obj.username = value; } }, metadata: _metadata }, _username_initializers, _username_extraInitializers);
        __esDecorate(null, null, _password_decorators, { kind: "field", name: "password", static: false, private: false, access: { has: function (obj) { return "password" in obj; }, get: function (obj) { return obj.password; }, set: function (obj, value) { obj.password = value; } }, metadata: _metadata }, _password_initializers, _password_extraInitializers);
        __esDecorate(null, null, _full_name_decorators, { kind: "field", name: "full_name", static: false, private: false, access: { has: function (obj) { return "full_name" in obj; }, get: function (obj) { return obj.full_name; }, set: function (obj, value) { obj.full_name = value; } }, metadata: _metadata }, _full_name_initializers, _full_name_extraInitializers);
        __esDecorate(null, null, _email_decorators, { kind: "field", name: "email", static: false, private: false, access: { has: function (obj) { return "email" in obj; }, get: function (obj) { return obj.email; }, set: function (obj, value) { obj.email = value; } }, metadata: _metadata }, _email_initializers, _email_extraInitializers);
        __esDecorate(null, null, _phone_decorators, { kind: "field", name: "phone", static: false, private: false, access: { has: function (obj) { return "phone" in obj; }, get: function (obj) { return obj.phone; }, set: function (obj, value) { obj.phone = value; } }, metadata: _metadata }, _phone_initializers, _phone_extraInitializers);
        __esDecorate(null, null, _address_decorators, { kind: "field", name: "address", static: false, private: false, access: { has: function (obj) { return "address" in obj; }, get: function (obj) { return obj.address; }, set: function (obj, value) { obj.address = value; } }, metadata: _metadata }, _address_initializers, _address_extraInitializers);
        __esDecorate(null, null, _avatar_decorators, { kind: "field", name: "avatar", static: false, private: false, access: { has: function (obj) { return "avatar" in obj; }, get: function (obj) { return obj.avatar; }, set: function (obj, value) { obj.avatar = value; } }, metadata: _metadata }, _avatar_initializers, _avatar_extraInitializers);
        __esDecorate(null, null, _role_decorators, { kind: "field", name: "role", static: false, private: false, access: { has: function (obj) { return "role" in obj; }, get: function (obj) { return obj.role; }, set: function (obj, value) { obj.role = value; } }, metadata: _metadata }, _role_initializers, _role_extraInitializers);
        __esDecorate(null, null, _refresh_token_decorators, { kind: "field", name: "refresh_token", static: false, private: false, access: { has: function (obj) { return "refresh_token" in obj; }, get: function (obj) { return obj.refresh_token; }, set: function (obj, value) { obj.refresh_token = value; } }, metadata: _metadata }, _refresh_token_initializers, _refresh_token_extraInitializers);
        __esDecorate(null, null, _resumes_decorators, { kind: "field", name: "resumes", static: false, private: false, access: { has: function (obj) { return "resumes" in obj; }, get: function (obj) { return obj.resumes; }, set: function (obj, value) { obj.resumes = value; } }, metadata: _metadata }, _resumes_initializers, _resumes_extraInitializers);
        __esDecorate(null, null, _applications_decorators, { kind: "field", name: "applications", static: false, private: false, access: { has: function (obj) { return "applications" in obj; }, get: function (obj) { return obj.applications; }, set: function (obj, value) { obj.applications = value; } }, metadata: _metadata }, _applications_initializers, _applications_extraInitializers);
        __esDecorate(null, null, _saved_jobs_decorators, { kind: "field", name: "saved_jobs", static: false, private: false, access: { has: function (obj) { return "saved_jobs" in obj; }, get: function (obj) { return obj.saved_jobs; }, set: function (obj, value) { obj.saved_jobs = value; } }, metadata: _metadata }, _saved_jobs_initializers, _saved_jobs_extraInitializers);
        __esDecorate(null, null, _followed_companies_decorators, { kind: "field", name: "followed_companies", static: false, private: false, access: { has: function (obj) { return "followed_companies" in obj; }, get: function (obj) { return obj.followed_companies; }, set: function (obj, value) { obj.followed_companies = value; } }, metadata: _metadata }, _followed_companies_initializers, _followed_companies_extraInitializers);
        __esDecorate(null, null, _notifications_decorators, { kind: "field", name: "notifications", static: false, private: false, access: { has: function (obj) { return "notifications" in obj; }, get: function (obj) { return obj.notifications; }, set: function (obj, value) { obj.notifications = value; } }, metadata: _metadata }, _notifications_initializers, _notifications_extraInitializers);
        __esDecorate(null, _classDescriptor = { value: _classThis }, _classDecorators, { kind: "class", name: _classThis.name, metadata: _metadata }, null, _classExtraInitializers);
        User = _classThis = _classDescriptor.value;
        if (_metadata) Object.defineProperty(_classThis, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
        __runInitializers(_classThis, _classExtraInitializers);
    })();
    return User = _classThis;
}();
exports.User = User;
