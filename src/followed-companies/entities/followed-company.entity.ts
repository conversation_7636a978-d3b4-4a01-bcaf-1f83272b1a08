import { <PERSON><PERSON><PERSON>, Col<PERSON><PERSON>, <PERSON>To<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Un<PERSON> } from 'typeorm';
import { BaseEntity } from '../../common/entities/base.entity';
import { User } from '../../users/entities/user.entity';
import { Company } from '../../companies/entities/company.entity';

@Entity('followed_companies')
@Unique(['user_id', 'company_id'])
export class FollowedCompany extends BaseEntity {
  @Column({ name: 'follow_id' })
  follow_id: number;

  @Column({ name: 'user_id' })
  user_id: number;

  @Column({ name: 'company_id' })
  company_id: number;

  @Column({ name: 'followed_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  followed_at: Date;

  @Column({ name: 'notification_status', default: true })
  notification_status: boolean;

  // Relations
  @ManyToOne(() => User, (user) => user.followed_companies, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @ManyToOne(() => Company, (company) => company.followers, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'company_id' })
  company: Company;
}
