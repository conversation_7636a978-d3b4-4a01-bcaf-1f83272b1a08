import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { FollowedCompaniesService } from './followed-companies.service';
import { FollowedCompaniesController } from './followed-companies.controller';
import { FollowedCompany } from './entities/followed-company.entity';
import { CompaniesModule } from '../companies/companies.module';
import { UsersModule } from '../users/users.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([FollowedCompany]),
    CompaniesModule,
    UsersModule,
  ],
  controllers: [FollowedCompaniesController],
  providers: [FollowedCompaniesService],
  exports: [FollowedCompaniesService],
})
export class FollowedCompaniesModule {}
