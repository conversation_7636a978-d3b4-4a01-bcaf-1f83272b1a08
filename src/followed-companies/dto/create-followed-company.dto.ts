import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsBoolean } from 'class-validator';

export class CreateFollowedCompanyDto {
  @ApiProperty({ example: 1, description: 'User ID' })
  @IsNotEmpty()
  @IsNumber()
  user_id: number;

  @ApiProperty({ example: 1, description: 'Company ID' })
  @IsNotEmpty()
  @IsNumber()
  company_id: number;

  @ApiProperty({ example: true, description: 'Notification status' })
  @IsOptional()
  @IsBoolean()
  notification_status?: boolean;
}
