"use strict";
var __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
    function accept(f) { if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected"); return f; }
    var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
    var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
    var _, done = false;
    for (var i = decorators.length - 1; i >= 0; i--) {
        var context = {};
        for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
        for (var p in contextIn.access) context.access[p] = contextIn.access[p];
        context.addInitializer = function (f) { if (done) throw new TypeError("Cannot add initializers after decoration has completed"); extraInitializers.push(accept(f || null)); };
        var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
        if (kind === "accessor") {
            if (result === void 0) continue;
            if (result === null || typeof result !== "object") throw new TypeError("Object expected");
            if (_ = accept(result.get)) descriptor.get = _;
            if (_ = accept(result.set)) descriptor.set = _;
            if (_ = accept(result.init)) initializers.unshift(_);
        }
        else if (_ = accept(result)) {
            if (kind === "field") initializers.unshift(_);
            else descriptor[key] = _;
        }
    }
    if (target) Object.defineProperty(target, contextIn.name, descriptor);
    done = true;
};
var __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {
    var useValue = arguments.length > 2;
    for (var i = 0; i < initializers.length; i++) {
        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
    }
    return useValue ? value : void 0;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateFollowedCompanyDto = void 0;
var openapi = require("@nestjs/swagger");
var swagger_1 = require("@nestjs/swagger");
var class_validator_1 = require("class-validator");
var CreateFollowedCompanyDto = function () {
    var _a;
    var _user_id_decorators;
    var _user_id_initializers = [];
    var _user_id_extraInitializers = [];
    var _company_id_decorators;
    var _company_id_initializers = [];
    var _company_id_extraInitializers = [];
    var _notification_status_decorators;
    var _notification_status_initializers = [];
    var _notification_status_extraInitializers = [];
    return _a = /** @class */ (function () {
            function CreateFollowedCompanyDto() {
                this.user_id = __runInitializers(this, _user_id_initializers, void 0);
                this.company_id = (__runInitializers(this, _user_id_extraInitializers), __runInitializers(this, _company_id_initializers, void 0));
                this.notification_status = (__runInitializers(this, _company_id_extraInitializers), __runInitializers(this, _notification_status_initializers, void 0));
                __runInitializers(this, _notification_status_extraInitializers);
            }
            CreateFollowedCompanyDto._OPENAPI_METADATA_FACTORY = function () {
                return { user_id: { required: true, type: function () { return Number; } }, company_id: { required: true, type: function () { return Number; } }, notification_status: { required: false, type: function () { return Boolean; } } };
            };
            return CreateFollowedCompanyDto;
        }()),
        (function () {
            var _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(null) : void 0;
            _user_id_decorators = [(0, swagger_1.ApiProperty)({ example: 1, description: 'User ID' }), (0, class_validator_1.IsNotEmpty)(), (0, class_validator_1.IsNumber)()];
            _company_id_decorators = [(0, swagger_1.ApiProperty)({ example: 1, description: 'Company ID' }), (0, class_validator_1.IsNotEmpty)(), (0, class_validator_1.IsNumber)()];
            _notification_status_decorators = [(0, swagger_1.ApiProperty)({ example: true, description: 'Notification status' }), (0, class_validator_1.IsOptional)(), (0, class_validator_1.IsBoolean)()];
            __esDecorate(null, null, _user_id_decorators, { kind: "field", name: "user_id", static: false, private: false, access: { has: function (obj) { return "user_id" in obj; }, get: function (obj) { return obj.user_id; }, set: function (obj, value) { obj.user_id = value; } }, metadata: _metadata }, _user_id_initializers, _user_id_extraInitializers);
            __esDecorate(null, null, _company_id_decorators, { kind: "field", name: "company_id", static: false, private: false, access: { has: function (obj) { return "company_id" in obj; }, get: function (obj) { return obj.company_id; }, set: function (obj, value) { obj.company_id = value; } }, metadata: _metadata }, _company_id_initializers, _company_id_extraInitializers);
            __esDecorate(null, null, _notification_status_decorators, { kind: "field", name: "notification_status", static: false, private: false, access: { has: function (obj) { return "notification_status" in obj; }, get: function (obj) { return obj.notification_status; }, set: function (obj, value) { obj.notification_status = value; } }, metadata: _metadata }, _notification_status_initializers, _notification_status_extraInitializers);
            if (_metadata) Object.defineProperty(_a, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
        })(),
        _a;
}();
exports.CreateFollowedCompanyDto = CreateFollowedCompanyDto;
