"use strict";
var __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {
    var useValue = arguments.length > 2;
    for (var i = 0; i < initializers.length; i++) {
        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
    }
    return useValue ? value : void 0;
};
var __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
    function accept(f) { if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected"); return f; }
    var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
    var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
    var _, done = false;
    for (var i = decorators.length - 1; i >= 0; i--) {
        var context = {};
        for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
        for (var p in contextIn.access) context.access[p] = contextIn.access[p];
        context.addInitializer = function (f) { if (done) throw new TypeError("Cannot add initializers after decoration has completed"); extraInitializers.push(accept(f || null)); };
        var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
        if (kind === "accessor") {
            if (result === void 0) continue;
            if (result === null || typeof result !== "object") throw new TypeError("Object expected");
            if (_ = accept(result.get)) descriptor.get = _;
            if (_ = accept(result.set)) descriptor.set = _;
            if (_ = accept(result.init)) initializers.unshift(_);
        }
        else if (_ = accept(result)) {
            if (kind === "field") initializers.unshift(_);
            else descriptor[key] = _;
        }
    }
    if (target) Object.defineProperty(target, contextIn.name, descriptor);
    done = true;
};
var __setFunctionName = (this && this.__setFunctionName) || function (f, name, prefix) {
    if (typeof name === "symbol") name = name.description ? "[".concat(name.description, "]") : "";
    return Object.defineProperty(f, "name", { configurable: true, value: prefix ? "".concat(prefix, " ", name) : name });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FollowedCompaniesController = void 0;
var openapi = require("@nestjs/swagger");
var common_1 = require("@nestjs/common");
var swagger_1 = require("@nestjs/swagger");
var response_message_decorator_1 = require("../common/decorators/response-message.decorator");
var FollowedCompaniesController = function () {
    var _classDecorators = [(0, swagger_1.ApiTags)('followed-companies'), (0, swagger_1.ApiBearerAuth)('access-token'), (0, common_1.Controller)('followed-companies')];
    var _classDescriptor;
    var _classExtraInitializers = [];
    var _classThis;
    var _instanceExtraInitializers = [];
    var _create_decorators;
    var _followCompany_decorators;
    var _getMyFollowedCompanies_decorators;
    var _findByUser_decorators;
    var _findByCompany_decorators;
    var _findOne_decorators;
    var _updateNotificationStatus_decorators;
    var _remove_decorators;
    var _unfollowCompany_decorators;
    var FollowedCompaniesController = _classThis = /** @class */ (function () {
        function FollowedCompaniesController_1(followedCompaniesService) {
            this.followedCompaniesService = (__runInitializers(this, _instanceExtraInitializers), followedCompaniesService);
        }
        FollowedCompaniesController_1.prototype.create = function (createFollowedCompanyDto) {
            return this.followedCompaniesService.create(createFollowedCompanyDto);
        };
        FollowedCompaniesController_1.prototype.followCompany = function (user, companyId) {
            return this.followedCompaniesService.create({ user_id: user.id, company_id: companyId });
        };
        FollowedCompaniesController_1.prototype.getMyFollowedCompanies = function (user) {
            return this.followedCompaniesService.findByUser(user.id);
        };
        FollowedCompaniesController_1.prototype.findByUser = function (userId) {
            return this.followedCompaniesService.findByUser(userId);
        };
        FollowedCompaniesController_1.prototype.findByCompany = function (companyId) {
            return this.followedCompaniesService.findByCompany(companyId);
        };
        FollowedCompaniesController_1.prototype.findOne = function (id) {
            return this.followedCompaniesService.findOne(id);
        };
        FollowedCompaniesController_1.prototype.updateNotificationStatus = function (id, notificationStatus) {
            return this.followedCompaniesService.updateNotificationStatus(id, notificationStatus);
        };
        FollowedCompaniesController_1.prototype.remove = function (id) {
            return this.followedCompaniesService.remove(id);
        };
        FollowedCompaniesController_1.prototype.unfollowCompany = function (user, companyId) {
            return this.followedCompaniesService.removeByUserAndCompany(user.id, companyId);
        };
        return FollowedCompaniesController_1;
    }());
    __setFunctionName(_classThis, "FollowedCompaniesController");
    (function () {
        var _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(null) : void 0;
        _create_decorators = [(0, common_1.Post)(), (0, swagger_1.ApiOperation)({ summary: 'Follow a company' }), (0, swagger_1.ApiResponse)({ status: 201, description: 'Company followed successfully' }), (0, response_message_decorator_1.ResponseMessage)('Company followed successfully'), openapi.ApiResponse({ status: 201, type: require("./entities/followed-company.entity").FollowedCompany })];
        _followCompany_decorators = [(0, common_1.Post)(':companyId'), (0, swagger_1.ApiOperation)({ summary: 'Follow a company by company ID' }), (0, swagger_1.ApiResponse)({ status: 201, description: 'Company followed successfully' }), (0, response_message_decorator_1.ResponseMessage)('Company followed successfully'), openapi.ApiResponse({ status: 201, type: require("./entities/followed-company.entity").FollowedCompany })];
        _getMyFollowedCompanies_decorators = [(0, common_1.Get)('my-followed-companies'), (0, swagger_1.ApiOperation)({ summary: 'Get current user followed companies' }), (0, swagger_1.ApiResponse)({ status: 200, description: 'Followed companies retrieved successfully' }), (0, response_message_decorator_1.ResponseMessage)('Followed companies retrieved successfully'), openapi.ApiResponse({ status: 200, type: [require("./entities/followed-company.entity").FollowedCompany] })];
        _findByUser_decorators = [(0, common_1.Get)('user/:userId'), (0, swagger_1.ApiOperation)({ summary: 'Get followed companies by user ID' }), (0, swagger_1.ApiResponse)({ status: 200, description: 'User followed companies retrieved successfully' }), (0, response_message_decorator_1.ResponseMessage)('User followed companies retrieved successfully'), openapi.ApiResponse({ status: 200, type: [require("./entities/followed-company.entity").FollowedCompany] })];
        _findByCompany_decorators = [(0, common_1.Get)('company/:companyId'), (0, swagger_1.ApiOperation)({ summary: 'Get company followers' }), (0, swagger_1.ApiResponse)({ status: 200, description: 'Company followers retrieved successfully' }), (0, response_message_decorator_1.ResponseMessage)('Company followers retrieved successfully'), openapi.ApiResponse({ status: 200, type: [require("./entities/followed-company.entity").FollowedCompany] })];
        _findOne_decorators = [(0, common_1.Get)(':id'), (0, swagger_1.ApiOperation)({ summary: 'Get followed company by ID' }), (0, swagger_1.ApiResponse)({ status: 200, description: 'Followed company retrieved successfully' }), (0, response_message_decorator_1.ResponseMessage)('Followed company retrieved successfully'), openapi.ApiResponse({ status: 200, type: require("./entities/followed-company.entity").FollowedCompany })];
        _updateNotificationStatus_decorators = [(0, common_1.Patch)(':id/notifications'), (0, swagger_1.ApiOperation)({ summary: 'Update notification status' }), (0, swagger_1.ApiResponse)({ status: 200, description: 'Notification status updated successfully' }), (0, response_message_decorator_1.ResponseMessage)('Notification status updated successfully'), openapi.ApiResponse({ status: 200, type: require("./entities/followed-company.entity").FollowedCompany })];
        _remove_decorators = [(0, common_1.Delete)(':id'), (0, swagger_1.ApiOperation)({ summary: 'Unfollow company by ID' }), (0, swagger_1.ApiResponse)({ status: 200, description: 'Company unfollowed successfully' }), (0, response_message_decorator_1.ResponseMessage)('Company unfollowed successfully'), openapi.ApiResponse({ status: 200 })];
        _unfollowCompany_decorators = [(0, common_1.Delete)('company/:companyId'), (0, swagger_1.ApiOperation)({ summary: 'Unfollow a company by company ID' }), (0, swagger_1.ApiResponse)({ status: 200, description: 'Company unfollowed successfully' }), (0, response_message_decorator_1.ResponseMessage)('Company unfollowed successfully'), openapi.ApiResponse({ status: 200 })];
        __esDecorate(_classThis, null, _create_decorators, { kind: "method", name: "create", static: false, private: false, access: { has: function (obj) { return "create" in obj; }, get: function (obj) { return obj.create; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _followCompany_decorators, { kind: "method", name: "followCompany", static: false, private: false, access: { has: function (obj) { return "followCompany" in obj; }, get: function (obj) { return obj.followCompany; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _getMyFollowedCompanies_decorators, { kind: "method", name: "getMyFollowedCompanies", static: false, private: false, access: { has: function (obj) { return "getMyFollowedCompanies" in obj; }, get: function (obj) { return obj.getMyFollowedCompanies; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _findByUser_decorators, { kind: "method", name: "findByUser", static: false, private: false, access: { has: function (obj) { return "findByUser" in obj; }, get: function (obj) { return obj.findByUser; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _findByCompany_decorators, { kind: "method", name: "findByCompany", static: false, private: false, access: { has: function (obj) { return "findByCompany" in obj; }, get: function (obj) { return obj.findByCompany; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _findOne_decorators, { kind: "method", name: "findOne", static: false, private: false, access: { has: function (obj) { return "findOne" in obj; }, get: function (obj) { return obj.findOne; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _updateNotificationStatus_decorators, { kind: "method", name: "updateNotificationStatus", static: false, private: false, access: { has: function (obj) { return "updateNotificationStatus" in obj; }, get: function (obj) { return obj.updateNotificationStatus; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _remove_decorators, { kind: "method", name: "remove", static: false, private: false, access: { has: function (obj) { return "remove" in obj; }, get: function (obj) { return obj.remove; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(_classThis, null, _unfollowCompany_decorators, { kind: "method", name: "unfollowCompany", static: false, private: false, access: { has: function (obj) { return "unfollowCompany" in obj; }, get: function (obj) { return obj.unfollowCompany; } }, metadata: _metadata }, null, _instanceExtraInitializers);
        __esDecorate(null, _classDescriptor = { value: _classThis }, _classDecorators, { kind: "class", name: _classThis.name, metadata: _metadata }, null, _classExtraInitializers);
        FollowedCompaniesController = _classThis = _classDescriptor.value;
        if (_metadata) Object.defineProperty(_classThis, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
        __runInitializers(_classThis, _classExtraInitializers);
    })();
    return FollowedCompaniesController = _classThis;
}();
exports.FollowedCompaniesController = FollowedCompaniesController;
