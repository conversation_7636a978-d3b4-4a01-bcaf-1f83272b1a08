import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ParseIntPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';

import { FollowedCompaniesService } from './followed-companies.service';
import { CreateFollowedCompanyDto } from './dto/create-followed-company.dto';
import { ResponseMessage } from '../common/decorators/response-message.decorator';
import { User } from '../common/decorators/user.decorator';
import { IUser } from '../auth/interfaces/user.interface';

@ApiTags('followed-companies')
@ApiBearerAuth('access-token')
@Controller('followed-companies')
export class FollowedCompaniesController {
  constructor(private readonly followedCompaniesService: FollowedCompaniesService) {}

  @Post()
  @ApiOperation({ summary: 'Follow a company' })
  @ApiResponse({ status: 201, description: 'Company followed successfully' })
  @ResponseMessage('Company followed successfully')
  create(@Body() createFollowedCompanyDto: CreateFollowedCompanyDto) {
    return this.followedCompaniesService.create(createFollowedCompanyDto);
  }

  @Post(':companyId')
  @ApiOperation({ summary: 'Follow a company by company ID' })
  @ApiResponse({ status: 201, description: 'Company followed successfully' })
  @ResponseMessage('Company followed successfully')
  followCompany(@User() user: IUser, @Param('companyId', ParseIntPipe) companyId: number) {
    return this.followedCompaniesService.create({ user_id: user.id, company_id: companyId });
  }

  @Get('my-followed-companies')
  @ApiOperation({ summary: 'Get current user followed companies' })
  @ApiResponse({ status: 200, description: 'Followed companies retrieved successfully' })
  @ResponseMessage('Followed companies retrieved successfully')
  getMyFollowedCompanies(@User() user: IUser) {
    return this.followedCompaniesService.findByUser(user.id);
  }

  @Get('user/:userId')
  @ApiOperation({ summary: 'Get followed companies by user ID' })
  @ApiResponse({ status: 200, description: 'User followed companies retrieved successfully' })
  @ResponseMessage('User followed companies retrieved successfully')
  findByUser(@Param('userId', ParseIntPipe) userId: number) {
    return this.followedCompaniesService.findByUser(userId);
  }

  @Get('company/:companyId')
  @ApiOperation({ summary: 'Get company followers' })
  @ApiResponse({ status: 200, description: 'Company followers retrieved successfully' })
  @ResponseMessage('Company followers retrieved successfully')
  findByCompany(@Param('companyId', ParseIntPipe) companyId: number) {
    return this.followedCompaniesService.findByCompany(companyId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get followed company by ID' })
  @ApiResponse({ status: 200, description: 'Followed company retrieved successfully' })
  @ResponseMessage('Followed company retrieved successfully')
  findOne(@Param('id', ParseIntPipe) id: number) {
    return this.followedCompaniesService.findOne(id);
  }

  @Patch(':id/notifications')
  @ApiOperation({ summary: 'Update notification status' })
  @ApiResponse({ status: 200, description: 'Notification status updated successfully' })
  @ResponseMessage('Notification status updated successfully')
  updateNotificationStatus(
    @Param('id', ParseIntPipe) id: number,
    @Body('notification_status') notificationStatus: boolean,
  ) {
    return this.followedCompaniesService.updateNotificationStatus(id, notificationStatus);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Unfollow company by ID' })
  @ApiResponse({ status: 200, description: 'Company unfollowed successfully' })
  @ResponseMessage('Company unfollowed successfully')
  remove(@Param('id', ParseIntPipe) id: number) {
    return this.followedCompaniesService.remove(id);
  }

  @Delete('company/:companyId')
  @ApiOperation({ summary: 'Unfollow a company by company ID' })
  @ApiResponse({ status: 200, description: 'Company unfollowed successfully' })
  @ResponseMessage('Company unfollowed successfully')
  unfollowCompany(@User() user: IUser, @Param('companyId', ParseIntPipe) companyId: number) {
    return this.followedCompaniesService.removeByUserAndCompany(user.id, companyId);
  }
}
