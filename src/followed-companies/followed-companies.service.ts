import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { FollowedCompany } from './entities/followed-company.entity';
import { CreateFollowedCompanyDto } from './dto/create-followed-company.dto';
import { CompaniesService } from '../companies/companies.service';
import { UsersService } from '../users/users.service';

@Injectable()
export class FollowedCompaniesService {
  constructor(
    @InjectRepository(FollowedCompany)
    private followedCompanyRepository: Repository<FollowedCompany>,
    private companiesService: CompaniesService,
    private usersService: UsersService,
  ) {}

  async create(createFollowedCompanyDto: CreateFollowedCompanyDto): Promise<FollowedCompany> {
    // Verify user and company exist
    await this.usersService.findOne(createFollowedCompanyDto.user_id);
    await this.companiesService.findOne(createFollowedCompanyDto.company_id);

    // Check if already following
    const existingFollow = await this.followedCompanyRepository.findOne({
      where: {
        user_id: createFollowedCompanyDto.user_id,
        company_id: createFollowedCompanyDto.company_id,
      },
    });

    if (existingFollow) {
      throw new ConflictException('User already follows this company');
    }

    const followedCompany = this.followedCompanyRepository.create({
      ...createFollowedCompanyDto,
      follow_id: Math.floor(Math.random() * 1000000),
      notification_status: createFollowedCompanyDto.notification_status ?? true,
    });

    return this.followedCompanyRepository.save(followedCompany);
  }

  async findByUser(userId: number): Promise<FollowedCompany[]> {
    return this.followedCompanyRepository.find({
      where: { user_id: userId },
      relations: ['company'],
      order: { followed_at: 'DESC' },
    });
  }

  async findByCompany(companyId: number): Promise<FollowedCompany[]> {
    return this.followedCompanyRepository.find({
      where: { company_id: companyId },
      relations: ['user'],
      order: { followed_at: 'DESC' },
    });
  }

  async findOne(id: number): Promise<FollowedCompany> {
    const followedCompany = await this.followedCompanyRepository.findOne({
      where: { id },
      relations: ['user', 'company'],
    });

    if (!followedCompany) {
      throw new NotFoundException('Followed company not found');
    }

    return followedCompany;
  }

  async updateNotificationStatus(id: number, notificationStatus: boolean): Promise<FollowedCompany> {
    const followedCompany = await this.findOne(id);
    await this.followedCompanyRepository.update(id, { notification_status: notificationStatus });
    return this.findOne(id);
  }

  async remove(id: number): Promise<void> {
    const followedCompany = await this.findOne(id);
    await this.followedCompanyRepository.remove(followedCompany);
  }

  async removeByUserAndCompany(userId: number, companyId: number): Promise<void> {
    const followedCompany = await this.followedCompanyRepository.findOne({
      where: { user_id: userId, company_id: companyId },
    });

    if (!followedCompany) {
      throw new NotFoundException('Followed company not found');
    }

    await this.followedCompanyRepository.remove(followedCompany);
  }
}
