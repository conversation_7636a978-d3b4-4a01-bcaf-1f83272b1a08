#!/bin/bash

echo "🚀 Setting up Work Finder API..."

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p uploads/resumes
mkdir -p src/database/migrations

# Copy environment file
echo "📄 Setting up environment..."
if [ ! -f .env ]; then
    cp .env.example .env
    echo "✅ Created .env file from .env.example"
    echo "⚠️  Please update the .env file with your database credentials"
else
    echo "✅ .env file already exists"
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Check if PostgreSQL is running
echo "🔍 Checking PostgreSQL connection..."
if command -v psql &> /dev/null; then
    echo "✅ PostgreSQL CLI found"
else
    echo "⚠️  PostgreSQL CLI not found. Please install PostgreSQL"
fi

echo "🎉 Setup complete!"
echo ""
echo "Next steps:"
echo "1. Update your .env file with correct database credentials"
echo "2. Create a PostgreSQL database named 'work_finder'"
echo "3. Run 'npm run start:dev' to start the development server"
echo "4. Visit http://localhost:3000/swagger for API documentation"
echo ""
echo "Database setup commands:"
echo "  createdb work_finder"
echo "  npm run migration:run"
