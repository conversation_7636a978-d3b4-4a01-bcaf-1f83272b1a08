#!/bin/bash

echo "🚀 Setting up Work Finder API..."

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p uploads/resumes
mkdir -p src/database/migrations

# Copy environment file
echo "📄 Setting up environment..."
if [ ! -f .env ]; then
    cp .env.example .env
    echo "✅ Created .env file from .env.example"
else
    echo "✅ .env file already exists"
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Check if PostgreSQL is running
echo "🔍 Checking PostgreSQL connection..."
if command -v psql &> /dev/null; then
    echo "✅ PostgreSQL CLI found"

    # Test connection to PostgreSQL
    echo "🔗 Testing PostgreSQL connection..."
    if PGPASSWORD=012003 psql -h localhost -U postgres -d postgres -c "SELECT version();" &> /dev/null; then
        echo "✅ PostgreSQL connection successful"

        # Check if database exists
        if PGPASSWORD=012003 psql -h localhost -U postgres -lqt | cut -d \| -f 1 | grep -qw work_finder; then
            echo "✅ Database 'work_finder' already exists"
        else
            echo "📊 Creating database 'work_finder'..."
            PGPASSWORD=012003 createdb -h localhost -U postgres work_finder
            echo "✅ Database 'work_finder' created successfully"
        fi

        # Load initial data
        echo "📊 Loading initial seed data..."
        PGPASSWORD=012003 psql -h localhost -U postgres -d work_finder -f src/database/seeds/initial-data.sql
        echo "✅ Initial data loaded successfully"

    else
        echo "❌ Cannot connect to PostgreSQL. Please check:"
        echo "   - PostgreSQL is running on localhost:5432"
        echo "   - Username: postgres"
        echo "   - Password: 012003"
        echo "   - User has permission to create databases"
    fi
else
    echo "⚠️  PostgreSQL CLI not found. Please install PostgreSQL"
fi

echo ""
echo "🎉 Setup complete!"
echo ""
echo "Next steps:"
echo "1. Run 'npm run start:dev' to start the development server"
echo "2. Visit http://localhost:3000/swagger for API documentation"
echo "3. Test the API endpoints"
echo ""
echo "Database info:"
echo "  Host: localhost:5432"
echo "  Database: work_finder"
echo "  Username: postgres"
echo "  Password: 012003"
