#!/bin/bash

echo "🔍 Testing PostgreSQL connection..."

# Test basic connection
echo "Testing connection to PostgreSQL..."
if PGPASSWORD=012003 psql -h localhost -U postgres -d postgres -c "SELECT 'Connection successful!' as status;" 2>/dev/null; then
    echo "✅ PostgreSQL connection successful"
else
    echo "❌ Cannot connect to PostgreSQL"
    echo "Please check:"
    echo "  - PostgreSQL is running: sudo systemctl status postgresql"
    echo "  - Port 5432 is open: sudo netstat -tlnp | grep 5432"
    echo "  - User postgres exists with password 012003"
    exit 1
fi

# Check if work_finder database exists
echo ""
echo "Checking work_finder database..."
if PGPASSWORD=012003 psql -h localhost -U postgres -lqt | cut -d \| -f 1 | grep -qw work_finder; then
    echo "✅ Database 'work_finder' exists"
    
    # Check tables
    echo ""
    echo "Checking database tables..."
    TABLES=$(PGPASSWORD=012003 psql -h localhost -U postgres -d work_finder -t -c "SELECT tablename FROM pg_tables WHERE schemaname = 'public';" | tr -d ' ')
    
    if [ -z "$TABLES" ]; then
        echo "⚠️  No tables found in work_finder database"
        echo "Run: PGPASSWORD=012003 psql -h localhost -U postgres -d work_finder -f src/database/seeds/initial-data.sql"
    else
        echo "✅ Found tables:"
        echo "$TABLES" | while read table; do
            if [ ! -z "$table" ]; then
                echo "  - $table"
            fi
        done
    fi
else
    echo "❌ Database 'work_finder' does not exist"
    echo "Run: PGPASSWORD=012003 createdb -h localhost -U postgres work_finder"
    exit 1
fi

echo ""
echo "🎉 Database setup looks good!"
echo ""
echo "Next steps:"
echo "1. cd api"
echo "2. npm install"
echo "3. npm run start:dev"
echo "4. Visit http://localhost:3000/swagger"
