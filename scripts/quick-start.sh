#!/bin/bash

echo "🚀 Work Finder API - Quick Start"
echo "================================"

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Please run this script from the api directory"
    echo "cd api && ./scripts/quick-start.sh"
    exit 1
fi

# Step 1: Test PostgreSQL connection
echo ""
echo "Step 1: Testing PostgreSQL connection..."
if ! ./scripts/test-connection.sh; then
    echo "❌ PostgreSQL connection failed. Please fix the connection first."
    exit 1
fi

# Step 2: Install dependencies
echo ""
echo "Step 2: Installing dependencies..."
if [ ! -d "node_modules" ]; then
    npm install
    echo "✅ Dependencies installed"
else
    echo "✅ Dependencies already installed"
fi

# Step 3: Create uploads directory
echo ""
echo "Step 3: Creating uploads directory..."
mkdir -p uploads/resumes
echo "✅ Uploads directory created"

# Step 4: Check .env file
echo ""
echo "Step 4: Checking environment configuration..."
if [ ! -f ".env" ]; then
    cp .env.example .env
    echo "✅ Created .env file"
else
    echo "✅ .env file exists"
fi

# Step 5: Load initial data
echo ""
echo "Step 5: Loading initial data..."
if PGPASSWORD=012003 psql -h localhost -U postgres -d work_finder -f src/database/seeds/initial-data.sql > /dev/null 2>&1; then
    echo "✅ Initial data loaded"
else
    echo "⚠️  Could not load initial data (may already exist)"
fi

# Step 6: Start the application
echo ""
echo "Step 6: Starting the application..."
echo "🎯 Starting Work Finder API on http://localhost:3000"
echo "📚 Swagger docs will be available at http://localhost:3000/swagger"
echo ""
echo "Press Ctrl+C to stop the server"
echo ""

npm run start:dev
