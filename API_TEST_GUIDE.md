# 🧪 Hướng dẫn Test API Work Finder

## Bước 1: Khởi động API

```bash
cd api
chmod +x scripts/quick-start.sh
./scripts/quick-start.sh
```

## Bước 2: Test cơ bản

### Health Check
```bash
curl http://localhost:3000/health
```

### API Info
```bash
curl http://localhost:3000/api/v1/
```

## Bước 3: Đăng ký và đăng nhập

### 1. Đăng ký user mới
```bash
curl -X POST http://localhost:3000/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "john_doe",
    "password": "password123",
    "full_name": "<PERSON>",
    "email": "<EMAIL>",
    "phone": "+**********"
  }'
```

### 2. Đăng nhập
```bash
curl -X POST http://localhost:3000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "john_doe",
    "password": "password123"
  }'
```

**<PERSON><PERSON><PERSON> access_token từ response để sử dụng cho các request tiếp theo!**

## Bước 4: Test các API chính

### Lấy thông tin profile (cần token)
```bash
curl -X GET http://localhost:3000/api/v1/auth/profile \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### Lấy danh sách công ty
```bash
curl -X GET http://localhost:3000/api/v1/companies
```

### Lấy danh sách việc làm
```bash
curl -X GET http://localhost:3000/api/v1/jobs
```

### Tìm kiếm việc làm
```bash
curl -X GET "http://localhost:3000/api/v1/jobs?search=engineer&location=San Francisco"
```

## Bước 5: Test upload CV

### Upload file CV (cần token)
```bash
curl -X POST http://localhost:3000/api/v1/resumes/upload \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -F "file=@/path/to/your/resume.pdf"
```

### Lấy danh sách CV của user
```bash
curl -X GET http://localhost:3000/api/v1/resumes/my-resumes \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## Bước 6: Test ứng tuyển

### Ứng tuyển vào một công việc
```bash
curl -X POST http://localhost:3000/api/v1/applications \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "job_id": 1,
    "resume_id": 1
  }'
```

### Xem đơn ứng tuyển của mình
```bash
curl -X GET http://localhost:3000/api/v1/applications/my-applications \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## Bước 7: Test lưu việc làm

### Lưu một công việc
```bash
curl -X POST http://localhost:3000/api/v1/saved-jobs/1 \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### Xem việc làm đã lưu
```bash
curl -X GET http://localhost:3000/api/v1/saved-jobs/my-saved-jobs \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## Bước 8: Test theo dõi công ty

### Theo dõi một công ty
```bash
curl -X POST http://localhost:3000/api/v1/companies/1/follow \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### Xem công ty đang theo dõi
```bash
curl -X GET http://localhost:3000/api/v1/companies/my-followed \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### Bỏ theo dõi công ty
```bash
curl -X DELETE http://localhost:3000/api/v1/companies/1/unfollow \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## Bước 9: Test thông báo

### Xem thông báo của mình
```bash
curl -X GET http://localhost:3000/api/v1/notifications \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### Xem thông báo chưa đọc
```bash
curl -X GET http://localhost:3000/api/v1/notifications/unread \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### Xem thống kê thông báo
```bash
curl -X GET http://localhost:3000/api/v1/notifications/stats \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### Đánh dấu thông báo đã đọc
```bash
curl -X PATCH http://localhost:3000/api/v1/notifications/1/read \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### Đánh dấu tất cả đã đọc
```bash
curl -X PATCH http://localhost:3000/api/v1/notifications/mark-all-read \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## Swagger UI Testing

Truy cập http://localhost:3000/swagger để test API qua giao diện web:

1. Click "Authorize" và nhập Bearer token
2. Test các endpoint trực tiếp
3. Xem response và schema

## Sample Data có sẵn

Sau khi chạy setup, database đã có:

### Companies:
- ID 1: Tech Innovations Inc.
- ID 2: Global Marketing Solutions  
- ID 3: Healthcare Plus
- ID 4: Green Energy Corp
- ID 5: Financial Services Group

### Jobs:
- ID 1: Senior Software Engineer (Tech Innovations)
- ID 2: Frontend Developer (Tech Innovations)
- ID 3: Digital Marketing Specialist (Global Marketing)
- ID 4: Healthcare Data Analyst (Healthcare Plus)
- ID 5: Renewable Energy Engineer (Green Energy)
- ID 6: Financial Analyst (Financial Services)
- ID 7: DevOps Engineer (Tech Innovations)
- ID 8: Content Marketing Manager (Global Marketing)

### Permissions:
- ID 1: USER
- ID 2: ADMIN
- ID 3: HR_MANAGER
- ID 4: COMPANY_ADMIN

## Troubleshooting

### Token hết hạn:
```bash
# Refresh token
curl -X POST http://localhost:3000/api/v1/auth/refresh \
  -H "Cookie: refresh_token=YOUR_REFRESH_TOKEN"
```

### Lỗi 401 Unauthorized:
- Kiểm tra token có đúng không
- Token có hết hạn không
- Header Authorization có đúng format không

### Lỗi 404 Not Found:
- Kiểm tra URL có đúng không
- API có chạy trên port 3000 không

🎯 **Happy Testing!**
