# 🚀 Hướng dẫn cài đặt Work Finder API

## Yêu cầu hệ thống

- Node.js 18+ 
- PostgreSQL 12+
- npm hoặc yarn

## Bước 1: <PERSON>ài đặt PostgreSQL

### Windows:
1. Tải PostgreSQL từ https://www.postgresql.org/download/windows/
2. Cài đặt với user `postgres` và password `012003`
3. <PERSON><PERSON><PERSON> b<PERSON>o PostgreSQL chạy trên port 5432

### macOS:
```bash
# Sử dụng Homebrew
brew install postgresql
brew services start postgresql

# Tạo user postgres với password
createuser -s postgres
psql -U postgres -c "ALTER USER postgres PASSWORD '012003';"
```

### Linux (Ubuntu/Debian):
```bash
sudo apt update
sudo apt install postgresql postgresql-contrib

# Đặt password cho user postgres
sudo -u postgres psql -c "ALTER USER postgres PASSWORD '012003';"
```

## Bước 2: Kiểm tra kết nối PostgreSQL

```bash
# Test kết nối
PGPASSWORD=012003 psql -h localhost -U postgres -c "SELECT version();"
```

## Bước 3: Cài đặt dự án

```bash
# Di chuyển vào thư mục api
cd api

# Cài đặt dependencies
npm install

# Chạy script setup tự động
chmod +x scripts/setup.sh
./scripts/setup.sh
```

## Bước 4: Chạy ứng dụng

```bash
# Development mode
npm run start:dev

# Production mode
npm run build
npm run start:prod
```

## Bước 5: Kiểm tra

1. **API Health Check:**
   ```
   http://localhost:3000/health
   ```

2. **Swagger Documentation:**
   ```
   http://localhost:3000/swagger
   ```

3. **Test API:**
   ```bash
   curl http://localhost:3000/api/v1/
   ```

## Cấu trúc Database

Sau khi setup, database sẽ có:

### Tables:
- `permissions` - Phân quyền người dùng
- `users` - Thông tin người dùng
- `companies` - Thông tin công ty
- `job_posts` - Bài đăng tuyển dụng
- `resumes` - CV của người dùng
- `applications` - Đơn ứng tuyển
- `interviews` - Lịch phỏng vấn
- `saved_jobs` - Việc làm đã lưu
- `followed_companies` - Công ty đang theo dõi
- `notifications` - Thông báo

### Sample Data:
- 4 permissions: USER, ADMIN, HR_MANAGER, COMPANY_ADMIN
- 5 sample companies
- 8 sample job posts

## Troubleshooting

### Lỗi kết nối PostgreSQL:
```bash
# Kiểm tra PostgreSQL có chạy không
sudo systemctl status postgresql  # Linux
brew services list | grep postgresql  # macOS

# Kiểm tra port
sudo netstat -tlnp | grep 5432
```

### Lỗi permission:
```bash
# Cấp quyền cho user postgres
sudo -u postgres psql -c "ALTER USER postgres CREATEDB;"
```

### Lỗi database không tồn tại:
```bash
# Tạo database thủ công
PGPASSWORD=012003 createdb -h localhost -U postgres work_finder
```

### Reset database:
```bash
# Xóa và tạo lại database
PGPASSWORD=012003 dropdb -h localhost -U postgres work_finder
PGPASSWORD=012003 createdb -h localhost -U postgres work_finder
PGPASSWORD=012003 psql -h localhost -U postgres -d work_finder -f src/database/seeds/initial-data.sql
```

## Các lệnh hữu ích

```bash
# Xem logs
npm run start:dev

# Chạy tests
npm run test
npm run test:e2e

# Format code
npm run format

# Lint code
npm run lint

# Build production
npm run build
```

## Cấu hình môi trường

File `.env` đã được tạo với cấu hình:
```env
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=012003
DB_DATABASE=work_finder
```

## Tiếp theo

Sau khi setup thành công:
1. Truy cập Swagger UI để test API
2. Tạo user đầu tiên qua `/auth/register`
3. Login và test các endpoints khác
4. Upload CV qua `/resumes/upload`
5. Tạo company và job posts

🎉 **Chúc mừng! API đã sẵn sàng sử dụng!**
