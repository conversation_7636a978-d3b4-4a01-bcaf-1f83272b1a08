#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function printStatus(message) {
  console.log(`${colors.cyan}[INFO]${colors.reset} ${message}`);
}

function printSuccess(message) {
  console.log(`${colors.green}[SUCCESS]${colors.reset} ${message}`);
}

function printWarning(message) {
  console.log(`${colors.yellow}[WARNING]${colors.reset} ${message}`);
}

function printError(message) {
  console.log(`${colors.red}[ERROR]${colors.reset} ${message}`);
}

function runCommand(command, options = {}) {
  try {
    execSync(command, { stdio: 'inherit', ...options });
    return true;
  } catch (error) {
    return false;
  }
}

function removeIfExists(filePath) {
  if (fs.existsSync(filePath)) {
    if (fs.lstatSync(filePath).isDirectory()) {
      fs.rmSync(filePath, { recursive: true, force: true });
    } else {
      fs.unlinkSync(filePath);
    }
    return true;
  }
  return false;
}

async function fixDependencies() {
  console.log(`${colors.blue}🔧 Fixing npm dependencies automatically...${colors.reset}\n`);

  try {
    // Step 1: Clear npm cache
    printStatus('Clearing npm cache...');
    if (runCommand('npm cache clean --force')) {
      printSuccess('npm cache cleared');
    } else {
      printWarning('Failed to clear npm cache, continuing...');
    }

    // Step 2: Remove node_modules and package-lock.json
    printStatus('Removing node_modules and package-lock.json...');
    removeIfExists('node_modules');
    removeIfExists('package-lock.json');
    printSuccess('Cleaned up old dependencies');

    // Step 3: Update npm to latest version
    printStatus('Updating npm to latest version...');
    if (runCommand('npm install -g npm@latest')) {
      printSuccess('npm updated');
    } else {
      printWarning('Failed to update npm, continuing...');
    }

    // Step 4: Install dependencies
    printStatus('Installing dependencies...');
    let installSuccess = false;

    // Try normal install first
    if (runCommand('npm install')) {
      printSuccess('Dependencies installed successfully!');
      installSuccess = true;
    } else {
      printError('Failed to install dependencies');
      
      // Try alternative solutions
      printStatus('Trying alternative solutions...');
      
      // Try with --legacy-peer-deps
      printStatus('Trying with --legacy-peer-deps...');
      if (runCommand('npm install --legacy-peer-deps')) {
        printSuccess('Dependencies installed with --legacy-peer-deps!');
        installSuccess = true;
      } else {
        // Try with --force
        printStatus('Trying with --force...');
        if (runCommand('npm install --force')) {
          printSuccess('Dependencies installed with --force!');
          installSuccess = true;
        } else {
          printError('All installation methods failed. Please check manually.');
          process.exit(1);
        }
      }
    }

    if (!installSuccess) {
      printError('Failed to install dependencies');
      process.exit(1);
    }

    // Step 5: Verify installation
    printStatus('Verifying installation...');
    if (fs.existsSync('node_modules') && fs.existsSync('package-lock.json')) {
      printSuccess('Installation verified successfully!');
      
      // Check if we can build the project
      printStatus('Testing build...');
      if (runCommand('npm run build')) {
        printSuccess('Build test passed!');
        console.log('\n🎉 All fixed! You can now run:');
        console.log('   npm run start:dev');
      } else {
        printWarning('Build test failed, but dependencies are installed');
      }
    } else {
      printError('Installation verification failed');
      process.exit(1);
    }

    console.log(`\n${colors.green}✅ Dependency fix completed!${colors.reset}`);

  } catch (error) {
    printError(`An error occurred: ${error.message}`);
    process.exit(1);
  }
}

// Run the fix
fixDependencies();
