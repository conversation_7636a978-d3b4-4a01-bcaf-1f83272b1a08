# Server Configuration
PORT=3000

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=password
DB_DATABASE=work_finder

# JWT Configuration
JWT_ACCESS_KEY=your-super-secret-jwt-access-key-change-this-in-production
JWT_ACCESS_EXPIRE=1d
JWT_REFRESH_KEY=your-super-secret-jwt-refresh-key-change-this-in-production
JWT_REFRESH_EXPIRE=7d

# Application Configuration
NODE_ENV=development

# File Upload Configuration
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=5242880
