# Work Finder API

A comprehensive job finder API built with NestJS, PostgreSQL, and TypeORM, designed for efficient job matching, company management, and application tracking.

## Features

- **User Management**: User registration, authentication, and profile management with role-based permissions
- **Company Management**: Company profiles, information management, and following system
- **Job Management**: Job posting, searching, application tracking, and saved jobs
- **Resume Management**: Resume upload, storage, and management
- **Application System**: Job application submission and tracking
- **Interview Scheduling**: Interview management and scheduling system
- **Notification System**: Real-time notifications for users
- **Authentication**: JWT-based authentication with Passport and role-based authorization
- **API Documentation**: Swagger/OpenAPI documentation
- **File Upload**: Support for resume and document uploads
- **Database**: PostgreSQL with TypeORM

## Tech Stack

- **Framework**: NestJS (Node.js)
- **Database**: PostgreSQL with TypeORM
- **Authentication**: JWT with Passport
- **Validation**: class-validator and class-transformer
- **Documentation**: Swagger/OpenAPI
- **File Upload**: Multer
- **Security**: Helmet, CORS, Rate limiting

## Installation

```bash
# Install dependencies
npm install
```

## Environment Variables

Create a `.env` file in the root directory with the following variables:

```env
PORT=3000
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=012003
DB_DATABASE=work_finder
JWT_ACCESS_KEY=your-super-secret-jwt-access-key-change-this-in-production
JWT_ACCESS_EXPIRE=1d
JWT_REFRESH_KEY=your-super-secret-jwt-refresh-key-change-this-in-production
JWT_REFRESH_EXPIRE=7d
NODE_ENV=development
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=5242880
```

## Database Setup

### Prerequisites
- PostgreSQL installed and running locally
- PostgreSQL user `postgres` with password `012003`

### Setup Steps

1. **Automatic Setup (Recommended):**
```bash
chmod +x scripts/setup.sh
./scripts/setup.sh
```

2. **Manual Setup:**
```bash
# Create database
PGPASSWORD=012003 createdb -h localhost -U postgres work_finder

# Load initial data
PGPASSWORD=012003 psql -h localhost -U postgres -d work_finder -f src/database/seeds/initial-data.sql

# Run migrations (if any)
npm run migration:run
```

## Running the Application

```bash
# Development
npm run start:dev

# Production mode
npm run start:prod

# Debug mode
npm run start:debug
```

## API Documentation

Once the application is running, you can access the Swagger documentation at:
http://localhost:3000/swagger

## Project Structure

```
src/
├── auth/              # Authentication module
├── users/             # User management module
├── companies/         # Company management module
├── jobs/              # Job management module
├── resumes/           # Resume management module
├── applications/      # Application management module
├── interviews/        # Interview management module
├── saved-jobs/        # Saved jobs module
├── followed-companies/ # Company following module
├── notifications/     # Notification system module
├── permissions/       # Permission management module
├── common/            # Shared utilities and decorators
├── database/          # Database configuration and entities
├── app.module.ts      # Main application module
├── app.controller.ts
├── app.service.ts
└── main.ts           # Application entry point
```

## Database Schema

The application uses the following main entities:

- **permissions**: User permission levels
- **users**: User accounts with permission references
- **companies**: Company information and profiles
- **resumes**: User resume files and metadata
- **job_posts**: Job postings by companies
- **applications**: Job applications linking users to jobs
- **interviews**: Interview scheduling for applications
- **saved_jobs**: Users saving jobs for later
- **followed_companies**: Users following companies
- **notifications**: System notifications

## Development

```bash
# Format code
npm run format

# Lint code
npm run lint

# Build application
npm run build

# Run tests
npm run test

# Run tests in watch mode
npm run test:watch

# Generate test coverage
npm run test:cov
```

## Database Migrations

```bash
# Generate a new migration
npm run migration:generate -- src/database/migrations/MigrationName

# Run migrations
npm run migration:run

# Revert last migration
npm run migration:revert
```

## License

This project is licensed under the UNLICENSED license.
