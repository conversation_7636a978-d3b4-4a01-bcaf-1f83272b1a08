{"version": 3, "sources": ["../../src/driver/sqljs/SqljsConnectionOptions.ts"], "names": [], "mappings": "", "file": "SqljsConnectionOptions.js", "sourcesContent": ["import { BaseDataSourceOptions } from \"../../data-source/BaseDataSourceOptions\"\n\n/**\n * Sql.js-specific connection options.\n */\nexport interface SqljsConnectionOptions extends BaseDataSourceOptions {\n    /**\n     * Database type.\n     */\n    readonly type: \"sqljs\"\n\n    /**\n     * A Uint8Array that gets imported when the connection is opened.\n     */\n    readonly database?: Uint8Array\n\n    /**\n     * The driver object\n     * This defaults to require(\"sql.js\")\n     */\n    readonly driver?: any\n\n    /**\n     * Config that's used to initialize sql.js.\n     */\n    readonly sqlJsConfig?: any\n\n    /**\n     * Enables the autoSave mechanism which either saves to location\n     * or calls autoSaveCallback every time a change to the database is made.\n     */\n    readonly autoSave?: boolean\n\n    /**\n     * A function that gets called on every change instead of the internal autoSave function.\n     * autoSave has to be enabled for this to work.\n     */\n    readonly autoSaveCallback?: Function\n\n    /**\n     * File path (Node.js) or local storage key (browser) to load and save database from.\n     * If this is specified without autoSave, the database is loaded from the location\n     * and can be saved manually via the SqljsEntityManager. If autoSave is enabled,\n     * location is used to automatically save the database.\n     */\n    readonly location?: string\n\n    /**\n     * Enables the usage of the localforage library to save & load the database asynchronously from the\n     * indexedDB instead of using the synchron local storage methods in a browser environment.\n     */\n    readonly useLocalForage?: boolean\n\n    readonly poolSize?: never\n}\n"], "sourceRoot": "../.."}