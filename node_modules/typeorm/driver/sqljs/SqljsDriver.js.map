{"version": 3, "sources": ["../../src/driver/sqljs/SqljsDriver.ts"], "names": [], "mappings": ";;;AAAA,kFAA8E;AAE9E,yDAAqD;AAGrD,+FAA2F;AAC3F,iFAA6E;AAC7E,gEAA4D;AAE5D,kDAA8C;AAG9C,uCAA0C;AAS1C,MAAa,WAAY,SAAQ,2CAAoB;IAIjD,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAY,UAAsB;QAC9B,KAAK,CAAC,UAAU,CAAC,CAAA;QAEjB,8EAA8E;QAC9E,uEAAuE;QACvE,IACI,IAAI,CAAC,OAAO,CAAC,QAAQ;YACrB,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ;YACtB,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAChC,CAAC;YACC,MAAM,IAAI,iDAAuB,CAAC,8BAA8B,CAAC,CAAA;QACrE,CAAC;QAED,sBAAsB;QACtB,IAAI,CAAC,gBAAgB,EAAE,CAAA;IAC3B,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,KAAK,CAAC,OAAO;QACT,IAAI,CAAC,kBAAkB,GAAG,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAA;IACnE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACZ,IAAI,CAAC,WAAW,GAAG,SAAS,CAAA;QAC5B,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAA;IACnC,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,IAAqB;QACnC,IAAI,CAAC,IAAI,CAAC,WAAW;YAAE,IAAI,CAAC,WAAW,GAAG,IAAI,mCAAgB,CAAC,IAAI,CAAC,CAAA;QAEpE,OAAO,IAAI,CAAC,WAAW,CAAA;IAC3B,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,IAAI,CACN,4BAAiD,EACjD,kCAA2C,IAAI;QAE/C,IAAI,OAAO,4BAA4B,KAAK,QAAQ,EAAE,CAAC;YACnD,2BAA2B;YAC3B,IAAI,6BAAa,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;gBAChC,UAAU;gBACV,4DAA4D;gBAC5D,IAAI,6BAAa,CAAC,SAAS,CAAC,4BAA4B,CAAC,EAAE,CAAC;oBACxD,MAAM,QAAQ,GAAG,6BAAa,CAAC,YAAY,CACvC,4BAA4B,CAC/B,CAAA;oBACD,OAAO,IAAI,CAAC,kCAAkC,CAAC,QAAQ,CAAC,CAAA;gBAC5D,CAAC;qBAAM,IAAI,+BAA+B,EAAE,CAAC;oBACzC,MAAM,IAAI,oBAAY,CAClB,QAAQ,4BAA4B,iBAAiB,CACxD,CAAA;gBACL,CAAC;qBAAM,CAAC;oBACJ,0EAA0E;oBAC1E,gEAAgE;oBAChE,iDAAiD;oBACjD,OAAO,IAAI,CAAC,kCAAkC,EAAE,CAAA;gBACpD,CAAC;YACL,CAAC;iBAAM,CAAC;gBACJ,UAAU;gBACV,yEAAyE;gBACzE,IAAI,mBAAmB,GAAG,IAAI,CAAA;gBAC9B,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;oBAC9B,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC;wBACrB,mBAAmB,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,OAAO,CAClD,4BAA4B,CAC/B,CAAA;oBACL,CAAC;yBAAM,CAAC;wBACJ,MAAM,IAAI,oBAAY,CAClB,0EAA0E,CAC7E,CAAA;oBACL,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACJ,mBAAmB;wBACf,6BAAa,CAAC,iBAAiB,EAAE,CAAC,YAAY,CAAC,OAAO,CAClD,4BAA4B,CAC/B,CAAA;gBACT,CAAC;gBAED,IAAI,mBAAmB,IAAI,IAAI,EAAE,CAAC;oBAC9B,6BAA6B;oBAC7B,OAAO,IAAI,CAAC,kCAAkC,CAC1C,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAClC,CAAA;gBACL,CAAC;qBAAM,IAAI,+BAA+B,EAAE,CAAC;oBACzC,MAAM,IAAI,oBAAY,CAClB,QAAQ,4BAA4B,iBAAiB,CACxD,CAAA;gBACL,CAAC;qBAAM,CAAC;oBACJ,wFAAwF;oBACxF,wDAAwD;oBACxD,+DAA+D;oBAC/D,OAAO,IAAI,CAAC,kCAAkC,EAAE,CAAA;gBACpD,CAAC;YACL,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,OAAO,IAAI,CAAC,kCAAkC,CAC1C,4BAA4B,CAC/B,CAAA;QACL,CAAC;IACL,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,IAAI,CAAC,QAAiB;QACxB,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;YACtC,MAAM,IAAI,oBAAY,CAClB,mGAAmG,CACtG,CAAA;QACL,CAAC;QAED,IAAI,IAAI,GAAG,EAAE,CAAA;QACb,IAAI,QAAQ,EAAE,CAAC;YACX,IAAI,GAAG,QAAQ,CAAA;QACnB,CAAC;aAAM,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;YAC/B,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAA;QAChC,CAAC;QAED,IAAI,6BAAa,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YAChC,IAAI,CAAC;gBACD,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC,CAAA;gBAC7D,MAAM,6BAAa,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;YAChD,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACT,MAAM,IAAI,oBAAY,CAAC,mCAAmC,CAAC,EAAE,CAAC,CAAA;YAClE,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,MAAM,QAAQ,GAAe,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAA;YAC7D,sEAAsE;YACtE,MAAM,aAAa,GAAG,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;YAC7C,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;gBAC9B,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC;oBACrB,MAAM,MAAM,CAAC,WAAW,CAAC,OAAO,CAC5B,IAAI,EACJ,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAChC,CAAA;gBACL,CAAC;qBAAM,CAAC;oBACJ,MAAM,IAAI,oBAAY,CAClB,0EAA0E,CAC7E,CAAA;gBACL,CAAC;YACL,CAAC;iBAAM,CAAC;gBACJ,6BAAa,CAAC,iBAAiB,EAAE,CAAC,YAAY,CAAC,OAAO,CAClD,IAAI,EACJ,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAChC,CAAA;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,QAAQ;QACV,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,mBAAmB,EAAE,CAAC;YAClE,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC;gBAChC,MAAM,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAA;YACtD,CAAC;iBAAM,CAAC;gBACJ,MAAM,IAAI,CAAC,IAAI,EAAE,CAAA;YACrB,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM;QACF,OAAO,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAA;IAC3C,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,QAAwB,EAAE,YAAiB;QAC1D,MAAM,YAAY,GAAG,QAAQ,CAAC,gBAAgB,CAAC,MAAM,CACjD,CAAC,GAAG,EAAE,eAAe,EAAE,EAAE;YACrB,mGAAmG;YACnG,IACI,eAAe,CAAC,SAAS;gBACzB,eAAe,CAAC,kBAAkB,KAAK,WAAW,EACpD,CAAC;gBACC,MAAM,KAAK,GAAG,4BAA4B,CAAA;gBAC1C,IAAI,CAAC;oBACD,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;oBAClD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;oBACtC,OAAO,mBAAQ,CAAC,SAAS,CACrB,GAAG,EACH,eAAe,CAAC,cAAc,CAC1B,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACzB,CACJ,CAAA;gBACL,CAAC;gBAAC,OAAO,CAAC,EAAE,CAAC;oBACT,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC,CAAA;gBACtD,CAAC;YACL,CAAC;YAED,OAAO,GAAG,CAAA;QACd,CAAC,EACD,EAAmB,CACtB,CAAA;QAED,OAAO,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS,CAAA;IAC1E,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;;OAGG;IACO,wBAAwB;QAC9B,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;YACxB,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAA;QAClD,CAAC;QAED,OAAO,IAAI,CAAC,kCAAkC,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;IACzE,CAAC;IAED;;;OAGG;IACO,KAAK,CAAC,kCAAkC,CAC9C,QAAqB;QAErB,2DAA2D;QAC3D,MAAM,eAAe,GAAG,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,KAAK,UAAU,CAAA;QAClE,MAAM,MAAM,GAAG,eAAe;YAC1B,CAAC,CAAC,IAAI,CAAC,MAAM;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAA;QACjD,IAAI,QAAQ,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClC,IAAI,CAAC,kBAAkB,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;QAC3D,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,kBAAkB,GAAG,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAA;QACnD,CAAC;QAED,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAA;QAExD,OAAO,IAAI,CAAC,kBAAkB,CAAA;IAClC,CAAC;IAED;;OAEG;IACO,gBAAgB;QACtB,IAAI,6BAAa,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YACnC,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,GAAG,CAAA;YAChD,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACxB,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC;gBACD,MAAM,MAAM,GACR,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,6BAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;gBACvD,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;YACxB,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACT,MAAM,IAAI,+DAA8B,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAA;YAChE,CAAC;QACL,CAAC;IACL,CAAC;CACJ;AAjSD,kCAiSC", "file": "SqljsDriver.js", "sourcesContent": ["import { AbstractSqliteDriver } from \"../sqlite-abstract/AbstractSqliteDriver\"\nimport { SqljsConnectionOptions } from \"./SqljsConnectionOptions\"\nimport { SqljsQueryRunner } from \"./SqljsQueryRunner\"\nimport { QueryRunner } from \"../../query-runner/QueryRunner\"\nimport { DataSource } from \"../../data-source/DataSource\"\nimport { DriverPackageNotInstalledError } from \"../../error/DriverPackageNotInstalledError\"\nimport { DriverOptionNotSetError } from \"../../error/DriverOptionNotSetError\"\nimport { PlatformTools } from \"../../platform/PlatformTools\"\nimport { EntityMetadata } from \"../../metadata/EntityMetadata\"\nimport { OrmUtils } from \"../../util/OrmUtils\"\nimport { ObjectLiteral } from \"../../common/ObjectLiteral\"\nimport { ReplicationMode } from \"../types/ReplicationMode\"\nimport { TypeORMError } from \"../../error\"\n\n// This is needed to satisfy the typescript compiler.\ninterface Window {\n    SQL: any\n    localforage: any\n}\ndeclare let window: Window\n\nexport class SqljsDriver extends AbstractSqliteDriver {\n    // The driver specific options.\n    options: SqljsConnectionOptions\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(connection: DataSource) {\n        super(connection)\n\n        // If autoSave is enabled by user, location or autoSaveCallback have to be set\n        // because either autoSave saves to location or calls autoSaveCallback.\n        if (\n            this.options.autoSave &&\n            !this.options.location &&\n            !this.options.autoSaveCallback\n        ) {\n            throw new DriverOptionNotSetError(`location or autoSaveCallback`)\n        }\n\n        // load sql.js package\n        this.loadDependencies()\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Performs connection to the database.\n     */\n    async connect(): Promise<void> {\n        this.databaseConnection = await this.createDatabaseConnection()\n    }\n\n    /**\n     * Closes connection with database.\n     */\n    async disconnect(): Promise<void> {\n        this.queryRunner = undefined\n        this.databaseConnection.close()\n    }\n\n    /**\n     * Creates a query runner used to execute database queries.\n     */\n    createQueryRunner(mode: ReplicationMode): QueryRunner {\n        if (!this.queryRunner) this.queryRunner = new SqljsQueryRunner(this)\n\n        return this.queryRunner\n    }\n\n    /**\n     * Loads a database from a given file (Node.js), local storage key (browser) or array.\n     * This will delete the current database!\n     */\n    async load(\n        fileNameOrLocalStorageOrData: string | Uint8Array,\n        checkIfFileOrLocalStorageExists: boolean = true,\n    ): Promise<any> {\n        if (typeof fileNameOrLocalStorageOrData === \"string\") {\n            // content has to be loaded\n            if (PlatformTools.type === \"node\") {\n                // Node.js\n                // fileNameOrLocalStorageOrData should be a path to the file\n                if (PlatformTools.fileExist(fileNameOrLocalStorageOrData)) {\n                    const database = PlatformTools.readFileSync(\n                        fileNameOrLocalStorageOrData,\n                    )\n                    return this.createDatabaseConnectionWithImport(database)\n                } else if (checkIfFileOrLocalStorageExists) {\n                    throw new TypeORMError(\n                        `File ${fileNameOrLocalStorageOrData} does not exist`,\n                    )\n                } else {\n                    // File doesn't exist and checkIfFileOrLocalStorageExists is set to false.\n                    // Therefore open a database without importing an existing file.\n                    // File will be written on first write operation.\n                    return this.createDatabaseConnectionWithImport()\n                }\n            } else {\n                // browser\n                // fileNameOrLocalStorageOrData should be a local storage / indexedDB key\n                let localStorageContent = null\n                if (this.options.useLocalForage) {\n                    if (window.localforage) {\n                        localStorageContent = await window.localforage.getItem(\n                            fileNameOrLocalStorageOrData,\n                        )\n                    } else {\n                        throw new TypeORMError(\n                            `localforage is not defined - please import localforage.js into your site`,\n                        )\n                    }\n                } else {\n                    localStorageContent =\n                        PlatformTools.getGlobalVariable().localStorage.getItem(\n                            fileNameOrLocalStorageOrData,\n                        )\n                }\n\n                if (localStorageContent != null) {\n                    // localStorage value exists.\n                    return this.createDatabaseConnectionWithImport(\n                        JSON.parse(localStorageContent),\n                    )\n                } else if (checkIfFileOrLocalStorageExists) {\n                    throw new TypeORMError(\n                        `File ${fileNameOrLocalStorageOrData} does not exist`,\n                    )\n                } else {\n                    // localStorage value doesn't exist and checkIfFileOrLocalStorageExists is set to false.\n                    // Therefore open a database without importing anything.\n                    // localStorage value will be written on first write operation.\n                    return this.createDatabaseConnectionWithImport()\n                }\n            }\n        } else {\n            return this.createDatabaseConnectionWithImport(\n                fileNameOrLocalStorageOrData,\n            )\n        }\n    }\n\n    /**\n     * Saved the current database to the given file (Node.js), local storage key (browser) or\n     * indexedDB key (browser with enabled useLocalForage option).\n     * If no location path is given, the location path in the options (if specified) will be used.\n     */\n    async save(location?: string) {\n        if (!location && !this.options.location) {\n            throw new TypeORMError(\n                `No location is set, specify a location parameter or add the location option to your configuration`,\n            )\n        }\n\n        let path = \"\"\n        if (location) {\n            path = location\n        } else if (this.options.location) {\n            path = this.options.location\n        }\n\n        if (PlatformTools.type === \"node\") {\n            try {\n                const content = Buffer.from(this.databaseConnection.export())\n                await PlatformTools.writeFile(path, content)\n            } catch (e) {\n                throw new TypeORMError(`Could not save database, error: ${e}`)\n            }\n        } else {\n            const database: Uint8Array = this.databaseConnection.export()\n            // convert Uint8Array to number array to improve local-storage storage\n            const databaseArray = [].slice.call(database)\n            if (this.options.useLocalForage) {\n                if (window.localforage) {\n                    await window.localforage.setItem(\n                        path,\n                        JSON.stringify(databaseArray),\n                    )\n                } else {\n                    throw new TypeORMError(\n                        `localforage is not defined - please import localforage.js into your site`,\n                    )\n                }\n            } else {\n                PlatformTools.getGlobalVariable().localStorage.setItem(\n                    path,\n                    JSON.stringify(databaseArray),\n                )\n            }\n        }\n    }\n\n    /**\n     * This gets called by the QueryRunner when a change to the database is made.\n     * If a custom autoSaveCallback is specified, it get's called with the database as Uint8Array,\n     * otherwise the save method is called which saves it to file (Node.js), local storage (browser)\n     * or indexedDB (browser with enabled useLocalForage option).\n     * Don't auto-save when in transaction as the call to export will end the current transaction\n     */\n    async autoSave() {\n        if (this.options.autoSave && !this.queryRunner?.isTransactionActive) {\n            if (this.options.autoSaveCallback) {\n                await this.options.autoSaveCallback(this.export())\n            } else {\n                await this.save()\n            }\n        }\n    }\n\n    /**\n     * Returns the current database as Uint8Array.\n     */\n    export(): Uint8Array {\n        return this.databaseConnection.export()\n    }\n\n    /**\n     * Creates generated map of values generated or returned by database after INSERT query.\n     */\n    createGeneratedMap(metadata: EntityMetadata, insertResult: any) {\n        const generatedMap = metadata.generatedColumns.reduce(\n            (map, generatedColumn) => {\n                // seems to be the only way to get the inserted id, see https://github.com/kripken/sql.js/issues/77\n                if (\n                    generatedColumn.isPrimary &&\n                    generatedColumn.generationStrategy === \"increment\"\n                ) {\n                    const query = \"SELECT last_insert_rowid()\"\n                    try {\n                        const result = this.databaseConnection.exec(query)\n                        this.connection.logger.logQuery(query)\n                        return OrmUtils.mergeDeep(\n                            map,\n                            generatedColumn.createValueMap(\n                                result[0].values[0][0],\n                            ),\n                        )\n                    } catch (e) {\n                        this.connection.logger.logQueryError(e, query, [])\n                    }\n                }\n\n                return map\n            },\n            {} as ObjectLiteral,\n        )\n\n        return Object.keys(generatedMap).length > 0 ? generatedMap : undefined\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates connection with the database.\n     * If the location option is set, the database is loaded first.\n     */\n    protected createDatabaseConnection(): Promise<any> {\n        if (this.options.location) {\n            return this.load(this.options.location, false)\n        }\n\n        return this.createDatabaseConnectionWithImport(this.options.database)\n    }\n\n    /**\n     * Creates connection with an optional database.\n     * If database is specified it is loaded, otherwise a new empty database is created.\n     */\n    protected async createDatabaseConnectionWithImport(\n        database?: Uint8Array,\n    ): Promise<any> {\n        // sql.js < 1.0 exposes an object with a `Database` method.\n        const isLegacyVersion = typeof this.sqlite.Database === \"function\"\n        const sqlite = isLegacyVersion\n            ? this.sqlite\n            : await this.sqlite(this.options.sqlJsConfig)\n        if (database && database.length > 0) {\n            this.databaseConnection = new sqlite.Database(database)\n        } else {\n            this.databaseConnection = new sqlite.Database()\n        }\n\n        this.databaseConnection.exec(`PRAGMA foreign_keys = ON`)\n\n        return this.databaseConnection\n    }\n\n    /**\n     * If driver dependency is not given explicitly, then try to load it via \"require\".\n     */\n    protected loadDependencies(): void {\n        if (PlatformTools.type === \"browser\") {\n            const sqlite = this.options.driver || window.SQL\n            this.sqlite = sqlite\n        } else {\n            try {\n                const sqlite =\n                    this.options.driver || PlatformTools.load(\"sql.js\")\n                this.sqlite = sqlite\n            } catch (e) {\n                throw new DriverPackageNotInstalledError(\"sql.js\", \"sql.js\")\n            }\n        }\n    }\n}\n"], "sourceRoot": "../.."}