{"version": 3, "sources": ["../../src/driver/sqlserver/SqlServerConnectionOptions.ts"], "names": [], "mappings": "", "file": "SqlServerConnectionOptions.js", "sourcesContent": ["import { BaseDataSourceOptions } from \"../../data-source/BaseDataSourceOptions\"\nimport { ReplicationMode } from \"../types/ReplicationMode\"\nimport { SqlServerConnectionCredentialsOptions } from \"./SqlServerConnectionCredentialsOptions\"\n\n/**\n * Microsoft Sql Server specific connection options.\n */\nexport interface SqlServerConnectionOptions\n    extends BaseDataSourceOptions,\n        SqlServerConnectionCredentialsOptions {\n    /**\n     * Database type.\n     */\n    readonly type: \"mssql\"\n\n    /**\n     * Connection timeout in ms (default: 15000).\n     */\n    readonly connectionTimeout?: number\n\n    /**\n     * Request timeout in ms (default: 15000). NOTE: msnodesqlv8 driver doesn't support timeouts < 1 second.\n     */\n    readonly requestTimeout?: number\n\n    /**\n     * Stream recordsets/rows instead of returning them all at once as an argument of callback (default: false).\n     * You can also enable streaming for each request independently (request.stream = true).\n     * Always set to true if you plan to work with large amount of rows.\n     */\n    readonly stream?: boolean\n\n    /**\n     * Database schema.\n     */\n    readonly schema?: string\n\n    /**\n     * The driver object\n     * This defaults to `require(\"mssql\")`\n     */\n    readonly driver?: any\n\n    /**\n     * An optional object/dictionary with the any of the properties\n     */\n    readonly pool?: {\n        /**\n         * Maximum number of resources to create at any given time. (default=1)\n         */\n        readonly max?: number\n\n        /**\n         * Minimum number of resources to keep in pool at any given time. If this is set >= max, the pool will silently\n         * set the min to equal max. (default=0)\n         */\n        readonly min?: number\n\n        /**\n         * Maximum number of queued requests allowed, additional acquire calls will be callback with an err in a future\n         * cycle of the event loop.\n         */\n        readonly maxWaitingClients?: number\n\n        /**\n         * Max milliseconds an acquire call will wait for a resource before timing out. (default no limit), if supplied should non-zero positive integer.\n         */\n        readonly acquireTimeoutMillis?: number\n\n        /**\n         * If true the oldest resources will be first to be allocated. If false the most recently released resources will\n         * be the first to be allocated. This in effect turns the pool's behaviour from a queue into a stack. boolean,\n         * (default true)\n         */\n        readonly fifo?: boolean\n\n        /**\n         * Int between 1 and x - if set, borrowers can specify their relative priority in the queue if no resources\n         * are available. see example. (default 1)\n         */\n        readonly priorityRange?: number\n\n        /**\n         * How often to run eviction checks. Default: 0 (does not run).\n         */\n        readonly evictionRunIntervalMillis?: number\n\n        /**\n         * Number of resources to check each eviction run. Default: 3.\n         */\n        readonly numTestsPerRun?: number\n\n        /**\n         * Amount of time an object may sit idle in the pool before it is eligible for eviction by the idle object\n         * evictor (if any), with the extra condition that at least \"min idle\" object instances remain in the pool.\n         * Default -1 (nothing can get evicted)\n         */\n        readonly softIdleTimeoutMillis?: number\n\n        /**\n         * The minimum amount of time that an object may sit idle in the pool before it is eligible for eviction due\n         * to idle time. Supercedes softIdleTimeoutMillis Default: 30000\n         */\n        readonly idleTimeoutMillis?: number\n\n        /*\n         * Function handling errors thrown by drivers pool.\n         * Defaults to logging error with `warn` level.\n         */\n        readonly errorHandler?: (err: any) => any\n    }\n\n    /**\n     * Extra options\n     */\n    readonly options?: {\n        /**\n         * The named instance to connect to\n         */\n        readonly instanceName?: string\n\n        /**\n         * By default, if the database requestion by options.database cannot be accessed, the connection will fail with\n         * an error. However, if options.fallbackToDefaultDb is set to true, then the user's default database will\n         * be used instead (Default: false).\n         */\n        readonly fallbackToDefaultDb?: boolean\n\n        /**\n         * If true, SET ANSI_NULL_DFLT_ON ON will be set in the initial sql. This means new columns will be nullable by\n         * default. See the T-SQL documentation for more details. (Default: true).\n         */\n        readonly enableAnsiNullDefault?: boolean\n\n        /**\n         * The number of milliseconds before the attempt to connect is considered failed (default: 15000).\n         */\n        readonly connectTimeout?: number\n\n        /**\n         * The number of milliseconds before the cancel (abort) of a request is considered failed (default: 5000).\n         */\n        readonly cancelTimeout?: number\n\n        /**\n         * The size of TDS packets (subject to negotiation with the server). Should be a power of 2. (default: 4096).\n         */\n        readonly packetSize?: number\n\n        /**\n         * A boolean determining whether to pass time values in UTC or local time. (default: false).\n         */\n        readonly useUTC?: boolean\n\n        /**\n         * A boolean determining whether to rollback a transaction automatically if any error is encountered during\n         * the given transaction's execution. This sets the value for SET XACT_ABORT during the initial SQL phase\n         * of a connection (documentation).\n         */\n        readonly abortTransactionOnError?: boolean\n\n        /**\n         * A string indicating which network interface (ip address) to use when connecting to SQL Server.\n         */\n        readonly localAddress?: string\n\n        /**\n         * A boolean determining whether to return rows as arrays or key-value collections. (default: false).\n         */\n        readonly useColumnNames?: boolean\n\n        /**\n         * A boolean, controlling whether the column names returned will have the first letter converted to lower case\n         * (true) or not. This value is ignored if you provide a columnNameReplacer. (default: false).\n         */\n        readonly camelCaseColumns?: boolean\n\n        /**\n         * A boolean, controlling whatever to disable RETURNING / OUTPUT statements.\n         */\n        readonly disableOutputReturning?: boolean\n\n        /**\n         * A boolean, controlling whether MssqlParameter types char, varchar, and text are converted to their unicode equivalents, nchar, nvarchar, and ntext.\n         * (default: false, meaning that char/varchar/text parameters will be converted to nchar/nvarchar/ntext)\n         */\n        readonly disableAsciiToUnicodeParamConversion?: boolean\n\n        /**\n         * Debug options\n         */\n        readonly debug?: {\n            /**\n             * A boolean, controlling whether debug events will be emitted with text describing packet details\n             * (default: false).\n             */\n            readonly packet?: boolean\n\n            /**\n             * A boolean, controlling whether debug events will be emitted with text describing packet data details\n             * (default: false).\n             */\n            readonly data?: boolean\n\n            /**\n             * A boolean, controlling whether debug events will be emitted with text describing packet payload details\n             * (default: false).\n             */\n            readonly payload?: boolean\n\n            /**\n             * A boolean, controlling whether debug events will be emitted with text describing token stream tokens\n             * (default: false).\n             */\n            readonly token?: boolean\n        }\n\n        /**\n         * The default isolation level that transactions will be run with. The isolation levels are available\n         * from require('tedious').ISOLATION_LEVEL. (default: READ_COMMITTED).\n         */\n        readonly isolation?:\n            | \"READ_UNCOMMITTED\"\n            | \"READ_COMMITTED\"\n            | \"REPEATABLE_READ\"\n            | \"SERIALIZABLE\"\n            | \"SNAPSHOT\"\n\n        /**\n         * The default isolation level for new connections. All out-of-transaction queries are executed with this\n         * setting. The isolation levels are available from require('tedious').ISOLATION_LEVEL .\n         */\n        readonly connectionIsolationLevel?:\n            | \"READ_UNCOMMITTED\"\n            | \"READ_COMMITTED\"\n            | \"REPEATABLE_READ\"\n            | \"SERIALIZABLE\"\n            | \"SNAPSHOT\"\n\n        /**\n         * A boolean, determining whether the connection will request read only access from a SQL Server\n         * Availability Group. For more information, see here. (default: false).\n         */\n        readonly readOnlyIntent?: boolean\n\n        /**\n         * A boolean determining whether or not the connection will be encrypted. Set to true if you're on\n         * Windows Azure. (default: true).\n         */\n        readonly encrypt?: boolean\n\n        /**\n         * When encryption is used, an object may be supplied that will be used for the first argument when calling\n         * tls.createSecurePair (default: {}).\n         */\n        readonly cryptoCredentialsDetails?: any\n\n        /**\n         * A boolean, that when true will expose received rows in Requests' done* events. See done, doneInProc and\n         * doneProc. (default: false)\n         * Caution: If many row are received, enabling this option could result in excessive memory usage.\n         */\n        readonly rowCollectionOnDone?: boolean\n\n        /**\n         * A boolean, that when true will expose received rows in Requests' completion callback. See new Request. (default: false)\n         * Caution: If many row are received, enabling this option could result in excessive memory usage.\n         */\n        readonly rowCollectionOnRequestCompletion?: boolean\n\n        /**\n         * The version of TDS to use. If server doesn't support specified version, negotiated version is used instead.\n         * The versions are available from require('tedious').TDS_VERSION. (default: 7_4).\n         */\n        readonly tdsVersion?: string\n\n        /**\n         * A boolean, that when true will abort a query when an overflow or divide-by-zero error occurs during query execution.\n         */\n        readonly enableArithAbort?: boolean\n\n        /**\n         * Application name used for identifying a specific application in profiling, logging or tracing tools of SQL Server.\n         * (default: node-mssql)\n         */\n        readonly appName?: string\n\n        /**\n         * A boolean, controlling whether encryption occurs if there is no verifiable server certificate.\n         * (default: false)\n         */\n        readonly trustServerCertificate?: boolean\n    }\n\n    /**\n     * Replication setup.\n     */\n    readonly replication?: {\n        /**\n         * Master server used by orm to perform writes.\n         */\n        readonly master: SqlServerConnectionCredentialsOptions\n\n        /**\n         * List of read-from servers (slaves).\n         */\n        readonly slaves: SqlServerConnectionCredentialsOptions[]\n\n        /**\n         * Default connection pool to use for SELECT queries\n         * @default \"slave\"\n         */\n        readonly defaultMode?: ReplicationMode\n    }\n\n    readonly poolSize?: never\n}\n"], "sourceRoot": "../.."}