{"version": 3, "sources": ["../../src/driver/sqlserver/SqlServerDriver.ts"], "names": [], "mappings": ";;;AACA,iFAA6E;AAC7E,+FAA2F;AAC3F,gDAA4C;AAE5C,iEAA6D;AAG7D,oDAAgD;AAChD,gEAA4D;AAE5D,gFAA4E;AAK5E,qDAAiD;AACjD,wEAAoE;AAGpE,kDAA8C;AAC9C,8EAA0E;AAK1E,uCAA0C;AAC1C,gEAA4D;AAE5D,kEAA8D;AAE9D;;GAEG;AACH,MAAa,eAAe;IAuNxB,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAY,UAAsB;QAvMlC;;;WAGG;QACH,WAAM,GAAU,EAAE,CAAA;QA+BlB;;WAEG;QACH,iBAAY,GAAY,KAAK,CAAA;QAE7B;;WAEG;QACH,gBAAW,GAAG,IAAI,CAAA;QAElB;;WAEG;QACH,uBAAkB,GAAG,QAAiB,CAAA;QAEtC;;;;WAIG;QACH,uBAAkB,GAAiB;YAC/B,KAAK;YACL,QAAQ;YACR,KAAK;YACL,SAAS;YACT,OAAO;YACP,SAAS;YACT,UAAU;YACV,YAAY;YACZ,SAAS;YACT,OAAO;YACP,MAAM;YACN,MAAM;YACN,WAAW;YACX,UAAU;YACV,gBAAgB;YAChB,eAAe;YACf,MAAM;YACN,MAAM;YACN,SAAS;YACT,MAAM;YACN,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,OAAO;YACP,WAAW;YACX,aAAa;YACb,aAAa;YACb,WAAW;YACX,kBAAkB;YAClB,KAAK;YACL,UAAU;YACV,WAAW;YACX,YAAY;SACf,CAAA;QAED;;WAEG;QACH,yBAAoB,GAAiB,CAAC,YAAY,CAAC,CAAA;QAEnD;;WAEG;QACH,iBAAY,GAAiB,CAAC,UAAU,EAAE,WAAW,CAAC,CAAA;QAEtD;;WAEG;QACH,0BAAqB,GAAiB;YAClC,MAAM;YACN,SAAS;YACT,OAAO;YACP,UAAU;YACV,QAAQ;YACR,WAAW;SACd,CAAA;QAED;;WAEG;QACH,6BAAwB,GAAiB;YACrC,SAAS;YACT,SAAS;YACT,MAAM;YACN,WAAW;YACX,gBAAgB;SACnB,CAAA;QAED;;WAEG;QACH,yBAAoB,GAAiB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAA;QAE3D;;;WAGG;QACH,oBAAe,GAAsB;YACjC,UAAU,EAAE,WAAW;YACvB,iBAAiB,EAAE,WAAW;YAC9B,UAAU,EAAE,WAAW;YACvB,iBAAiB,EAAE,WAAW;YAC9B,UAAU,EAAE,WAAW;YACvB,kBAAkB,EAAE,IAAI;YACxB,OAAO,EAAE,KAAK;YACd,SAAS,EAAE,KAAK;YAChB,WAAW,EAAE,KAAK;YAClB,aAAa,EAAE,SAAS;YACxB,kBAAkB,EAAE,QAAQ;YAC5B,OAAO,EAAE,KAAK;YACd,eAAe,EAAE,UAAU;YAC3B,SAAS,EAAE,QAAQ;YACnB,aAAa,EAAE,KAAK;YACpB,UAAU,EAAE,eAAsB;YAClC,WAAW,EAAE,eAAsB;YACnC,YAAY,EAAE,SAAS;YACvB,gBAAgB,EAAE,SAAS;YAC3B,cAAc,EAAE,SAAS;YACzB,aAAa,EAAE,SAAS;YACxB,YAAY,EAAE,SAAS;YACvB,aAAa,EAAE,eAAsB;SACxC,CAAA;QAED;;WAEG;QACH,qBAAgB,GAAW,GAAG,CAAA;QAE9B;;;WAGG;QACH,qBAAgB,GAAqB;YACjC,IAAI,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE;YACnB,KAAK,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE;YACpB,OAAO,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE;YACxB,QAAQ,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE;YACzB,MAAM,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE;YACrB,SAAS,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE;YACxB,OAAO,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE;YACpC,OAAO,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE;YACpC,IAAI,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE;YACtB,SAAS,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE;YAC3B,cAAc,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE;SACnC,CAAA;QAED,oBAAe,GAAoB;YAC/B,OAAO,EAAE,IAAI;YACb,gIAAgI;YAChI,QAAQ,EAAE,KAAK;SAClB,CAAA;QAED;;;WAGG;QACH,mBAAc,GAAG,GAAG,CAAA;QAOhB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAA;QAC5B,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,OAAqC,CAAA;QAC/D,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAA;QAE3D,qBAAqB;QACrB,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAEvB,IAAI,CAAC,QAAQ,GAAG,yBAAW,CAAC,kBAAkB,CAC1C,IAAI,CAAC,OAAO,CAAC,WAAW;YACpB,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM;YACjC,CAAC,CAAC,IAAI,CAAC,OAAO,CACrB,CAAC,QAAQ,CAAA;QACV,IAAI,CAAC,MAAM,GAAG,yBAAW,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,CAAA;QAEjE,mHAAmH;QACnH,kDAAkD;QAClD,0BAA0B;QAC1B,6CAA6C;QAC7C,8BAA8B;QAC9B,qDAAqD;QACrD,8BAA8B;QAC9B,qDAAqD;IACzD,CAAC;IAED,4EAA4E;IAC5E,6BAA6B;IAC7B,4EAA4E;IAE5E;;;;OAIG;IACH,KAAK,CAAC,OAAO;QACT,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;YAC3B,IAAI,CAAC,MAAM,GAAG,MAAM,OAAO,CAAC,GAAG,CAC3B,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;gBAC1C,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;YAC/C,CAAC,CAAC,CACL,CAAA;YACD,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAC/B,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAClC,CAAA;QACL,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;QACnE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACvC,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAA;YAEpD,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACjB,IAAI,CAAC,QAAQ,GAAG,MAAM,WAAW,CAAC,kBAAkB,EAAE,CAAA;YAC1D,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;gBACrB,IAAI,CAAC,YAAY,GAAG,MAAM,WAAW,CAAC,gBAAgB,EAAE,CAAA;YAC5D,CAAC;YAED,MAAM,WAAW,CAAC,OAAO,EAAE,CAAA;QAC/B,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAA;QACnC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,YAAY;QACR,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACZ,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACf,MAAM,IAAI,iDAAuB,CAAC,OAAO,CAAC,CAAA;QAC9C,CAAC;QACD,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QACjC,MAAM,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QACpE,IAAI,CAAC,MAAM,GAAG,SAAS,CAAA;QACvB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAA;IACpB,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,SAAS,CAAC,IAAS;QAC/B,OAAO,IAAI,OAAO,CAAO,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;YAClC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAQ,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAA;QACtD,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACH,mBAAmB;QACf,OAAO,IAAI,uCAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;IAClD,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,IAAqB;QACnC,OAAO,IAAI,2CAAoB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;IAC/C,CAAC;IAED;;;OAGG;IACH,yBAAyB,CACrB,GAAW,EACX,UAAyB,EACzB,gBAA+B;QAE/B,MAAM,iBAAiB,GAAU,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,GAAG,CAC9D,CAAC,GAAG,EAAE,EAAE,CAAC,gBAAgB,CAAC,GAAG,CAAC,CACjC,CAAA;QACD,IAAI,CAAC,UAAU,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM;YAC9C,OAAO,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAA;QAEnC,MAAM,iBAAiB,GAAG,IAAI,GAAG,EAAkB,CAAA;QACnD,GAAG,GAAG,GAAG,CAAC,OAAO,CACb,6BAA6B,EAC7B,CAAC,IAAI,EAAE,OAAe,EAAE,GAAW,EAAU,EAAE;YAC3C,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC;gBAClC,OAAO,IAAI,CAAA;YACf,CAAC;YAED,IAAI,iBAAiB,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC7B,OAAO,IAAI,CAAC,gBAAgB,GAAG,iBAAiB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;YAC7D,CAAC;YAED,MAAM,KAAK,GAAQ,UAAU,CAAC,GAAG,CAAC,CAAA;YAElC,IAAI,OAAO,EAAE,CAAC;gBACV,OAAO,KAAK;qBACP,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE;oBACZ,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;oBACzB,OAAO,IAAI,CAAC,eAAe,CACvB,GAAG,EACH,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAC/B,CAAA;gBACL,CAAC,CAAC;qBACD,IAAI,CAAC,IAAI,CAAC,CAAA;YACnB,CAAC;YAED,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE,CAAC;gBAC9B,OAAO,KAAK,EAAE,CAAA;YAClB,CAAC;YAED,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YAC7B,iBAAiB,CAAC,GAAG,CAAC,GAAG,EAAE,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;YACxD,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;QAClE,CAAC,CACJ,CAAA,CAAC,kEAAkE;QACpE,OAAO,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAA;IACnC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,UAAkB;QACrB,OAAO,IAAI,UAAU,GAAG,CAAA;IAC5B,CAAC;IAED;;;OAGG;IACH,cAAc,CACV,SAAiB,EACjB,MAAe,EACf,QAAiB;QAEjB,MAAM,SAAS,GAAG,CAAC,SAAS,CAAC,CAAA;QAE7B,IAAI,MAAM,EAAE,CAAC;YACT,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;QAC7B,CAAC;QAED,IAAI,QAAQ,EAAE,CAAC;YACX,IAAI,CAAC,MAAM,EAAE,CAAC;gBACV,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC,CAAA;YACzB,CAAC;YAED,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;QAC/B,CAAC;QAED,OAAO,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IAC9B,CAAC;IAED;;OAEG;IACH,cAAc,CACV,MAAgE;QAEhE,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAA;QACpC,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAA;QAEhC,IAAI,iCAAe,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,iCAAe,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;YACpE,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;YAE/C,OAAO;gBACH,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,IAAI,cAAc;gBAC9D,MAAM,EAAE,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,IAAI,YAAY;gBACtD,SAAS,EAAE,MAAM,CAAC,SAAS;aAC9B,CAAA;QACL,CAAC;QAED,IAAI,iCAAe,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE,CAAC;YAC5C,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAA;YAE9D,OAAO;gBACH,QAAQ,EACJ,MAAM,CAAC,kBAAkB;oBACzB,MAAM,CAAC,QAAQ;oBACf,cAAc;gBAClB,MAAM,EACF,MAAM,CAAC,gBAAgB,IAAI,MAAM,CAAC,MAAM,IAAI,YAAY;gBAC5D,SAAS,EAAE,MAAM,CAAC,SAAS;aAC9B,CAAA;QACL,CAAC;QAED,IAAI,iCAAe,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC;YAC3C,2CAA2C;YAE3C,OAAO;gBACH,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,cAAc;gBAC3C,MAAM,EAAE,MAAM,CAAC,MAAM,IAAI,YAAY;gBACrC,SAAS,EAAE,MAAM,CAAC,SAAS;aAC9B,CAAA;QACL,CAAC;QAED,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QAE/B,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrB,OAAO;gBACH,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,cAAc;gBACpC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,YAAY;gBAChC,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC;aACtB,CAAA;QACL,CAAC;aAAM,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC5B,OAAO;gBACH,QAAQ,EAAE,cAAc;gBACxB,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;gBAChB,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC;aACtB,CAAA;QACL,CAAC;aAAM,CAAC;YACJ,OAAO;gBACH,QAAQ,EAAE,cAAc;gBACxB,MAAM,EAAE,YAAY;gBACpB,SAAS,EAAE,MAAM;aACpB,CAAA;QACL,CAAC;IACL,CAAC;IAED;;OAEG;IACH,sBAAsB,CAAC,KAAU,EAAE,cAA8B;QAC7D,IAAI,cAAc,CAAC,WAAW;YAC1B,KAAK,GAAG,+CAAsB,CAAC,WAAW,CACtC,cAAc,CAAC,WAAW,EAC1B,KAAK,CACR,CAAA;QAEL,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS;YAAE,OAAO,KAAK,CAAA;QAEvD,IAAI,cAAc,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YAClC,OAAO,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QACjC,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACxC,OAAO,qBAAS,CAAC,eAAe,CAAC,KAAK,CAAC,CAAA;QAC3C,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACxC,OAAO,qBAAS,CAAC,eAAe,CAAC,KAAK,CAAC,CAAA;QAC3C,CAAC;aAAM,IACH,cAAc,CAAC,IAAI,KAAK,UAAU;YAClC,cAAc,CAAC,IAAI,KAAK,eAAe;YACvC,cAAc,CAAC,IAAI,KAAK,IAAI,EAC9B,CAAC;YACC,OAAO,qBAAS,CAAC,eAAe,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAA;QACzD,CAAC;aAAM,IACH,cAAc,CAAC,IAAI,KAAK,WAAW;YACnC,cAAc,CAAC,IAAI,KAAK,gBAAgB,EAC1C,CAAC;YACC,OAAO,qBAAS,CAAC,eAAe,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;QACxD,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;YAChD,OAAO,qBAAS,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAA;QAC/C,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;YAC/C,OAAO,qBAAS,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAA;QAC9C,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;YAC/C,OAAO,qBAAS,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAA;QAC9C,CAAC;QAED,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,KAAU,EAAE,cAA8B;QAC3D,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS;YACrC,OAAO,cAAc,CAAC,WAAW;gBAC7B,CAAC,CAAC,+CAAsB,CAAC,aAAa,CAChC,cAAc,CAAC,WAAW,EAC1B,KAAK,CACR;gBACH,CAAC,CAAC,KAAK,CAAA;QAEf,IAAI,cAAc,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YAClC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAA;QAChC,CAAC;aAAM,IACH,cAAc,CAAC,IAAI,KAAK,UAAU;YAClC,cAAc,CAAC,IAAI,KAAK,IAAI;YAC5B,cAAc,CAAC,IAAI,KAAK,WAAW;YACnC,cAAc,CAAC,IAAI,KAAK,eAAe;YACvC,cAAc,CAAC,IAAI,KAAK,gBAAgB,EAC1C,CAAC;YACC,KAAK,GAAG,qBAAS,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAA;QAClD,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACxC,KAAK,GAAG,qBAAS,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAA;QAClD,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACxC,KAAK,GAAG,qBAAS,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAA;QAC9C,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;YAChD,KAAK,GAAG,qBAAS,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAA;QAChD,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;YAC/C,KAAK,GAAG,qBAAS,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAA;QAC/C,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;YAC/C,KAAK,GAAG,qBAAS,CAAC,kBAAkB,CAAC,KAAK,EAAE,cAAc,CAAC,CAAA;QAC/D,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACxC,8BAA8B;YAC9B,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAA;QACpD,CAAC;QAED,IAAI,cAAc,CAAC,WAAW;YAC1B,KAAK,GAAG,+CAAsB,CAAC,aAAa,CACxC,cAAc,CAAC,WAAW,EAC1B,KAAK,CACR,CAAA;QAEL,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,MAKb;QACG,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YACtD,OAAO,KAAK,CAAA;QAChB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YAChC,OAAO,UAAU,CAAA;QACrB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;YAC9B,OAAO,UAAU,CAAA;QACrB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YACjC,OAAO,KAAK,CAAA;QAChB,CAAC;aAAM,IAAK,MAAM,CAAC,IAAY,KAAK,MAAM,EAAE,CAAC;YACzC,OAAO,QAAQ,CAAA;QACnB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YAChC,OAAO,kBAAkB,CAAA;QAC7B,CAAC;aAAM,IACH,MAAM,CAAC,IAAI,KAAK,cAAc;YAC9B,MAAM,CAAC,IAAI,KAAK,aAAa,EAC/B,CAAC;YACC,OAAO,OAAO,CAAA;QAClB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;YACvC,OAAO,UAAU,CAAA;QACrB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC;YAC/B,OAAO,SAAS,CAAA;QACpB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,kBAAkB,EAAE,CAAC;YAC5C,OAAO,OAAO,CAAA;QAClB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;YACtC,OAAO,WAAW,CAAA,CAAC,iEAAiE;QACxF,CAAC;aAAM,CAAC;YACJ,OAAQ,MAAM,CAAC,IAAe,IAAI,EAAE,CAAA;QACxC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,cAA8B;QAC3C,MAAM,YAAY,GAAG,cAAc,CAAC,OAAO,CAAA;QAE3C,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE,CAAC;YACnC,OAAO,GAAG,YAAY,EAAE,CAAA;QAC5B,CAAC;QAED,IAAI,OAAO,YAAY,KAAK,SAAS,EAAE,CAAC;YACpC,OAAO,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;QACnC,CAAC;QAED,IAAI,OAAO,YAAY,KAAK,UAAU,EAAE,CAAC;YACrC,MAAM,KAAK,GAAG,YAAY,EAAE,CAAA;YAC5B,IAAI,KAAK,CAAC,WAAW,EAAE,KAAK,mBAAmB,EAAE,CAAC;gBAC9C,OAAO,WAAW,CAAA;YACtB,CAAC;YACD,OAAO,KAAK,CAAA;QAChB,CAAC;QAED,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE,CAAC;YACnC,OAAO,IAAI,YAAY,GAAG,CAAA;QAC9B,CAAC;QAED,IAAI,YAAY,KAAK,SAAS,IAAI,YAAY,KAAK,IAAI,EAAE,CAAC;YACtD,OAAO,SAAS,CAAA;QACpB,CAAC;QAED,OAAO,GAAG,YAAY,EAAE,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,MAAsB;QACpC,OAAO,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CACrC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM,CAC9D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,MAAoC;QAChD,IAAI,MAAM,CAAC,MAAM;YAAE,OAAO,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAA;QAElD,IACI,MAAM,CAAC,IAAI,KAAK,SAAS;YACzB,MAAM,CAAC,IAAI,KAAK,UAAU;YAC1B,MAAM,CAAC,IAAI,KAAK,MAAM;YAEtB,OAAO,KAAK,CAAA;QAEhB,OAAO,EAAE,CAAA;IACb,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,MAAmB;QAC9B,4FAA4F;QAC5F,uEAAuE;QACvE,IAAI,MAAM,CAAC,YAAY;YAAE,OAAO,EAAE,CAAA;QAElC,IAAI,IAAI,GAAG,MAAM,CAAC,IAAI,CAAA;QAEtB,2GAA2G;QAC3G,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,CAAC;YAC/B,IAAI,IAAI,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,GAAG,CAAA;QAC/C,CAAC;aAAM,IACH,MAAM,CAAC,SAAS,KAAK,IAAI;YACzB,MAAM,CAAC,SAAS,KAAK,SAAS;YAC9B,MAAM,CAAC,KAAK,KAAK,IAAI;YACrB,MAAM,CAAC,KAAK,KAAK,SAAS,EAC5B,CAAC;YACC,IAAI,IAAI,IAAI,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,KAAK,GAAG,CAAA;QACnD,CAAC;aAAM,IACH,MAAM,CAAC,SAAS,KAAK,IAAI;YACzB,MAAM,CAAC,SAAS,KAAK,SAAS,EAChC,CAAC;YACC,IAAI,IAAI,IAAI,MAAM,CAAC,SAAS,GAAG,CAAA;QACnC,CAAC;QAED,IAAI,MAAM,CAAC,OAAO;YAAE,IAAI,IAAI,QAAQ,CAAA;QAEpC,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;;;OAIG;IACH,sBAAsB;QAClB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACf,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,oBAAY,CAAC,sBAAsB,CAAC,CAAC,CAAA;QACnE,CAAC;QAED,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;IACvC,CAAC;IAED;;;;OAIG;IACH,qBAAqB;QACjB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM;YAAE,OAAO,IAAI,CAAC,sBAAsB,EAAE,CAAA;QAE7D,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;QAC7D,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,QAAwB,EAAE,YAA2B;QACpE,IAAI,CAAC,YAAY;YAAE,OAAO,SAAS,CAAA;QAEnC,OAAO,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;YACjD,MAAM,MAAM,GAAG,QAAQ,CAAC,0BAA0B,CAAC,GAAG,CAAC,CAAA;YACvD,IAAI,MAAM,EAAE,CAAC;gBACT,mBAAQ,CAAC,SAAS,CACd,GAAG,EACH,MAAM,CAAC,cAAc,CACjB,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,CACvD,CACJ,CAAA;YACL,CAAC;YACD,OAAO,GAAG,CAAA;QACd,CAAC,EAAE,EAAmB,CAAC,CAAA;IAC3B,CAAC;IAED;;;OAGG;IACH,kBAAkB,CACd,YAA2B,EAC3B,eAAiC;QAEjC,OAAO,eAAe,CAAC,MAAM,CAAC,CAAC,cAAc,EAAE,EAAE;YAC7C,MAAM,WAAW,GAAG,YAAY,CAAC,IAAI,CACjC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,cAAc,CAAC,YAAY,CAChD,CAAA;YACD,IAAI,CAAC,WAAW;gBAAE,OAAO,KAAK,CAAA,CAAC,4DAA4D;YAE3F,MAAM,eAAe,GACjB,WAAW,CAAC,IAAI,KAAK,cAAc,CAAC,YAAY;gBAChD,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,cAAc,CAAC;gBACnD,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,cAAc,CAAC;gBACrD,WAAW,CAAC,SAAS,KAAK,cAAc,CAAC,SAAS;gBAClD,WAAW,CAAC,KAAK,KAAK,cAAc,CAAC,KAAK;gBAC1C,+DAA+D;gBAC/D,WAAW,CAAC,WAAW,KAAK,cAAc,CAAC,WAAW;gBACtD,CAAC,CAAC,WAAW,CAAC,WAAW;oBACrB,IAAI,CAAC,4BAA4B,CAC7B,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,CACxC;wBACG,IAAI,CAAC,4BAA4B,CAC7B,WAAW,CAAC,OAAO,CACtB,CAAC,IAAI,kGAAkG;gBAChH,WAAW,CAAC,SAAS,KAAK,cAAc,CAAC,SAAS;gBAClD,WAAW,CAAC,UAAU,KAAK,cAAc,CAAC,UAAU;gBACpD,WAAW,CAAC,YAAY,KAAK,cAAc,CAAC,YAAY;gBACxD,WAAW,CAAC,aAAa,KAAK,cAAc,CAAC,aAAa;gBAC1D,WAAW,CAAC,QAAQ;oBAChB,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC;gBAC1C,CAAC,WAAW,CAAC,IAAI;oBACb,cAAc,CAAC,IAAI;oBACnB,CAAC,mBAAQ,CAAC,aAAa,CACnB,WAAW,CAAC,IAAI,EAChB,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,CAC7C,CAAC,CAAA;YAEV,gBAAgB;YAChB,yBAAyB;YACzB,qEAAqE;YACrE,mBAAmB;YACnB,mBAAmB;YACnB,4BAA4B;YAC5B,uCAAuC;YACvC,QAAQ;YACR,mBAAmB;YACnB,mBAAmB;YACnB,4BAA4B;YAC5B,8CAA8C;YAC9C,+DAA+D;YAC/D,QAAQ;YACR,mBAAmB;YACnB,qBAAqB;YACrB,8BAA8B;YAC9B,iCAAiC;YACjC,iEAAiE;YACjE,QAAQ;YACR,mBAAmB;YACnB,wBAAwB;YACxB,iCAAiC;YACjC,oCAAoC;YACpC,QAAQ;YACR,qEAAqE;YACrE,mBAAmB;YACnB,0BAA0B;YAC1B,mCAAmC;YACnC,sCAAsC;YACtC,QAAQ;YACR,mBAAmB;YACnB,4BAA4B;YAC5B,sCAAsC;YACtC,iDAAiD;YACjD,yDAAyD;YACzD,oBAAoB;YACpB,qDAAqD;YACrD,2CAA2C;YAC3C,qBAAqB;YACrB,QAAQ;YACR,mBAAmB;YACnB,wBAAwB;YACxB,iCAAiC;YACjC,oCAAoC;YACpC,QAAQ;YACR,mBAAmB;YACnB,yBAAyB;YACzB,kCAAkC;YAClC,qCAAqC;YACrC,QAAQ;YACR,mBAAmB;YACnB,2BAA2B;YAC3B,oCAAoC;YACpC,uCAAuC;YACvC,QAAQ;YACR,mBAAmB;YACnB,4BAA4B;YAC5B,qCAAqC;YACrC,wCAAwC;YACxC,QAAQ;YACR,mBAAmB;YACnB,uBAAuB;YACvB,gCAAgC;YAChC,kDAAkD;YAClD,QAAQ;YACR,gEAAgE;YAChE,IAAI;YAEJ,OAAO,eAAe,CAAA;QAC1B,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACH,uBAAuB;QACnB,IACI,IAAI,CAAC,OAAO,CAAC,OAAO;YACpB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,sBAAsB,EAC7C,CAAC;YACC,OAAO,KAAK,CAAA;QAChB,CAAC;QACD,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;OAEG;IACH,yBAAyB;QACrB,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;OAEG;IACH,6BAA6B;QACzB,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,aAAqB,EAAE,KAAa;QAChD,OAAO,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAA;IACxC,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;;OAGG;IACH,gBAAgB,CAAC,MAAsB,EAAE,KAAU;QAC/C,sDAAsD;QACtD,IAAI,iCAAe,CAAC,gBAAgB,CAAC,KAAK,CAAC;YAAE,OAAO,KAAK,CAAA;QAEzD,MAAM,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,CAAC,CAAA;QAChE,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YAChB,OAAO,IAAI,+BAAc,CACrB,KAAK,EACL,cAAqB,EACrB,MAAM,CAAC,MAAa,CACvB,CAAA;QACL,CAAC;aAAM,IACH,MAAM,CAAC,SAAS,KAAK,IAAI;YACzB,MAAM,CAAC,SAAS,KAAK,SAAS;YAC9B,MAAM,CAAC,KAAK,KAAK,IAAI;YACrB,MAAM,CAAC,KAAK,KAAK,SAAS,EAC5B,CAAC;YACC,OAAO,IAAI,+BAAc,CACrB,KAAK,EACL,cAAqB,EACrB,MAAM,CAAC,SAAS,EAChB,MAAM,CAAC,KAAK,CACf,CAAA;QACL,CAAC;aAAM,IACH,MAAM,CAAC,SAAS,KAAK,IAAI;YACzB,MAAM,CAAC,SAAS,KAAK,SAAS,EAChC,CAAC;YACC,OAAO,IAAI,+BAAc,CACrB,KAAK,EACL,cAAqB,EACrB,MAAM,CAAC,SAAS,CACnB,CAAA;QACL,CAAC;aAAM,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,IAAI,MAAM,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;YAC7D,OAAO,IAAI,+BAAc,CACrB,KAAK,EACL,cAAqB,EACrB,MAAM,CAAC,KAAK,CACf,CAAA;QACL,CAAC;QAED,OAAO,IAAI,+BAAc,CAAC,KAAK,EAAE,cAAqB,CAAC,CAAA;IAC3D,CAAC;IAED;;;;;;;;;;OAUG;IACH,iBAAiB,CAAC,MAAsB,EAAE,KAAU;QAChD,IAAI,KAAK,YAAY,2BAAY,EAAE,CAAC;YAChC,IAAI,KAAK,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC;gBACvB,KAAK,CAAC,cAAc,CAAC;oBACjB,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC,CAAC;oBAC5C,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;iBACjB,CAAC,CAAA;YACN,CAAC;YAED,OAAO,KAAK,CAAA;QAChB,CAAC;QAED,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;IAC/C,CAAC;IAED;;;OAGG;IACH,cAAc,CAAC,SAAiB,EAAE,GAAkB;QAChD,oCAAoC;QACpC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,SAAS,CAAC;YACvC,2FAA2F;YAC3F,OAAO,GAAG,CAAA;QACd,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,SAAS,CAAC,CAAA;QAEvD,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE;YAC3C,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,CAAA;YAEtB,uBAAuB;YACvB,MAAM,MAAM,GAAG,QAAQ,CAAC,0BAA0B,CAAC,GAAG,CAAC,CAAA;YACvD,IAAI,CAAC,MAAM;gBACP,uFAAuF;gBACvF,OAAO,KAAK,CAAA;YAEhB,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;YAClD,OAAO,MAAM,CAAA;QACjB,CAAC,EAAE,EAAmB,CAAC,CAAA;IAC3B,CAAC;IAED,6BAA6B,CACzB,UAAkB,EAClB,OAAyB;QAEzB,MAAM,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;YACzC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,IAAI,CAAC,cAAc,CAC7D,IAAI,yBAAW,CAAC;gBACZ,IAAI,EAAE,MAAM,CAAC,YAAY;gBACzB,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;gBAChC,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,UAAU,EAAE,MAAM,CAAC,UAAU;gBAC7B,OAAO,EAAE,MAAM,CAAC,OAAO;aAC1B,CAAC,CACL,EAAE,CAAA;QACP,CAAC,CAAC,CAAA;QAEF,OAAO,WAAW,UAAU,WAAW,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAA;IACtE,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;OAEG;IACO,gBAAgB;QACtB,IAAI,CAAC;YACD,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,6BAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YAChE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAA;QACtB,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,qCAAqC;YACrC,MAAM,IAAI,+DAA8B,CAAC,YAAY,EAAE,OAAO,CAAC,CAAA;QACnE,CAAC;IACL,CAAC;IAES,iBAAiB,CACvB,WAAwB,EACxB,cAA8B;QAE9B,4FAA4F;QAC5F,uEAAuE;QACvE,IAAI,cAAc,CAAC,YAAY;YAAE,OAAO,KAAK,CAAA;QAE7C,OAAO,WAAW,CAAC,IAAI,KAAK,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAA;IAClE,CAAC;IAES,mBAAmB,CACzB,WAAwB,EACxB,cAA8B;QAE9B,4FAA4F;QAC5F,uEAAuE;QACvE,IAAI,cAAc,CAAC,YAAY;YAAE,OAAO,KAAK,CAAA;QAE7C,OAAO,CACH,WAAW,CAAC,MAAM,CAAC,WAAW,EAAE;YAChC,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC,WAAW,EAAE,CACrD,CAAA;IACL,CAAC;IAES,4BAA4B,CAAC,KAAyB;QAC5D,8GAA8G;QAC9G,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,OAAO,KAAK,CAAA;QAChB,CAAC;QACD,OAAO,KAAK;aACP,KAAK,CAAC,GAAG,CAAC;aACV,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YACV,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAA;QAC5C,CAAC,CAAC;aACD,IAAI,CAAC,GAAG,CAAC,CAAA;IAClB,CAAC;IAED;;OAEG;IACO,UAAU,CAChB,OAAmC,EACnC,WAAkD;QAElD,WAAW,GAAG,MAAM,CAAC,MAAM,CACvB,EAAE,EACF,WAAW,EACX,yBAAW,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAC9C,CAAA,CAAC,yBAAyB;QAE3B,+DAA+D;QAC/D,MAAM,cAAc,GAAG,CAAC,WAAW,CAAC,MAAM;YACtC,CAAC,CAAC,WAAW,CAAC,cAAc;YAC5B,CAAC,CAAC;gBACI,IAAI,EAAE,MAAM;gBACZ,OAAO,EAAE;oBACL,MAAM,EAAE,WAAW,CAAC,MAAM;oBAC1B,QAAQ,EAAE,WAAW,CAAC,QAAQ;oBAC9B,QAAQ,EAAE,WAAW,CAAC,QAAQ;iBACjC;aACJ,CAAA;QACP,0CAA0C;QAC1C,MAAM,iBAAiB,GAAG,MAAM,CAAC,MAAM,CACnC,EAAE,EACF;YACI,iBAAiB,EAAE,IAAI,CAAC,OAAO,CAAC,iBAAiB;YACjD,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc;YAC3C,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;YAC3B,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI;YACvB,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO;SAChC,EACD;YACI,MAAM,EAAE,WAAW,CAAC,IAAI;YACxB,QAAQ,EAAE,WAAW,CAAC,QAAQ;YAC9B,IAAI,EAAE,WAAW,CAAC,IAAI;YACtB,IAAI,EAAE,WAAW,CAAC,QAAQ;YAC1B,QAAQ,EAAE,WAAW,CAAC,QAAQ;YAC9B,cAAc,EAAE,cAAc;SACjC,EACD,OAAO,CAAC,KAAK,IAAI,EAAE,CACtB,CAAA;QAED,kDAAkD;QAClD,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;YAC7B,iBAAiB,CAAC,OAAO,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,CAAA;QACjD,CAAC;aAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,OAAO,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAA;QAC/D,CAAC;QAED,8DAA8D;QAC9D,kCAAkC;QAClC,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,OAAO,EAAE,EAAE,gBAAgB,EAAE,IAAI,EAAE,CAAC,CAAA;QAEpE,6DAA6D;QAC7D,+DAA+D;QAC/D,OAAO,IAAI,OAAO,CAAO,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;YAClC,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAA;YAE7D,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAA;YAElC,MAAM,gBAAgB,GAClB,CAAC,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC;gBAC3C,CAAC,CAAC,KAAU,EAAE,EAAE,CACZ,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,+BAA+B,KAAK,EAAE,CAAC,CAAC,CAAA;YACnE;;;eAGG;YACH,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAA;YAElC,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,GAAQ,EAAE,EAAE;gBACzC,IAAI,GAAG;oBAAE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAA;gBACzB,EAAE,CAAC,UAAU,CAAC,CAAA;YAClB,CAAC,CAAC,CAAA;QACN,CAAC,CAAC,CAAA;IACN,CAAC;CACJ;AA1nCD,0CA0nCC", "file": "SqlServerDriver.js", "sourcesContent": ["import { Driver } from \"../Driver\"\nimport { ConnectionIsNotSetError } from \"../../error/ConnectionIsNotSetError\"\nimport { DriverPackageNotInstalledError } from \"../../error/DriverPackageNotInstalledError\"\nimport { DriverUtils } from \"../DriverUtils\"\nimport { CteCapabilities } from \"../types/CteCapabilities\"\nimport { SqlServerQueryRunner } from \"./SqlServerQueryRunner\"\nimport { ObjectLiteral } from \"../../common/ObjectLiteral\"\nimport { ColumnMetadata } from \"../../metadata/ColumnMetadata\"\nimport { DateUtils } from \"../../util/DateUtils\"\nimport { PlatformTools } from \"../../platform/PlatformTools\"\nimport { DataSource } from \"../../data-source/DataSource\"\nimport { RdbmsSchemaBuilder } from \"../../schema-builder/RdbmsSchemaBuilder\"\nimport { SqlServerConnectionOptions } from \"./SqlServerConnectionOptions\"\nimport { MappedColumnTypes } from \"../types/MappedColumnTypes\"\nimport { ColumnType } from \"../types/ColumnTypes\"\nimport { DataTypeDefaults } from \"../types/DataTypeDefaults\"\nimport { MssqlParameter } from \"./MssqlParameter\"\nimport { TableColumn } from \"../../schema-builder/table/TableColumn\"\nimport { SqlServerConnectionCredentialsOptions } from \"./SqlServerConnectionCredentialsOptions\"\nimport { EntityMetadata } from \"../../metadata/EntityMetadata\"\nimport { OrmUtils } from \"../../util/OrmUtils\"\nimport { ApplyValueTransformers } from \"../../util/ApplyValueTransformers\"\nimport { ReplicationMode } from \"../types/ReplicationMode\"\nimport { Table } from \"../../schema-builder/table/Table\"\nimport { View } from \"../../schema-builder/view/View\"\nimport { TableForeignKey } from \"../../schema-builder/table/TableForeignKey\"\nimport { TypeORMError } from \"../../error\"\nimport { InstanceChecker } from \"../../util/InstanceChecker\"\nimport { UpsertType } from \"../types/UpsertType\"\nimport { FindOperator } from \"../../find-options/FindOperator\"\n\n/**\n * Organizes communication with SQL Server DBMS.\n */\nexport class SqlServerDriver implements Driver {\n    // -------------------------------------------------------------------------\n    // Public Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Connection used by driver.\n     */\n    connection: DataSource\n\n    /**\n     * SQL Server library.\n     */\n    mssql: any\n\n    /**\n     * Pool for master database.\n     */\n    master: any\n\n    /**\n     * Pool for slave databases.\n     * Used in replication.\n     */\n    slaves: any[] = []\n\n    // -------------------------------------------------------------------------\n    // Public Implemented Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Connection options.\n     */\n    options: SqlServerConnectionOptions\n\n    /**\n     * Database name used to perform all write queries.\n     */\n    database?: string\n\n    /**\n     * Schema name used to perform all write queries.\n     */\n    schema?: string\n\n    /**\n     * Schema that's used internally by SQL Server for object resolution.\n     *\n     * Because we never set this we have to track it in separately from the `schema` so\n     * we know when we have to specify the full schema or not.\n     *\n     * In most cases this will be `dbo`.\n     */\n    searchSchema?: string\n\n    /**\n     * Indicates if replication is enabled.\n     */\n    isReplicated: boolean = false\n\n    /**\n     * Indicates if tree tables are supported by this driver.\n     */\n    treeSupport = true\n\n    /**\n     * Represent transaction support by this driver\n     */\n    transactionSupport = \"simple\" as const\n\n    /**\n     * Gets list of supported column data types by a driver.\n     *\n     * @see https://docs.microsoft.com/en-us/sql/t-sql/data-types/data-types-transact-sql\n     */\n    supportedDataTypes: ColumnType[] = [\n        \"int\",\n        \"bigint\",\n        \"bit\",\n        \"decimal\",\n        \"money\",\n        \"numeric\",\n        \"smallint\",\n        \"smallmoney\",\n        \"tinyint\",\n        \"float\",\n        \"real\",\n        \"date\",\n        \"datetime2\",\n        \"datetime\",\n        \"datetimeoffset\",\n        \"smalldatetime\",\n        \"time\",\n        \"char\",\n        \"varchar\",\n        \"text\",\n        \"nchar\",\n        \"nvarchar\",\n        \"ntext\",\n        \"binary\",\n        \"image\",\n        \"varbinary\",\n        \"hierarchyid\",\n        \"sql_variant\",\n        \"timestamp\",\n        \"uniqueidentifier\",\n        \"xml\",\n        \"geometry\",\n        \"geography\",\n        \"rowversion\",\n    ]\n\n    /**\n     * Returns type of upsert supported by driver if any\n     */\n    supportedUpsertTypes: UpsertType[] = [\"merge-into\"]\n\n    /**\n     * Gets list of spatial column data types.\n     */\n    spatialTypes: ColumnType[] = [\"geometry\", \"geography\"]\n\n    /**\n     * Gets list of column data types that support length by a driver.\n     */\n    withLengthColumnTypes: ColumnType[] = [\n        \"char\",\n        \"varchar\",\n        \"nchar\",\n        \"nvarchar\",\n        \"binary\",\n        \"varbinary\",\n    ]\n\n    /**\n     * Gets list of column data types that support precision by a driver.\n     */\n    withPrecisionColumnTypes: ColumnType[] = [\n        \"decimal\",\n        \"numeric\",\n        \"time\",\n        \"datetime2\",\n        \"datetimeoffset\",\n    ]\n\n    /**\n     * Gets list of column data types that support scale by a driver.\n     */\n    withScaleColumnTypes: ColumnType[] = [\"decimal\", \"numeric\"]\n\n    /**\n     * Orm has special columns and we need to know what database column types should be for those types.\n     * Column types are driver dependant.\n     */\n    mappedDataTypes: MappedColumnTypes = {\n        createDate: \"datetime2\",\n        createDateDefault: \"getdate()\",\n        updateDate: \"datetime2\",\n        updateDateDefault: \"getdate()\",\n        deleteDate: \"datetime2\",\n        deleteDateNullable: true,\n        version: \"int\",\n        treeLevel: \"int\",\n        migrationId: \"int\",\n        migrationName: \"varchar\",\n        migrationTimestamp: \"bigint\",\n        cacheId: \"int\",\n        cacheIdentifier: \"nvarchar\",\n        cacheTime: \"bigint\",\n        cacheDuration: \"int\",\n        cacheQuery: \"nvarchar(MAX)\" as any,\n        cacheResult: \"nvarchar(MAX)\" as any,\n        metadataType: \"varchar\",\n        metadataDatabase: \"varchar\",\n        metadataSchema: \"varchar\",\n        metadataTable: \"varchar\",\n        metadataName: \"varchar\",\n        metadataValue: \"nvarchar(MAX)\" as any,\n    }\n\n    /**\n     * The prefix used for the parameters\n     */\n    parametersPrefix: string = \"@\"\n\n    /**\n     * Default values of length, precision and scale depends on column data type.\n     * Used in the cases when length/precision/scale is not specified by user.\n     */\n    dataTypeDefaults: DataTypeDefaults = {\n        char: { length: 1 },\n        nchar: { length: 1 },\n        varchar: { length: 255 },\n        nvarchar: { length: 255 },\n        binary: { length: 1 },\n        varbinary: { length: 1 },\n        decimal: { precision: 18, scale: 0 },\n        numeric: { precision: 18, scale: 0 },\n        time: { precision: 7 },\n        datetime2: { precision: 7 },\n        datetimeoffset: { precision: 7 },\n    }\n\n    cteCapabilities: CteCapabilities = {\n        enabled: true,\n        // todo: enable it for SQL Server - it's partially supported, but there are issues with generation of non-standard OUTPUT clause\n        writable: false,\n    }\n\n    /**\n     * Max length allowed by MSSQL Server for aliases (identifiers).\n     * @see https://docs.microsoft.com/en-us/sql/sql-server/maximum-capacity-specifications-for-sql-server\n     */\n    maxAliasLength = 128\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(connection: DataSource) {\n        this.connection = connection\n        this.options = connection.options as SqlServerConnectionOptions\n        this.isReplicated = this.options.replication ? true : false\n\n        // load mssql package\n        this.loadDependencies()\n\n        this.database = DriverUtils.buildDriverOptions(\n            this.options.replication\n                ? this.options.replication.master\n                : this.options,\n        ).database\n        this.schema = DriverUtils.buildDriverOptions(this.options).schema\n\n        // Object.assign(connection.options, DriverUtils.buildDriverOptions(connection.options)); // todo: do it better way\n        // validate options to make sure everything is set\n        // if (!this.options.host)\n        // throw new DriverOptionNotSetError(\"host\");\n        // if (!this.options.username)\n        //     throw new DriverOptionNotSetError(\"username\");\n        // if (!this.options.database)\n        //     throw new DriverOptionNotSetError(\"database\");\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Implemented Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Performs connection to the database.\n     * Based on pooling options, it can either create connection immediately,\n     * either create a pool and create connection when needed.\n     */\n    async connect(): Promise<void> {\n        if (this.options.replication) {\n            this.slaves = await Promise.all(\n                this.options.replication.slaves.map((slave) => {\n                    return this.createPool(this.options, slave)\n                }),\n            )\n            this.master = await this.createPool(\n                this.options,\n                this.options.replication.master,\n            )\n        } else {\n            this.master = await this.createPool(this.options, this.options)\n        }\n\n        if (!this.database || !this.searchSchema) {\n            const queryRunner = this.createQueryRunner(\"master\")\n\n            if (!this.database) {\n                this.database = await queryRunner.getCurrentDatabase()\n            }\n\n            if (!this.searchSchema) {\n                this.searchSchema = await queryRunner.getCurrentSchema()\n            }\n\n            await queryRunner.release()\n        }\n\n        if (!this.schema) {\n            this.schema = this.searchSchema\n        }\n    }\n\n    /**\n     * Makes any action after connection (e.g. create extensions in Postgres driver).\n     */\n    afterConnect(): Promise<void> {\n        return Promise.resolve()\n    }\n\n    /**\n     * Closes connection with the database.\n     */\n    async disconnect(): Promise<void> {\n        if (!this.master) {\n            throw new ConnectionIsNotSetError(\"mssql\")\n        }\n        await this.closePool(this.master)\n        await Promise.all(this.slaves.map((slave) => this.closePool(slave)))\n        this.master = undefined\n        this.slaves = []\n    }\n\n    /**\n     * Closes connection pool.\n     */\n    protected async closePool(pool: any): Promise<void> {\n        return new Promise<void>((ok, fail) => {\n            pool.close((err: any) => (err ? fail(err) : ok()))\n        })\n    }\n\n    /**\n     * Creates a schema builder used to build and sync a schema.\n     */\n    createSchemaBuilder() {\n        return new RdbmsSchemaBuilder(this.connection)\n    }\n\n    /**\n     * Creates a query runner used to execute database queries.\n     */\n    createQueryRunner(mode: ReplicationMode) {\n        return new SqlServerQueryRunner(this, mode)\n    }\n\n    /**\n     * Replaces parameters in the given sql with special escaping character\n     * and an array of parameter names to be passed to a query.\n     */\n    escapeQueryWithParameters(\n        sql: string,\n        parameters: ObjectLiteral,\n        nativeParameters: ObjectLiteral,\n    ): [string, any[]] {\n        const escapedParameters: any[] = Object.keys(nativeParameters).map(\n            (key) => nativeParameters[key],\n        )\n        if (!parameters || !Object.keys(parameters).length)\n            return [sql, escapedParameters]\n\n        const parameterIndexMap = new Map<string, number>()\n        sql = sql.replace(\n            /:(\\.\\.\\.)?([A-Za-z0-9_.]+)/g,\n            (full, isArray: string, key: string): string => {\n                if (!parameters.hasOwnProperty(key)) {\n                    return full\n                }\n\n                if (parameterIndexMap.has(key)) {\n                    return this.parametersPrefix + parameterIndexMap.get(key)\n                }\n\n                const value: any = parameters[key]\n\n                if (isArray) {\n                    return value\n                        .map((v: any) => {\n                            escapedParameters.push(v)\n                            return this.createParameter(\n                                key,\n                                escapedParameters.length - 1,\n                            )\n                        })\n                        .join(\", \")\n                }\n\n                if (typeof value === \"function\") {\n                    return value()\n                }\n\n                escapedParameters.push(value)\n                parameterIndexMap.set(key, escapedParameters.length - 1)\n                return this.createParameter(key, escapedParameters.length - 1)\n            },\n        ) // todo: make replace only in value statements, otherwise problems\n        return [sql, escapedParameters]\n    }\n\n    /**\n     * Escapes a column name.\n     */\n    escape(columnName: string): string {\n        return `\"${columnName}\"`\n    }\n\n    /**\n     * Build full table name with database name, schema name and table name.\n     * E.g. myDB.mySchema.myTable\n     */\n    buildTableName(\n        tableName: string,\n        schema?: string,\n        database?: string,\n    ): string {\n        const tablePath = [tableName]\n\n        if (schema) {\n            tablePath.unshift(schema)\n        }\n\n        if (database) {\n            if (!schema) {\n                tablePath.unshift(\"\")\n            }\n\n            tablePath.unshift(database)\n        }\n\n        return tablePath.join(\".\")\n    }\n\n    /**\n     * Parse a target table name or other types and return a normalized table definition.\n     */\n    parseTableName(\n        target: EntityMetadata | Table | View | TableForeignKey | string,\n    ): { database?: string; schema?: string; tableName: string } {\n        const driverDatabase = this.database\n        const driverSchema = this.schema\n\n        if (InstanceChecker.isTable(target) || InstanceChecker.isView(target)) {\n            const parsed = this.parseTableName(target.name)\n\n            return {\n                database: target.database || parsed.database || driverDatabase,\n                schema: target.schema || parsed.schema || driverSchema,\n                tableName: parsed.tableName,\n            }\n        }\n\n        if (InstanceChecker.isTableForeignKey(target)) {\n            const parsed = this.parseTableName(target.referencedTableName)\n\n            return {\n                database:\n                    target.referencedDatabase ||\n                    parsed.database ||\n                    driverDatabase,\n                schema:\n                    target.referencedSchema || parsed.schema || driverSchema,\n                tableName: parsed.tableName,\n            }\n        }\n\n        if (InstanceChecker.isEntityMetadata(target)) {\n            // EntityMetadata tableName is never a path\n\n            return {\n                database: target.database || driverDatabase,\n                schema: target.schema || driverSchema,\n                tableName: target.tableName,\n            }\n        }\n\n        const parts = target.split(\".\")\n\n        if (parts.length === 3) {\n            return {\n                database: parts[0] || driverDatabase,\n                schema: parts[1] || driverSchema,\n                tableName: parts[2],\n            }\n        } else if (parts.length === 2) {\n            return {\n                database: driverDatabase,\n                schema: parts[0],\n                tableName: parts[1],\n            }\n        } else {\n            return {\n                database: driverDatabase,\n                schema: driverSchema,\n                tableName: target,\n            }\n        }\n    }\n\n    /**\n     * Prepares given value to a value to be persisted, based on its column type and metadata.\n     */\n    preparePersistentValue(value: any, columnMetadata: ColumnMetadata): any {\n        if (columnMetadata.transformer)\n            value = ApplyValueTransformers.transformTo(\n                columnMetadata.transformer,\n                value,\n            )\n\n        if (value === null || value === undefined) return value\n\n        if (columnMetadata.type === Boolean) {\n            return value === true ? 1 : 0\n        } else if (columnMetadata.type === \"date\") {\n            return DateUtils.mixedDateToDate(value)\n        } else if (columnMetadata.type === \"time\") {\n            return DateUtils.mixedTimeToDate(value)\n        } else if (\n            columnMetadata.type === \"datetime\" ||\n            columnMetadata.type === \"smalldatetime\" ||\n            columnMetadata.type === Date\n        ) {\n            return DateUtils.mixedDateToDate(value, false, false)\n        } else if (\n            columnMetadata.type === \"datetime2\" ||\n            columnMetadata.type === \"datetimeoffset\"\n        ) {\n            return DateUtils.mixedDateToDate(value, false, true)\n        } else if (columnMetadata.type === \"simple-array\") {\n            return DateUtils.simpleArrayToString(value)\n        } else if (columnMetadata.type === \"simple-json\") {\n            return DateUtils.simpleJsonToString(value)\n        } else if (columnMetadata.type === \"simple-enum\") {\n            return DateUtils.simpleEnumToString(value)\n        }\n\n        return value\n    }\n\n    /**\n     * Prepares given value to a value to be persisted, based on its column type or metadata.\n     */\n    prepareHydratedValue(value: any, columnMetadata: ColumnMetadata): any {\n        if (value === null || value === undefined)\n            return columnMetadata.transformer\n                ? ApplyValueTransformers.transformFrom(\n                      columnMetadata.transformer,\n                      value,\n                  )\n                : value\n\n        if (columnMetadata.type === Boolean) {\n            value = value ? true : false\n        } else if (\n            columnMetadata.type === \"datetime\" ||\n            columnMetadata.type === Date ||\n            columnMetadata.type === \"datetime2\" ||\n            columnMetadata.type === \"smalldatetime\" ||\n            columnMetadata.type === \"datetimeoffset\"\n        ) {\n            value = DateUtils.normalizeHydratedDate(value)\n        } else if (columnMetadata.type === \"date\") {\n            value = DateUtils.mixedDateToDateString(value)\n        } else if (columnMetadata.type === \"time\") {\n            value = DateUtils.mixedTimeToString(value)\n        } else if (columnMetadata.type === \"simple-array\") {\n            value = DateUtils.stringToSimpleArray(value)\n        } else if (columnMetadata.type === \"simple-json\") {\n            value = DateUtils.stringToSimpleJson(value)\n        } else if (columnMetadata.type === \"simple-enum\") {\n            value = DateUtils.stringToSimpleEnum(value, columnMetadata)\n        } else if (columnMetadata.type === Number) {\n            // convert to number if number\n            value = !isNaN(+value) ? parseInt(value) : value\n        }\n\n        if (columnMetadata.transformer)\n            value = ApplyValueTransformers.transformFrom(\n                columnMetadata.transformer,\n                value,\n            )\n\n        return value\n    }\n\n    /**\n     * Creates a database type from a given column metadata.\n     */\n    normalizeType(column: {\n        type?: ColumnType\n        length?: number | string\n        precision?: number | null\n        scale?: number\n    }): string {\n        if (column.type === Number || column.type === \"integer\") {\n            return \"int\"\n        } else if (column.type === String) {\n            return \"nvarchar\"\n        } else if (column.type === Date) {\n            return \"datetime\"\n        } else if (column.type === Boolean) {\n            return \"bit\"\n        } else if ((column.type as any) === Buffer) {\n            return \"binary\"\n        } else if (column.type === \"uuid\") {\n            return \"uniqueidentifier\"\n        } else if (\n            column.type === \"simple-array\" ||\n            column.type === \"simple-json\"\n        ) {\n            return \"ntext\"\n        } else if (column.type === \"simple-enum\") {\n            return \"nvarchar\"\n        } else if (column.type === \"dec\") {\n            return \"decimal\"\n        } else if (column.type === \"double precision\") {\n            return \"float\"\n        } else if (column.type === \"rowversion\") {\n            return \"timestamp\" // the rowversion type's name in SQL server metadata is timestamp\n        } else {\n            return (column.type as string) || \"\"\n        }\n    }\n\n    /**\n     * Normalizes \"default\" value of the column.\n     */\n    normalizeDefault(columnMetadata: ColumnMetadata): string | undefined {\n        const defaultValue = columnMetadata.default\n\n        if (typeof defaultValue === \"number\") {\n            return `${defaultValue}`\n        }\n\n        if (typeof defaultValue === \"boolean\") {\n            return defaultValue ? \"1\" : \"0\"\n        }\n\n        if (typeof defaultValue === \"function\") {\n            const value = defaultValue()\n            if (value.toUpperCase() === \"CURRENT_TIMESTAMP\") {\n                return \"getdate()\"\n            }\n            return value\n        }\n\n        if (typeof defaultValue === \"string\") {\n            return `'${defaultValue}'`\n        }\n\n        if (defaultValue === undefined || defaultValue === null) {\n            return undefined\n        }\n\n        return `${defaultValue}`\n    }\n\n    /**\n     * Normalizes \"isUnique\" value of the column.\n     */\n    normalizeIsUnique(column: ColumnMetadata): boolean {\n        return column.entityMetadata.uniques.some(\n            (uq) => uq.columns.length === 1 && uq.columns[0] === column,\n        )\n    }\n\n    /**\n     * Returns default column lengths, which is required on column creation.\n     */\n    getColumnLength(column: ColumnMetadata | TableColumn): string {\n        if (column.length) return column.length.toString()\n\n        if (\n            column.type === \"varchar\" ||\n            column.type === \"nvarchar\" ||\n            column.type === String\n        )\n            return \"255\"\n\n        return \"\"\n    }\n\n    /**\n     * Creates column type definition including length, precision and scale\n     */\n    createFullType(column: TableColumn): string {\n        // The Database Engine determines the data type of the computed column by applying the rules\n        // of data type precedence to the expressions specified in the formula.\n        if (column.asExpression) return \"\"\n\n        let type = column.type\n\n        // used 'getColumnLength()' method, because SqlServer sets `varchar` and `nvarchar` length to 1 by default.\n        if (this.getColumnLength(column)) {\n            type += `(${this.getColumnLength(column)})`\n        } else if (\n            column.precision !== null &&\n            column.precision !== undefined &&\n            column.scale !== null &&\n            column.scale !== undefined\n        ) {\n            type += `(${column.precision},${column.scale})`\n        } else if (\n            column.precision !== null &&\n            column.precision !== undefined\n        ) {\n            type += `(${column.precision})`\n        }\n\n        if (column.isArray) type += \" array\"\n\n        return type\n    }\n\n    /**\n     * Obtains a new database connection to a master server.\n     * Used for replication.\n     * If replication is not setup then returns default connection's database connection.\n     */\n    obtainMasterConnection(): Promise<any> {\n        if (!this.master) {\n            return Promise.reject(new TypeORMError(\"Driver not Connected\"))\n        }\n\n        return Promise.resolve(this.master)\n    }\n\n    /**\n     * Obtains a new database connection to a slave server.\n     * Used for replication.\n     * If replication is not setup then returns master (default) connection's database connection.\n     */\n    obtainSlaveConnection(): Promise<any> {\n        if (!this.slaves.length) return this.obtainMasterConnection()\n\n        const random = Math.floor(Math.random() * this.slaves.length)\n        return Promise.resolve(this.slaves[random])\n    }\n\n    /**\n     * Creates generated map of values generated or returned by database after INSERT query.\n     */\n    createGeneratedMap(metadata: EntityMetadata, insertResult: ObjectLiteral) {\n        if (!insertResult) return undefined\n\n        return Object.keys(insertResult).reduce((map, key) => {\n            const column = metadata.findColumnWithDatabaseName(key)\n            if (column) {\n                OrmUtils.mergeDeep(\n                    map,\n                    column.createValueMap(\n                        this.prepareHydratedValue(insertResult[key], column),\n                    ),\n                )\n            }\n            return map\n        }, {} as ObjectLiteral)\n    }\n\n    /**\n     * Differentiate columns of this table and columns from the given column metadatas columns\n     * and returns only changed.\n     */\n    findChangedColumns(\n        tableColumns: TableColumn[],\n        columnMetadatas: ColumnMetadata[],\n    ): ColumnMetadata[] {\n        return columnMetadatas.filter((columnMetadata) => {\n            const tableColumn = tableColumns.find(\n                (c) => c.name === columnMetadata.databaseName,\n            )\n            if (!tableColumn) return false // we don't need new columns, we only need exist and changed\n\n            const isColumnChanged =\n                tableColumn.name !== columnMetadata.databaseName ||\n                this.compareColumnType(tableColumn, columnMetadata) ||\n                this.compareColumnLength(tableColumn, columnMetadata) ||\n                tableColumn.precision !== columnMetadata.precision ||\n                tableColumn.scale !== columnMetadata.scale ||\n                // || tableColumn.comment !== columnMetadata.comment || // todo\n                tableColumn.isGenerated !== columnMetadata.isGenerated ||\n                (!tableColumn.isGenerated &&\n                    this.lowerDefaultValueIfNecessary(\n                        this.normalizeDefault(columnMetadata),\n                    ) !==\n                        this.lowerDefaultValueIfNecessary(\n                            tableColumn.default,\n                        )) || // we included check for generated here, because generated columns already can have default values\n                tableColumn.isPrimary !== columnMetadata.isPrimary ||\n                tableColumn.isNullable !== columnMetadata.isNullable ||\n                tableColumn.asExpression !== columnMetadata.asExpression ||\n                tableColumn.generatedType !== columnMetadata.generatedType ||\n                tableColumn.isUnique !==\n                    this.normalizeIsUnique(columnMetadata) ||\n                (tableColumn.enum &&\n                    columnMetadata.enum &&\n                    !OrmUtils.isArraysEqual(\n                        tableColumn.enum,\n                        columnMetadata.enum.map((val) => val + \"\"),\n                    ))\n\n            // DEBUG SECTION\n            // if (isColumnChanged) {\n            //     console.log(\"table:\", columnMetadata.entityMetadata.tableName)\n            //     console.log(\n            //         \"name:\",\n            //         tableColumn.name,\n            //         columnMetadata.databaseName,\n            //     )\n            //     console.log(\n            //         \"type:\",\n            //         tableColumn.type,\n            //         this.normalizeType(columnMetadata),\n            //         this.compareColumnType(tableColumn, columnMetadata),\n            //     )\n            //     console.log(\n            //         \"length:\",\n            //         tableColumn.length,\n            //         columnMetadata.length,\n            //         this.compareColumnLength(tableColumn, columnMetadata),\n            //     )\n            //     console.log(\n            //         \"precision:\",\n            //         tableColumn.precision,\n            //         columnMetadata.precision,\n            //     )\n            //     console.log(\"scale:\", tableColumn.scale, columnMetadata.scale)\n            //     console.log(\n            //         \"isGenerated:\",\n            //         tableColumn.isGenerated,\n            //         columnMetadata.isGenerated,\n            //     )\n            //     console.log(\n            //         \"isGenerated 2:\",\n            //         !tableColumn.isGenerated &&\n            //             this.lowerDefaultValueIfNecessary(\n            //                 this.normalizeDefault(columnMetadata),\n            //             ) !==\n            //                 this.lowerDefaultValueIfNecessary(\n            //                     tableColumn.default,\n            //                 ),\n            //     )\n            //     console.log(\n            //         \"isPrimary:\",\n            //         tableColumn.isPrimary,\n            //         columnMetadata.isPrimary,\n            //     )\n            //     console.log(\n            //         \"isNullable:\",\n            //         tableColumn.isNullable,\n            //         columnMetadata.isNullable,\n            //     )\n            //     console.log(\n            //         \"asExpression:\",\n            //         tableColumn.asExpression,\n            //         columnMetadata.asExpression,\n            //     )\n            //     console.log(\n            //         \"generatedType:\",\n            //         tableColumn.generatedType,\n            //         columnMetadata.generatedType,\n            //     )\n            //     console.log(\n            //         \"isUnique:\",\n            //         tableColumn.isUnique,\n            //         this.normalizeIsUnique(columnMetadata),\n            //     )\n            //     console.log(\"==========================================\")\n            // }\n\n            return isColumnChanged\n        })\n    }\n\n    /**\n     * Returns true if driver supports RETURNING / OUTPUT statement.\n     */\n    isReturningSqlSupported(): boolean {\n        if (\n            this.options.options &&\n            this.options.options.disableOutputReturning\n        ) {\n            return false\n        }\n        return true\n    }\n\n    /**\n     * Returns true if driver supports uuid values generation on its own.\n     */\n    isUUIDGenerationSupported(): boolean {\n        return true\n    }\n\n    /**\n     * Returns true if driver supports fulltext indices.\n     */\n    isFullTextColumnTypeSupported(): boolean {\n        return false\n    }\n\n    /**\n     * Creates an escaped parameter.\n     */\n    createParameter(parameterName: string, index: number): string {\n        return this.parametersPrefix + index\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Sql server's parameters needs to be wrapped into special object with type information about this value.\n     * This method wraps given value into MssqlParameter based on its column definition.\n     */\n    parametrizeValue(column: ColumnMetadata, value: any) {\n        // if its already MssqlParameter then simply return it\n        if (InstanceChecker.isMssqlParameter(value)) return value\n\n        const normalizedType = this.normalizeType({ type: column.type })\n        if (column.length) {\n            return new MssqlParameter(\n                value,\n                normalizedType as any,\n                column.length as any,\n            )\n        } else if (\n            column.precision !== null &&\n            column.precision !== undefined &&\n            column.scale !== null &&\n            column.scale !== undefined\n        ) {\n            return new MssqlParameter(\n                value,\n                normalizedType as any,\n                column.precision,\n                column.scale,\n            )\n        } else if (\n            column.precision !== null &&\n            column.precision !== undefined\n        ) {\n            return new MssqlParameter(\n                value,\n                normalizedType as any,\n                column.precision,\n            )\n        } else if (column.scale !== null && column.scale !== undefined) {\n            return new MssqlParameter(\n                value,\n                normalizedType as any,\n                column.scale,\n            )\n        }\n\n        return new MssqlParameter(value, normalizedType as any)\n    }\n\n    /**\n     * Recursively wraps values (including those inside FindOperators) into MssqlParameter instances,\n     * ensuring correct type metadata is passed to the SQL Server driver.\n     *\n     * - If the value is a FindOperator containing an array, all elements are individually parametrized.\n     * - If the value is a non-raw FindOperator, a transformation is applied to its internal value.\n     * - Otherwise, the value is passed directly to parametrizeValue for wrapping.\n     *\n     * This ensures SQL Server receives properly typed parameters for queries involving operators like\n     * In, MoreThan, Between, etc.\n     */\n    parametrizeValues(column: ColumnMetadata, value: any) {\n        if (value instanceof FindOperator) {\n            if (value.type !== \"raw\") {\n                value.transformValue({\n                    to: (v) => this.parametrizeValues(column, v),\n                    from: (v) => v,\n                })\n            }\n\n            return value\n        }\n\n        return this.parametrizeValue(column, value)\n    }\n\n    /**\n     * Sql server's parameters needs to be wrapped into special object with type information about this value.\n     * This method wraps all values of the given object into MssqlParameter based on their column definitions in the given table.\n     */\n    parametrizeMap(tablePath: string, map: ObjectLiteral): ObjectLiteral {\n        // find metadata for the given table\n        if (!this.connection.hasMetadata(tablePath))\n            // if no metadata found then we can't proceed because we don't have columns and their types\n            return map\n        const metadata = this.connection.getMetadata(tablePath)\n\n        return Object.keys(map).reduce((newMap, key) => {\n            const value = map[key]\n\n            // find column metadata\n            const column = metadata.findColumnWithDatabaseName(key)\n            if (!column)\n                // if we didn't find a column then we can't proceed because we don't have a column type\n                return value\n\n            newMap[key] = this.parametrizeValue(column, value)\n            return newMap\n        }, {} as ObjectLiteral)\n    }\n\n    buildTableVariableDeclaration(\n        identifier: string,\n        columns: ColumnMetadata[],\n    ): string {\n        const outputColumns = columns.map((column) => {\n            return `${this.escape(column.databaseName)} ${this.createFullType(\n                new TableColumn({\n                    name: column.databaseName,\n                    type: this.normalizeType(column),\n                    length: column.length,\n                    isNullable: column.isNullable,\n                    isArray: column.isArray,\n                }),\n            )}`\n        })\n\n        return `DECLARE ${identifier} TABLE (${outputColumns.join(\", \")})`\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * If driver dependency is not given explicitly, then try to load it via \"require\".\n     */\n    protected loadDependencies(): void {\n        try {\n            const mssql = this.options.driver || PlatformTools.load(\"mssql\")\n            this.mssql = mssql\n        } catch (e) {\n            // todo: better error for browser env\n            throw new DriverPackageNotInstalledError(\"SQL Server\", \"mssql\")\n        }\n    }\n\n    protected compareColumnType(\n        tableColumn: TableColumn,\n        columnMetadata: ColumnMetadata,\n    ): boolean {\n        // The Database Engine determines the data type of the computed column by applying the rules\n        // of data type precedence to the expressions specified in the formula.\n        if (columnMetadata.asExpression) return false\n\n        return tableColumn.type !== this.normalizeType(columnMetadata)\n    }\n\n    protected compareColumnLength(\n        tableColumn: TableColumn,\n        columnMetadata: ColumnMetadata,\n    ): boolean {\n        // The Database Engine determines the data type of the computed column by applying the rules\n        // of data type precedence to the expressions specified in the formula.\n        if (columnMetadata.asExpression) return false\n\n        return (\n            tableColumn.length.toUpperCase() !==\n            this.getColumnLength(columnMetadata).toUpperCase()\n        )\n    }\n\n    protected lowerDefaultValueIfNecessary(value: string | undefined) {\n        // SqlServer saves function calls in default value as lowercase https://github.com/typeorm/typeorm/issues/2733\n        if (!value) {\n            return value\n        }\n        return value\n            .split(`'`)\n            .map((v, i) => {\n                return i % 2 === 1 ? v : v.toLowerCase()\n            })\n            .join(`'`)\n    }\n\n    /**\n     * Creates a new connection pool for a given database credentials.\n     */\n    protected createPool(\n        options: SqlServerConnectionOptions,\n        credentials: SqlServerConnectionCredentialsOptions,\n    ): Promise<any> {\n        credentials = Object.assign(\n            {},\n            credentials,\n            DriverUtils.buildDriverOptions(credentials),\n        ) // todo: do it better way\n\n        // todo: credentials.domain is deprecation. remove it in future\n        const authentication = !credentials.domain\n            ? credentials.authentication\n            : {\n                  type: \"ntlm\",\n                  options: {\n                      domain: credentials.domain,\n                      userName: credentials.username,\n                      password: credentials.password,\n                  },\n              }\n        // build connection options for the driver\n        const connectionOptions = Object.assign(\n            {},\n            {\n                connectionTimeout: this.options.connectionTimeout,\n                requestTimeout: this.options.requestTimeout,\n                stream: this.options.stream,\n                pool: this.options.pool,\n                options: this.options.options,\n            },\n            {\n                server: credentials.host,\n                database: credentials.database,\n                port: credentials.port,\n                user: credentials.username,\n                password: credentials.password,\n                authentication: authentication,\n            },\n            options.extra || {},\n        )\n\n        // set default useUTC option if it hasn't been set\n        if (!connectionOptions.options) {\n            connectionOptions.options = { useUTC: false }\n        } else if (!connectionOptions.options.useUTC) {\n            Object.assign(connectionOptions.options, { useUTC: false })\n        }\n\n        // Match the next release of tedious for configuration options\n        // Also prevents warning messages.\n        Object.assign(connectionOptions.options, { enableArithAbort: true })\n\n        // pooling is enabled either when its set explicitly to true,\n        // either when its not defined at all (e.g. enabled by default)\n        return new Promise<void>((ok, fail) => {\n            const pool = new this.mssql.ConnectionPool(connectionOptions)\n\n            const { logger } = this.connection\n\n            const poolErrorHandler =\n                (options.pool && options.pool.errorHandler) ||\n                ((error: any) =>\n                    logger.log(\"warn\", `MSSQL pool raised an error. ${error}`))\n            /**\n             * Attaching an error handler to pool errors is essential, as, otherwise, errors raised will go unhandled and\n             * cause the hosting app to crash.\n             */\n            pool.on(\"error\", poolErrorHandler)\n\n            const connection = pool.connect((err: any) => {\n                if (err) return fail(err)\n                ok(connection)\n            })\n        })\n    }\n}\n"], "sourceRoot": "../.."}