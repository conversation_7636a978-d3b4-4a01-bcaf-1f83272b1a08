{"version": 3, "sources": ["../../src/driver/sqlite/SqliteQueryRunner.ts"], "names": [], "mappings": ";;;AAAA,iFAA6E;AAC7E,mEAA+D;AAC/D,iGAA6F;AAC7F,gEAA4D;AAC5D,8DAA0D;AAC1D,0EAAsE;AACtE,4FAAwF;AAIxF;;;;;GAKG;AACH,MAAa,iBAAkB,SAAQ,qDAAyB;IAM5D,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAY,MAAoB;QAC5B,KAAK,EAAE,CAAA;QACP,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACpB,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,CAAA;QACnC,IAAI,CAAC,WAAW,GAAG,IAAI,yBAAW,CAAC,IAAI,CAAC,CAAA;IAC5C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe;QACjB,MAAM,IAAI,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAA;IACjD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc;QAChB,MAAM,IAAI,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAA;IAChD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK,CACP,KAAa,EACb,UAAkB,EAClB,mBAAmB,GAAG,KAAK;QAE3B,IAAI,IAAI,CAAC,UAAU;YAAE,MAAM,IAAI,iEAA+B,EAAE,CAAA;QAEhE,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAA;QACzC,MAAM,OAAO,GAAG,UAAU,CAAC,OAAkC,CAAA;QAC7D,MAAM,qBAAqB,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,qBAAqB,CAAA;QACvE,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAA;QAEpC,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC;YAC5B,MAAM,IAAI,iDAAuB,CAAC,QAAQ,CAAC,CAAA;QAC/C,CAAC;QAED,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAA;QAE/C,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;QAC/D,MAAM,WAAW,CAAC,SAAS,CAAC,aAAa,EAAE,KAAK,EAAE,UAAU,CAAC,CAAA;QAE7D,MAAM,iBAAiB,GAAG,IAAI,qCAAiB,EAAE,CAAA;QAEjD,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE;YAClC,IAAI,CAAC;gBACD,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;gBACjC,MAAM,aAAa,GAAG,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,CAAA;gBACjD,MAAM,aAAa,GAAG,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,CAAA;gBACjD,MAAM,aAAa,GAAG,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,CAAA;gBAEjD,MAAM,OAAO,GAAG,KAAK,IAAI,EAAE;oBACvB,IAAI,aAAa,IAAI,aAAa,IAAI,aAAa,EAAE,CAAC;wBAClD,MAAM,kBAAkB,CAAC,GAAG,CAAC,KAAK,EAAE,UAAU,EAAE,OAAO,CAAC,CAAA;oBAC5D,CAAC;yBAAM,CAAC;wBACJ,MAAM,kBAAkB,CAAC,GAAG,CAAC,KAAK,EAAE,UAAU,EAAE,OAAO,CAAC,CAAA;oBAC5D,CAAC;gBACL,CAAC,CAAA;gBAED,MAAM,IAAI,GAAG,IAAI,CAAA;gBACjB,MAAM,OAAO,GAAG,UAAqB,GAAQ,EAAE,IAAS;oBACpD,IAAI,GAAG,IAAI,GAAG,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;wBACvD,IACI,OAAO,OAAO,CAAC,cAAc,KAAK,QAAQ;4BAC1C,OAAO,CAAC,cAAc,GAAG,CAAC,EAC5B,CAAC;4BACC,UAAU,CAAC,OAAO,EAAE,OAAO,CAAC,cAAc,CAAC,CAAA;4BAC3C,OAAM;wBACV,CAAC;oBACL,CAAC;oBAED,oDAAoD;oBACpD,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;oBAC/B,MAAM,kBAAkB,GAAG,YAAY,GAAG,cAAc,CAAA;oBACxD,IACI,qBAAqB;wBACrB,kBAAkB,GAAG,qBAAqB;wBAE1C,UAAU,CAAC,MAAM,CAAC,YAAY,CAC1B,kBAAkB,EAClB,KAAK,EACL,UAAU,EACV,IAAI,CACP,CAAA;oBAEL,IAAI,GAAG,EAAE,CAAC;wBACN,UAAU,CAAC,MAAM,CAAC,aAAa,CAC3B,GAAG,EACH,KAAK,EACL,UAAU,EACV,IAAI,CACP,CAAA;wBACD,WAAW,CAAC,wBAAwB,CAChC,iBAAiB,EACjB,KAAK,EACL,UAAU,EACV,KAAK,EACL,SAAS,EACT,SAAS,EACT,GAAG,CACN,CAAA;wBAED,OAAO,IAAI,CACP,IAAI,mCAAgB,CAAC,KAAK,EAAE,UAAU,EAAE,GAAG,CAAC,CAC/C,CAAA;oBACL,CAAC;yBAAM,CAAC;wBACJ,MAAM,MAAM,GAAG,IAAI,yBAAW,EAAE,CAAA;wBAEhC,IAAI,aAAa,EAAE,CAAC;4BAChB,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAA;wBAC/B,CAAC;6BAAM,CAAC;4BACJ,MAAM,CAAC,GAAG,GAAG,IAAI,CAAA;wBACrB,CAAC;wBAED,WAAW,CAAC,wBAAwB,CAChC,iBAAiB,EACjB,KAAK,EACL,UAAU,EACV,IAAI,EACJ,kBAAkB,EAClB,MAAM,CAAC,GAAG,EACV,SAAS,CACZ,CAAA;wBAED,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;4BACtB,MAAM,CAAC,OAAO,GAAG,IAAI,CAAA;wBACzB,CAAC;wBAED,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,CAAA;wBAEjC,IAAI,mBAAmB,EAAE,CAAC;4BACtB,EAAE,CAAC,MAAM,CAAC,CAAA;wBACd,CAAC;6BAAM,CAAC;4BACJ,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;wBAClB,CAAC;oBACL,CAAC;gBACL,CAAC,CAAA;gBAED,MAAM,OAAO,EAAE,CAAA;YACnB,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACX,IAAI,CAAC,GAAG,CAAC,CAAA;YACb,CAAC;oBAAS,CAAC;gBACP,MAAM,iBAAiB,CAAC,IAAI,EAAE,CAAA;YAClC,CAAC;QACL,CAAC,CAAC,CAAA;IACN,CAAC;CACJ;AA/JD,8CA+JC", "file": "SqliteQueryRunner.js", "sourcesContent": ["import { ConnectionIsNotSetError } from \"../../error/ConnectionIsNotSetError\"\nimport { QueryFailedError } from \"../../error/QueryFailedError\"\nimport { QueryRunnerAlreadyReleasedError } from \"../../error/QueryRunnerAlreadyReleasedError\"\nimport { QueryResult } from \"../../query-runner/QueryResult\"\nimport { Broadcaster } from \"../../subscriber/Broadcaster\"\nimport { BroadcasterResult } from \"../../subscriber/BroadcasterResult\"\nimport { AbstractSqliteQueryRunner } from \"../sqlite-abstract/AbstractSqliteQueryRunner\"\nimport { SqliteConnectionOptions } from \"./SqliteConnectionOptions\"\nimport { SqliteDriver } from \"./SqliteDriver\"\n\n/**\n * Runs queries on a single sqlite database connection.\n *\n * Does not support compose primary keys with autoincrement field.\n * todo: need to throw exception for this case.\n */\nexport class SqliteQueryRunner extends AbstractSqliteQueryRunner {\n    /**\n     * Database driver used by connection.\n     */\n    driver: SqliteDriver\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(driver: SqliteDriver) {\n        super()\n        this.driver = driver\n        this.connection = driver.connection\n        this.broadcaster = new Broadcaster(this)\n    }\n\n    /**\n     * Called before migrations are run.\n     */\n    async beforeMigration(): Promise<void> {\n        await this.query(`PRAGMA foreign_keys = OFF`)\n    }\n\n    /**\n     * Called after migrations are run.\n     */\n    async afterMigration(): Promise<void> {\n        await this.query(`PRAGMA foreign_keys = ON`)\n    }\n\n    /**\n     * Executes a given SQL query.\n     */\n    async query(\n        query: string,\n        parameters?: any[],\n        useStructuredResult = false,\n    ): Promise<any> {\n        if (this.isReleased) throw new QueryRunnerAlreadyReleasedError()\n\n        const connection = this.driver.connection\n        const options = connection.options as SqliteConnectionOptions\n        const maxQueryExecutionTime = this.driver.options.maxQueryExecutionTime\n        const broadcaster = this.broadcaster\n\n        if (!connection.isInitialized) {\n            throw new ConnectionIsNotSetError(\"sqlite\")\n        }\n\n        const databaseConnection = await this.connect()\n\n        this.driver.connection.logger.logQuery(query, parameters, this)\n        await broadcaster.broadcast(\"BeforeQuery\", query, parameters)\n\n        const broadcasterResult = new BroadcasterResult()\n\n        return new Promise(async (ok, fail) => {\n            try {\n                const queryStartTime = Date.now()\n                const isInsertQuery = query.startsWith(\"INSERT \")\n                const isDeleteQuery = query.startsWith(\"DELETE \")\n                const isUpdateQuery = query.startsWith(\"UPDATE \")\n\n                const execute = async () => {\n                    if (isInsertQuery || isDeleteQuery || isUpdateQuery) {\n                        await databaseConnection.run(query, parameters, handler)\n                    } else {\n                        await databaseConnection.all(query, parameters, handler)\n                    }\n                }\n\n                const self = this\n                const handler = function (this: any, err: any, rows: any) {\n                    if (err && err.toString().indexOf(\"SQLITE_BUSY:\") !== -1) {\n                        if (\n                            typeof options.busyErrorRetry === \"number\" &&\n                            options.busyErrorRetry > 0\n                        ) {\n                            setTimeout(execute, options.busyErrorRetry)\n                            return\n                        }\n                    }\n\n                    // log slow queries if maxQueryExecution time is set\n                    const queryEndTime = Date.now()\n                    const queryExecutionTime = queryEndTime - queryStartTime\n                    if (\n                        maxQueryExecutionTime &&\n                        queryExecutionTime > maxQueryExecutionTime\n                    )\n                        connection.logger.logQuerySlow(\n                            queryExecutionTime,\n                            query,\n                            parameters,\n                            self,\n                        )\n\n                    if (err) {\n                        connection.logger.logQueryError(\n                            err,\n                            query,\n                            parameters,\n                            self,\n                        )\n                        broadcaster.broadcastAfterQueryEvent(\n                            broadcasterResult,\n                            query,\n                            parameters,\n                            false,\n                            undefined,\n                            undefined,\n                            err,\n                        )\n\n                        return fail(\n                            new QueryFailedError(query, parameters, err),\n                        )\n                    } else {\n                        const result = new QueryResult()\n\n                        if (isInsertQuery) {\n                            result.raw = this[\"lastID\"]\n                        } else {\n                            result.raw = rows\n                        }\n\n                        broadcaster.broadcastAfterQueryEvent(\n                            broadcasterResult,\n                            query,\n                            parameters,\n                            true,\n                            queryExecutionTime,\n                            result.raw,\n                            undefined,\n                        )\n\n                        if (Array.isArray(rows)) {\n                            result.records = rows\n                        }\n\n                        result.affected = this[\"changes\"]\n\n                        if (useStructuredResult) {\n                            ok(result)\n                        } else {\n                            ok(result.raw)\n                        }\n                    }\n                }\n\n                await execute()\n            } catch (err) {\n                fail(err)\n            } finally {\n                await broadcasterResult.wait()\n            }\n        })\n    }\n}\n"], "sourceRoot": "../.."}