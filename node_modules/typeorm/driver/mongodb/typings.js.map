{"version": 3, "sources": ["../../src/driver/mongodb/typings.ts"], "names": [], "mappings": ";;;AAAA,iDAsBuB;AA0xBd,qFA/yBL,mBAAI,OA+yBK;AAEJ,2FAhzBL,yBAAU,OAgzBK;AA0CV,2FAz1BL,yBAAU,OAy1BK;AAEV,yFA11BL,uBAAQ,OA01BK;AAxDR,uFAjyBL,qBAAM,OAiyBK;AA85BN,qFA9rDL,mBAAI,OA8rDK;AA0zCJ,sFAv/FL,oBAAK,OAu/FK;AAQL,2FA9/FL,yBAAU,OA8/FK;AAiEV,uFA5jGL,qBAAM,OA4jGK;AAu4BN,sFAl8HL,oBAAK,OAk8HK;AA+PL,qFAhsIL,mBAAI,OAgsIK;AAMJ,uFArsIL,qBAAM,OAqsIK;AAON,uFA3sIL,qBAAM,OA2sIK;AAqiCN,yFA/uKL,uBAAQ,OA+uKK;AAk7BR,0FA9pML,wBAAS,OA8pMK;AAnnGT,4FA1iGL,0BAAW,OA0iGK;AAsrFX,0FA/tLL,wBAAS,OA+tLK", "file": "typings.js", "sourcesContent": ["import {\n    <PERSON><PERSON>,\n    <PERSON><PERSON><PERSON>egExp,\n    BSONSymbol,\n    BSONType,\n    Binary,\n    Code,\n    DBRef,\n    Decimal128,\n    DeserializeOptions,\n    Document,\n    Double,\n    Int32,\n    <PERSON>,\n    <PERSON><PERSON><PERSON>,\n    <PERSON><PERSON><PERSON>,\n    ObjectId,\n    ObjectIdLike,\n    SerializeOptions,\n    Timestamp,\n    deserialize,\n    serialize,\n} from \"./bson.typings\"\nimport type { ConnectionOptions as ConnectionOptions_2 } from \"tls\"\nimport type { Socket } from \"net\"\nimport type { SrvRecord } from \"dns\"\nimport type { TcpNetConnectOpts } from \"net\"\nimport type { TLSSocket } from \"tls\"\nimport type { TLSSocketOptions } from \"tls\"\nimport { Readable, EventEmitter } from \"../../platform/PlatformTools\"\n\n/** @public */\nexport declare abstract class AbstractCursor<\n    TSchema = any,\n    CursorEvents extends AbstractCursorEvents = AbstractCursorEvents,\n> extends TypedEventEmitter<CursorEvents> {\n    /* Excluded from this release type: [kId] */\n    /* Excluded from this release type: [kSession] */\n    /* Excluded from this release type: [kServer] */\n    /* Excluded from this release type: [kNamespace] */\n    /* Excluded from this release type: [kDocuments] */\n    /* Excluded from this release type: [kClient] */\n    /* Excluded from this release type: [kTransform] */\n    /* Excluded from this release type: [kInitialized] */\n    /* Excluded from this release type: [kClosed] */\n    /* Excluded from this release type: [kKilled] */\n    /* Excluded from this release type: [kOptions] */\n    /** @event */\n    static readonly CLOSE: \"close\"\n    /* Excluded from this release type: __constructor */\n    get id(): Long | undefined\n    /* Excluded from this release type: client */\n    /* Excluded from this release type: server */\n    get namespace(): MongoDBNamespace\n    get readPreference(): ReadPreference\n    get readConcern(): ReadConcern | undefined\n    /* Excluded from this release type: session */\n    /* Excluded from this release type: session */\n    /* Excluded from this release type: cursorOptions */\n    get closed(): boolean\n    get killed(): boolean\n    get loadBalanced(): boolean\n    /** Returns current buffered documents length */\n    bufferedCount(): number\n    /** Returns current buffered documents */\n    readBufferedDocuments(number?: number): TSchema[]\n    [Symbol.asyncIterator](): AsyncGenerator<TSchema, void, void>\n    stream(options?: CursorStreamOptions): Readable & AsyncIterable<TSchema>\n    hasNext(): Promise<boolean>\n    /** Get the next available document from the cursor, returns null if no more documents are available. */\n    next(): Promise<TSchema | null>\n    /**\n     * Try to get the next available document from the cursor or `null` if an empty batch is returned\n     */\n    tryNext(): Promise<TSchema | null>\n    /**\n     * Iterates over all the documents for this cursor using the iterator, callback pattern.\n     *\n     * If the iterator returns `false`, iteration will stop.\n     *\n     * @param iterator - The iteration callback.\n     */\n    forEach(iterator: (doc: TSchema) => boolean | void): Promise<void>\n    close(): Promise<void>\n    /**\n     * Returns an array of documents. The caller is responsible for making sure that there\n     * is enough memory to store the results. Note that the array only contains partial\n     * results when this cursor had been previously accessed. In that case,\n     * cursor.rewind() can be used to reset the cursor.\n     */\n    toArray(): Promise<TSchema[]>\n    /**\n     * Add a cursor flag to the cursor\n     *\n     * @param flag - The flag to set, must be one of following ['tailable', 'oplogReplay', 'noCursorTimeout', 'awaitData', 'partial' -.\n     * @param value - The flag boolean value.\n     */\n    addCursorFlag(flag: CursorFlag, value: boolean): this\n    /**\n     * Map all documents using the provided function\n     * If there is a transform set on the cursor, that will be called first and the result passed to\n     * this function's transform.\n     *\n     * @remarks\n     *\n     * **Note** Cursors use `null` internally to indicate that there are no more documents in the cursor. Providing a mapping\n     * function that maps values to `null` will result in the cursor closing itself before it has finished iterating\n     * all documents.  This will **not** result in a memory leak, just surprising behavior.  For example:\n     *\n     * ```typescript\n     * const cursor = collection.find({});\n     * cursor.map(() => null);\n     *\n     * const documents = await cursor.toArray();\n     * // documents is always [], regardless of how many documents are in the collection.\n     * ```\n     *\n     * Other falsey values are allowed:\n     *\n     * ```typescript\n     * const cursor = collection.find({});\n     * cursor.map(() => '');\n     *\n     * const documents = await cursor.toArray();\n     * // documents is now an array of empty strings\n     * ```\n     *\n     * **Note for Typescript Users:** adding a transform changes the return type of the iteration of this cursor,\n     * it **does not** return a new instance of a cursor. This means when calling map,\n     * you should always assign the result to a new variable in order to get a correctly typed cursor variable.\n     * Take note of the following example:\n     *\n     * @example\n     * ```typescript\n     * const cursor: FindCursor<Document> = coll.find();\n     * const mappedCursor: FindCursor<number> = cursor.map(doc => Object.keys(doc).length);\n     * const keyCounts: number[] = await mappedCursor.toArray(); // cursor.toArray() still returns Document[]\n     * ```\n     * @param transform - The mapping transformation method.\n     */\n    map<T = any>(transform: (doc: TSchema) => T): AbstractCursor<T>\n    /**\n     * Set the ReadPreference for the cursor.\n     *\n     * @param readPreference - The new read preference for the cursor.\n     */\n    withReadPreference(readPreference: ReadPreferenceLike): this\n    /**\n     * Set the ReadPreference for the cursor.\n     *\n     * @param readPreference - The new read preference for the cursor.\n     */\n    withReadConcern(readConcern: ReadConcernLike): this\n    /**\n     * Set a maxTimeMS on the cursor query, allowing for hard timeout limits on queries (Only supported on MongoDB 2.6 or higher)\n     *\n     * @param value - Number of milliseconds to wait before aborting the query.\n     */\n    maxTimeMS(value: number): this\n    /**\n     * Set the batch size for the cursor.\n     *\n     * @param value - The number of documents to return per batch. See {@link https://www.mongodb.com/docs/manual/reference/command/find/|find command documentation}.\n     */\n    batchSize(value: number): this\n    /**\n     * Rewind this cursor to its uninitialized state. Any options that are present on the cursor will\n     * remain in effect. Iterating this cursor will cause new queries to be sent to the server, even\n     * if the resultant data has already been retrieved by this cursor.\n     */\n    rewind(): void\n    /**\n     * Returns a new uninitialized copy of this cursor, with options matching those that have been set on the current instance\n     */\n    abstract clone(): AbstractCursor<TSchema>\n    /* Excluded from this release type: _initialize */\n    /* Excluded from this release type: _getMore */\n    /* Excluded from this release type: [kInit] */\n}\n\n/** @public */\nexport declare type AbstractCursorEvents = {\n    [AbstractCursor.CLOSE](): void\n}\n\n/** @public */\nexport declare interface AbstractCursorOptions extends BSONSerializeOptions {\n    session?: ClientSession\n    readPreference?: ReadPreferenceLike\n    readConcern?: ReadConcernLike\n    /**\n     * Specifies the number of documents to return in each response from MongoDB\n     */\n    batchSize?: number\n    /**\n     * When applicable `maxTimeMS` controls the amount of time the initial command\n     * that constructs a cursor should take. (ex. find, aggregate, listCollections)\n     */\n    maxTimeMS?: number\n    /**\n     * When applicable `maxAwaitTimeMS` controls the amount of time subsequent getMores\n     * that a cursor uses to fetch more data should take. (ex. cursor.next())\n     */\n    maxAwaitTimeMS?: number\n    /**\n     * Comment to apply to the operation.\n     *\n     * In server versions pre-4.4, 'comment' must be string.  A server\n     * error will be thrown if any other type is provided.\n     *\n     * In server versions 4.4 and above, 'comment' can be any valid BSON type.\n     */\n    comment?: unknown\n    /**\n     * By default, MongoDB will automatically close a cursor when the\n     * client has exhausted all results in the cursor. However, for [capped collections](https://www.mongodb.com/docs/manual/core/capped-collections)\n     * you may use a Tailable Cursor that remains open after the client exhausts\n     * the results in the initial cursor.\n     */\n    tailable?: boolean\n    /**\n     * If awaitData is set to true, when the cursor reaches the end of the capped collection,\n     * MongoDB blocks the query thread for a period of time waiting for new data to arrive.\n     * When new data is inserted into the capped collection, the blocked thread is signaled\n     * to wake up and return the next batch to the client.\n     */\n    awaitData?: boolean\n    noCursorTimeout?: boolean\n}\n\n/* Excluded from this release type: AbstractOperation */\n\n/** @public */\nexport declare type AcceptedFields<TSchema, FieldType, AssignableType> = {\n    readonly [key in KeysOfAType<TSchema, FieldType>]?: AssignableType\n}\n\n/** @public */\nexport declare type AddToSetOperators<Type> = {\n    $each?: Array<Flatten<Type>>\n}\n\n/** @public */\nexport declare interface AddUserOptions extends CommandOperationOptions {\n    /** Roles associated with the created user */\n    roles?: string | string[] | RoleSpecification | RoleSpecification[]\n    /** Custom data associated with the user (only Mongodb 2.6 or higher) */\n    customData?: Document\n}\n\n/**\n * The **Admin** class is an internal class that allows convenient access to\n * the admin functionality and commands for MongoDB.\n *\n * **ADMIN Cannot directly be instantiated**\n * @public\n *\n * @example\n * ```ts\n * import { MongoClient } from 'mongodb';\n *\n * const client = new MongoClient('mongodb://localhost:27017');\n * const admin = client.db().admin();\n * const dbInfo = await admin.listDatabases();\n * for (const db of dbInfo.databases) {\n *   console.log(db.name);\n * }\n * ```\n */\nexport declare class Admin {\n    /* Excluded from this release type: s */\n    /* Excluded from this release type: __constructor */\n    /**\n     * Execute a command\n     *\n     * @param command - The command to execute\n     * @param options - Optional settings for the command\n     */\n    command(command: Document, options?: RunCommandOptions): Promise<Document>\n    /**\n     * Retrieve the server build information\n     *\n     * @param options - Optional settings for the command\n     */\n    buildInfo(options?: CommandOperationOptions): Promise<Document>\n    /**\n     * Retrieve the server build information\n     *\n     * @param options - Optional settings for the command\n     */\n    serverInfo(options?: CommandOperationOptions): Promise<Document>\n    /**\n     * Retrieve this db's server status.\n     *\n     * @param options - Optional settings for the command\n     */\n    serverStatus(options?: CommandOperationOptions): Promise<Document>\n    /**\n     * Ping the MongoDB server and retrieve results\n     *\n     * @param options - Optional settings for the command\n     */\n    ping(options?: CommandOperationOptions): Promise<Document>\n    /**\n     * Add a user to the database\n     *\n     * @param username - The username for the new user\n     * @param passwordOrOptions - An optional password for the new user, or the options for the command\n     * @param options - Optional settings for the command\n     */\n    addUser(\n        username: string,\n        passwordOrOptions?: string | AddUserOptions,\n        options?: AddUserOptions,\n    ): Promise<Document>\n    /**\n     * Remove a user from a database\n     *\n     * @param username - The username to remove\n     * @param options - Optional settings for the command\n     */\n    removeUser(username: string, options?: RemoveUserOptions): Promise<boolean>\n    /**\n     * Validate an existing collection\n     *\n     * @param collectionName - The name of the collection to validate.\n     * @param options - Optional settings for the command\n     */\n    validateCollection(\n        collectionName: string,\n        options?: ValidateCollectionOptions,\n    ): Promise<Document>\n    /**\n     * List the available databases\n     *\n     * @param options - Optional settings for the command\n     */\n    listDatabases(options?: ListDatabasesOptions): Promise<ListDatabasesResult>\n    /**\n     * Get ReplicaSet status\n     *\n     * @param options - Optional settings for the command\n     */\n    replSetGetStatus(options?: CommandOperationOptions): Promise<Document>\n}\n\n/* Excluded from this release type: AdminPrivate */\n\n/* Excluded from this release type: AggregateOperation */\n\n/** @public */\nexport declare interface AggregateOptions extends CommandOperationOptions {\n    /** allowDiskUse lets the server know if it can use disk to store temporary results for the aggregation (requires mongodb 2.6 \\>). */\n    allowDiskUse?: boolean\n    /** The number of documents to return per batch. See [aggregation documentation](https://www.mongodb.com/docs/manual/reference/command/aggregate). */\n    batchSize?: number\n    /** Allow driver to bypass schema validation in MongoDB 3.2 or higher. */\n    bypassDocumentValidation?: boolean\n    /** Return the query as cursor, on 2.6 \\> it returns as a real cursor on pre 2.6 it returns as an emulated cursor. */\n    cursor?: Document\n    /** specifies a cumulative time limit in milliseconds for processing operations on the cursor. MongoDB interrupts the operation at the earliest following interrupt point. */\n    maxTimeMS?: number\n    /** The maximum amount of time for the server to wait on new documents to satisfy a tailable cursor query. */\n    maxAwaitTimeMS?: number\n    /** Specify collation. */\n    collation?: CollationOptions\n    /** Add an index selection hint to an aggregation command */\n    hint?: Hint\n    /** Map of parameter names and values that can be accessed using $$var (requires MongoDB 5.0). */\n    let?: Document\n    out?: string\n}\n\n/**\n * The **AggregationCursor** class is an internal class that embodies an aggregation cursor on MongoDB\n * allowing for iteration over the results returned from the underlying query. It supports\n * one by one document iteration, conversion to an array or can be iterated as a Node 4.X\n * or higher stream\n * @public\n */\nexport declare class AggregationCursor<\n    TSchema = any,\n> extends AbstractCursor<TSchema> {\n    /* Excluded from this release type: [kPipeline] */\n    /* Excluded from this release type: [kOptions] */\n    /* Excluded from this release type: __constructor */\n    get pipeline(): Document[]\n    clone(): AggregationCursor<TSchema>\n    map<T>(transform: (doc: TSchema) => T): AggregationCursor<T>\n    /* Excluded from this release type: _initialize */\n    /** Execute the explain for the cursor */\n    explain(verbosity?: ExplainVerbosityLike): Promise<Document>\n    /** Add a group stage to the aggregation pipeline */\n    group<T = TSchema>($group: Document): AggregationCursor<T>\n    /** Add a limit stage to the aggregation pipeline */\n    limit($limit: number): this\n    /** Add a match stage to the aggregation pipeline */\n    match($match: Document): this\n    /** Add an out stage to the aggregation pipeline */\n    out(\n        $out:\n            | {\n                  db: string\n                  coll: string\n              }\n            | string,\n    ): this\n    /**\n     * Add a project stage to the aggregation pipeline\n     *\n     * @remarks\n     * In order to strictly type this function you must provide an interface\n     * that represents the effect of your projection on the result documents.\n     *\n     * By default chaining a projection to your cursor changes the returned type to the generic {@link Document} type.\n     * You should specify a parameterized type to have assertions on your final results.\n     *\n     * @example\n     * ```typescript\n     * // Best way\n     * const docs: AggregationCursor<{ a: number }> = cursor.project<{ a: number }>({ _id: 0, a: true });\n     * // Flexible way\n     * const docs: AggregationCursor<Document> = cursor.project({ _id: 0, a: true });\n     * ```\n     *\n     * @remarks\n     * In order to strictly type this function you must provide an interface\n     * that represents the effect of your projection on the result documents.\n     *\n     * **Note for Typescript Users:** adding a transform changes the return type of the iteration of this cursor,\n     * it **does not** return a new instance of a cursor. This means when calling project,\n     * you should always assign the result to a new variable in order to get a correctly typed cursor variable.\n     * Take note of the following example:\n     *\n     * @example\n     * ```typescript\n     * const cursor: AggregationCursor<{ a: number; b: string }> = coll.aggregate([]);\n     * const projectCursor = cursor.project<{ a: number }>({ _id: 0, a: true });\n     * const aPropOnlyArray: {a: number}[] = await projectCursor.toArray();\n     *\n     * // or always use chaining and save the final cursor\n     *\n     * const cursor = coll.aggregate().project<{ a: string }>({\n     *   _id: 0,\n     *   a: { $convert: { input: '$a', to: 'string' }\n     * }});\n     * ```\n     */\n    project<T extends Document = Document>(\n        $project: Document,\n    ): AggregationCursor<T>\n    /** Add a lookup stage to the aggregation pipeline */\n    lookup($lookup: Document): this\n    /** Add a redact stage to the aggregation pipeline */\n    redact($redact: Document): this\n    /** Add a skip stage to the aggregation pipeline */\n    skip($skip: number): this\n    /** Add a sort stage to the aggregation pipeline */\n    sort($sort: Sort): this\n    /** Add a unwind stage to the aggregation pipeline */\n    unwind($unwind: Document | string): this\n    /** Add a geoNear stage to the aggregation pipeline */\n    geoNear($geoNear: Document): this\n}\n\n/** @public */\nexport declare interface AggregationCursorOptions\n    extends AbstractCursorOptions,\n        AggregateOptions {}\n\n/**\n * It is possible to search using alternative types in mongodb e.g.\n * string types can be searched using a regex in mongo\n * array types can be searched using their element type\n * @public\n */\nexport declare type AlternativeType<T> = T extends ReadonlyArray<infer U>\n    ? T | RegExpOrString<U>\n    : RegExpOrString<T>\n\n/** @public */\nexport declare type AnyBulkWriteOperation<TSchema extends Document = Document> =\n\n        | {\n              insertOne: InsertOneModel<TSchema>\n          }\n        | {\n              replaceOne: ReplaceOneModel<TSchema>\n          }\n        | {\n              updateOne: UpdateOneModel<TSchema>\n          }\n        | {\n              updateMany: UpdateManyModel<TSchema>\n          }\n        | {\n              deleteOne: DeleteOneModel<TSchema>\n          }\n        | {\n              deleteMany: DeleteManyModel<TSchema>\n          }\n\n/** @public */\nexport declare type AnyError = MongoError | Error\n\n/** @public */\nexport declare type ArrayElement<Type> = Type extends ReadonlyArray<infer Item>\n    ? Item\n    : never\n\n/** @public */\nexport declare type ArrayOperator<Type> = {\n    $each?: Array<Flatten<Type>>\n    $slice?: number\n    $position?: number\n    $sort?: Sort\n}\n\n/** @public */\nexport declare interface Auth {\n    /** The username for auth */\n    username?: string\n    /** The password for auth */\n    password?: string\n}\n\n/* Excluded from this release type: AuthContext */\n\n/** @public */\nexport declare const AuthMechanism: Readonly<{\n    readonly MONGODB_AWS: \"MONGODB-AWS\"\n    readonly MONGODB_CR: \"MONGODB-CR\"\n    readonly MONGODB_DEFAULT: \"DEFAULT\"\n    readonly MONGODB_GSSAPI: \"GSSAPI\"\n    readonly MONGODB_PLAIN: \"PLAIN\"\n    readonly MONGODB_SCRAM_SHA1: \"SCRAM-SHA-1\"\n    readonly MONGODB_SCRAM_SHA256: \"SCRAM-SHA-256\"\n    readonly MONGODB_X509: \"MONGODB-X509\"\n    /** @experimental */\n    readonly MONGODB_OIDC: \"MONGODB-OIDC\"\n}>\n\n/** @public */\nexport declare type AuthMechanism =\n    (typeof AuthMechanism)[keyof typeof AuthMechanism]\n\n/** @public */\nexport declare interface AuthMechanismProperties extends Document {\n    SERVICE_HOST?: string\n    SERVICE_NAME?: string\n    SERVICE_REALM?: string\n    CANONICALIZE_HOST_NAME?: GSSAPICanonicalizationValue\n    AWS_SESSION_TOKEN?: string\n    /** @experimental */\n    REQUEST_TOKEN_CALLBACK?: OIDCRequestFunction\n    /** @experimental */\n    REFRESH_TOKEN_CALLBACK?: OIDCRefreshFunction\n    /** @experimental */\n    PROVIDER_NAME?: \"aws\"\n}\n\n/** @public */\nexport declare interface AutoEncrypter {\n    new (client: MongoClient, options: AutoEncryptionOptions): AutoEncrypter\n    init(cb: Callback): void\n    teardown(force: boolean, callback: Callback): void\n    encrypt(\n        ns: string,\n        cmd: Document,\n        options: any,\n        callback: Callback<Document>,\n    ): void\n    decrypt(cmd: Document, options: any, callback: Callback<Document>): void\n    /** @experimental */\n    readonly cryptSharedLibVersionInfo: {\n        version: bigint\n        versionStr: string\n    } | null\n}\n\n/** @public */\nexport declare const AutoEncryptionLoggerLevel: Readonly<{\n    readonly FatalError: 0\n    readonly Error: 1\n    readonly Warning: 2\n    readonly Info: 3\n    readonly Trace: 4\n}>\n\n/** @public */\nexport declare type AutoEncryptionLoggerLevel =\n    (typeof AutoEncryptionLoggerLevel)[keyof typeof AutoEncryptionLoggerLevel]\n\n/** @public */\nexport declare interface AutoEncryptionOptions {\n    /* Excluded from this release type: metadataClient */\n    /** A `MongoClient` used to fetch keys from a key vault */\n    keyVaultClient?: MongoClient\n    /** The namespace where keys are stored in the key vault */\n    keyVaultNamespace?: string\n    /** Configuration options that are used by specific KMS providers during key generation, encryption, and decryption. */\n    kmsProviders?: {\n        /** Configuration options for using 'aws' as your KMS provider */\n        aws?:\n            | {\n                  /** The access key used for the AWS KMS provider */\n                  accessKeyId: string\n                  /** The secret access key used for the AWS KMS provider */\n                  secretAccessKey: string\n                  /**\n                   * An optional AWS session token that will be used as the\n                   * X-Amz-Security-Token header for AWS requests.\n                   */\n                  sessionToken?: string\n              }\n            | Record<string, never>\n        /** Configuration options for using 'local' as your KMS provider */\n        local?: {\n            /**\n             * The master key used to encrypt/decrypt data keys.\n             * A 96-byte long Buffer or base64 encoded string.\n             */\n            key: Buffer | string\n        }\n        /** Configuration options for using 'azure' as your KMS provider */\n        azure?:\n            | {\n                  /** The tenant ID identifies the organization for the account */\n                  tenantId: string\n                  /** The client ID to authenticate a registered application */\n                  clientId: string\n                  /** The client secret to authenticate a registered application */\n                  clientSecret: string\n                  /**\n                   * If present, a host with optional port. E.g. \"example.com\" or \"example.com:443\".\n                   * This is optional, and only needed if customer is using a non-commercial Azure instance\n                   * (e.g. a government or China account, which use different URLs).\n                   * Defaults to \"login.microsoftonline.com\"\n                   */\n                  identityPlatformEndpoint?: string | undefined\n              }\n            | {\n                  /**\n                   * If present, an access token to authenticate with Azure.\n                   */\n                  accessToken: string\n              }\n            | Record<string, never>\n        /** Configuration options for using 'gcp' as your KMS provider */\n        gcp?:\n            | {\n                  /** The service account email to authenticate */\n                  email: string\n                  /** A PKCS#8 encrypted key. This can either be a base64 string or a binary representation */\n                  privateKey: string | Buffer\n                  /**\n                   * If present, a host with optional port. E.g. \"example.com\" or \"example.com:443\".\n                   * Defaults to \"oauth2.googleapis.com\"\n                   */\n                  endpoint?: string | undefined\n              }\n            | {\n                  /**\n                   * If present, an access token to authenticate with GCP.\n                   */\n                  accessToken: string\n              }\n            | Record<string, never>\n        /**\n         * Configuration options for using 'kmip' as your KMS provider\n         */\n        kmip?: {\n            /**\n             * The output endpoint string.\n             * The endpoint consists of a hostname and port separated by a colon.\n             * E.g. \"example.com:123\". A port is always present.\n             */\n            endpoint?: string\n        }\n    }\n    /**\n     * A map of namespaces to a local JSON schema for encryption\n     *\n     * **NOTE**: Supplying options.schemaMap provides more security than relying on JSON Schemas obtained from the server.\n     * It protects against a malicious server advertising a false JSON Schema, which could trick the client into sending decrypted data that should be encrypted.\n     * Schemas supplied in the schemaMap only apply to configuring automatic encryption for Client-Side Field Level Encryption.\n     * Other validation rules in the JSON schema will not be enforced by the driver and will result in an error.\n     */\n    schemaMap?: Document\n    /** @experimental Public Technical Preview: Supply a schema for the encrypted fields in the document  */\n    encryptedFieldsMap?: Document\n    /** Allows the user to bypass auto encryption, maintaining implicit decryption */\n    bypassAutoEncryption?: boolean\n    /** @experimental Public Technical Preview: Allows users to bypass query analysis */\n    bypassQueryAnalysis?: boolean\n    options?: {\n        /** An optional hook to catch logging messages from the underlying encryption engine */\n        logger?: (level: AutoEncryptionLoggerLevel, message: string) => void\n    }\n    extraOptions?: {\n        /**\n         * A local process the driver communicates with to determine how to encrypt values in a command.\n         * Defaults to \"mongodb://%2Fvar%2Fmongocryptd.sock\" if domain sockets are available or \"mongodb://localhost:27020\" otherwise\n         */\n        mongocryptdURI?: string\n        /** If true, autoEncryption will not attempt to spawn a mongocryptd before connecting  */\n        mongocryptdBypassSpawn?: boolean\n        /** The path to the mongocryptd executable on the system */\n        mongocryptdSpawnPath?: string\n        /** Command line arguments to use when auto-spawning a mongocryptd */\n        mongocryptdSpawnArgs?: string[]\n        /**\n         * Full path to a MongoDB Crypt shared library to be used (instead of mongocryptd).\n         *\n         * This needs to be the path to the file itself, not a directory.\n         * It can be an absolute or relative path. If the path is relative and\n         * its first component is `$ORIGIN`, it will be replaced by the directory\n         * containing the mongodb-client-encryption native addon file. Otherwise,\n         * the path will be interpreted relative to the current working directory.\n         *\n         * Currently, loading different MongoDB Crypt shared library files from different\n         * MongoClients in the same process is not supported.\n         *\n         * If this option is provided and no MongoDB Crypt shared library could be loaded\n         * from the specified location, creating the MongoClient will fail.\n         *\n         * If this option is not provided and `cryptSharedLibRequired` is not specified,\n         * the AutoEncrypter will attempt to spawn and/or use mongocryptd according\n         * to the mongocryptd-specific `extraOptions` options.\n         *\n         * Specifying a path prevents mongocryptd from being used as a fallback.\n         *\n         * Requires the MongoDB Crypt shared library, available in MongoDB 6.0 or higher.\n         */\n        cryptSharedLibPath?: string\n        /**\n         * If specified, never use mongocryptd and instead fail when the MongoDB Crypt\n         * shared library could not be loaded.\n         *\n         * This is always true when `cryptSharedLibPath` is specified.\n         *\n         * Requires the MongoDB Crypt shared library, available in MongoDB 6.0 or higher.\n         */\n        cryptSharedLibRequired?: boolean\n        /* Excluded from this release type: cryptSharedLibSearchPaths */\n    }\n    proxyOptions?: ProxyOptions\n    /** The TLS options to use connecting to the KMS provider */\n    tlsOptions?: {\n        aws?: AutoEncryptionTlsOptions\n        local?: AutoEncryptionTlsOptions\n        azure?: AutoEncryptionTlsOptions\n        gcp?: AutoEncryptionTlsOptions\n        kmip?: AutoEncryptionTlsOptions\n    }\n}\n\n/** @public */\nexport declare interface AutoEncryptionTlsOptions {\n    /**\n     * Specifies the location of a local .pem file that contains\n     * either the client's TLS/SSL certificate and key or only the\n     * client's TLS/SSL key when tlsCertificateFile is used to\n     * provide the certificate.\n     */\n    tlsCertificateKeyFile?: string\n    /**\n     * Specifies the password to de-crypt the tlsCertificateKeyFile.\n     */\n    tlsCertificateKeyFilePassword?: string\n    /**\n     * Specifies the location of a local .pem file that contains the\n     * root certificate chain from the Certificate Authority.\n     * This file is used to validate the certificate presented by the\n     * KMS provider.\n     */\n    tlsCAFile?: string\n}\n\n/**\n * Keeps the state of a unordered batch so we can rewrite the results\n * correctly after command execution\n *\n * @public\n */\nexport declare class Batch<T = Document> {\n    originalZeroIndex: number\n    currentIndex: number\n    originalIndexes: number[]\n    batchType: BatchType\n    operations: T[]\n    size: number\n    sizeBytes: number\n    constructor(batchType: BatchType, originalZeroIndex: number)\n}\n\n/** @public */\nexport declare const BatchType: Readonly<{\n    readonly INSERT: 1\n    readonly UPDATE: 2\n    readonly DELETE: 3\n}>\n\n/** @public */\nexport declare type BatchType = (typeof BatchType)[keyof typeof BatchType]\n\nexport { Binary }\n\n/* Excluded from this release type: BinMsg */\n\n/** @public */\nexport declare type BitwiseFilter =\n    | number /** numeric bit mask */\n    | Binary /** BinData bit mask */\n    | ReadonlyArray<number>\n\nexport { BSON }\n\nexport { BSONRegExp }\n\n/**\n * BSON Serialization options.\n * @public\n */\nexport declare interface BSONSerializeOptions\n    extends Omit<SerializeOptions, \"index\">,\n        Omit<\n            DeserializeOptions,\n            | \"evalFunctions\"\n            | \"cacheFunctions\"\n            | \"cacheFunctionsCrc32\"\n            | \"allowObjectSmallerThanBufferSize\"\n            | \"index\"\n            | \"validation\"\n        > {\n    /**\n     * Enabling the raw option will return a [Node.js Buffer](https://nodejs.org/api/buffer.html)\n     * which is allocated using [allocUnsafe API](https://nodejs.org/api/buffer.html#static-method-bufferallocunsafesize).\n     * See this section from the [Node.js Docs here](https://nodejs.org/api/buffer.html#what-makes-bufferallocunsafe-and-bufferallocunsafeslow-unsafe)\n     * for more detail about what \"unsafe\" refers to in this context.\n     * If you need to maintain your own editable clone of the bytes returned for an extended life time of the process, it is recommended you allocate\n     * your own buffer and clone the contents:\n     *\n     * @example\n     * ```ts\n     * const raw = await collection.findOne({}, { raw: true });\n     * const myBuffer = Buffer.alloc(raw.byteLength);\n     * myBuffer.set(raw, 0);\n     * // Only save and use `myBuffer` beyond this point\n     * ```\n     *\n     * @remarks\n     * Please note there is a known limitation where this option cannot be used at the MongoClient level (see [NODE-3946](https://jira.mongodb.org/browse/NODE-3946)).\n     * It does correctly work at `Db`, `Collection`, and per operation the same as other BSON options work.\n     */\n    raw?: boolean\n    /** Enable utf8 validation when deserializing BSON documents.  Defaults to true. */\n    enableUtf8Validation?: boolean\n}\n\nexport { BSONSymbol }\n\nexport { BSONType }\n\n/** @public */\nexport declare type BSONTypeAlias = keyof typeof BSONType\n\n/* Excluded from this release type: BufferPool */\n\n/** @public */\nexport declare abstract class BulkOperationBase {\n    isOrdered: boolean\n    /* Excluded from this release type: s */\n    operationId?: number\n    /* Excluded from this release type: __constructor */\n    /**\n     * Add a single insert document to the bulk operation\n     *\n     * @example\n     * ```ts\n     * const bulkOp = collection.initializeOrderedBulkOp();\n     *\n     * // Adds three inserts to the bulkOp.\n     * bulkOp\n     *   .insert({ a: 1 })\n     *   .insert({ b: 2 })\n     *   .insert({ c: 3 });\n     * await bulkOp.execute();\n     * ```\n     */\n    insert(document: Document): BulkOperationBase\n    /**\n     * Builds a find operation for an update/updateOne/delete/deleteOne/replaceOne.\n     * Returns a builder object used to complete the definition of the operation.\n     *\n     * @example\n     * ```ts\n     * const bulkOp = collection.initializeOrderedBulkOp();\n     *\n     * // Add an updateOne to the bulkOp\n     * bulkOp.find({ a: 1 }).updateOne({ $set: { b: 2 } });\n     *\n     * // Add an updateMany to the bulkOp\n     * bulkOp.find({ c: 3 }).update({ $set: { d: 4 } });\n     *\n     * // Add an upsert\n     * bulkOp.find({ e: 5 }).upsert().updateOne({ $set: { f: 6 } });\n     *\n     * // Add a deletion\n     * bulkOp.find({ g: 7 }).deleteOne();\n     *\n     * // Add a multi deletion\n     * bulkOp.find({ h: 8 }).delete();\n     *\n     * // Add a replaceOne\n     * bulkOp.find({ i: 9 }).replaceOne({writeConcern: { j: 10 }});\n     *\n     * // Update using a pipeline (requires Mongodb 4.2 or higher)\n     * bulk.find({ k: 11, y: { $exists: true }, z: { $exists: true } }).updateOne([\n     *   { $set: { total: { $sum: [ '$y', '$z' ] } } }\n     * ]);\n     *\n     * // All of the ops will now be executed\n     * await bulkOp.execute();\n     * ```\n     */\n    find(selector: Document): FindOperators\n    /** Specifies a raw operation to perform in the bulk write. */\n    raw(op: AnyBulkWriteOperation): this\n    get bsonOptions(): BSONSerializeOptions\n    get writeConcern(): WriteConcern | undefined\n    get batches(): Batch[]\n    execute(options?: BulkWriteOptions): Promise<BulkWriteResult>\n    /* Excluded from this release type: handleWriteError */\n    abstract addToOperationsList(\n        batchType: BatchType,\n        document: Document | UpdateStatement | DeleteStatement,\n    ): this\n}\n\n/* Excluded from this release type: BulkOperationPrivate */\n\n/* Excluded from this release type: BulkResult */\n\n/** @public */\nexport declare interface BulkWriteOperationError {\n    index: number\n    code: number\n    errmsg: string\n    errInfo: Document\n    op: Document | UpdateStatement | DeleteStatement\n}\n\n/** @public */\nexport declare interface BulkWriteOptions extends CommandOperationOptions {\n    /** Allow driver to bypass schema validation in MongoDB 3.2 or higher. */\n    bypassDocumentValidation?: boolean\n    /** If true, when an insert fails, don't execute the remaining writes. If false, continue with remaining inserts when one fails. */\n    ordered?: boolean\n    /** Force server to assign _id values instead of driver. */\n    forceServerObjectId?: boolean\n    /** Map of parameter names and values that can be accessed using $$var (requires MongoDB 5.0). */\n    let?: Document\n}\n\n/**\n * @public\n * The result of a bulk write.\n */\nexport declare class BulkWriteResult {\n    private readonly result\n    /** Number of documents inserted. */\n    readonly insertedCount: number\n    /** Number of documents matched for update. */\n    readonly matchedCount: number\n    /** Number of documents modified. */\n    readonly modifiedCount: number\n    /** Number of documents deleted. */\n    readonly deletedCount: number\n    /** Number of documents upserted. */\n    readonly upsertedCount: number\n    /** Upserted document generated Id's, hash key is the index of the originating operation */\n    readonly upsertedIds: {\n        [key: number]: any\n    }\n    /** Inserted document generated Id's, hash key is the index of the originating operation */\n    readonly insertedIds: {\n        [key: number]: any\n    }\n    private static generateIdMap\n    /* Excluded from this release type: __constructor */\n    /** Evaluates to true if the bulk operation correctly executes */\n    get ok(): number\n    /** The number of inserted documents */\n    get nInserted(): number\n    /** Number of upserted documents */\n    get nUpserted(): number\n    /** Number of matched documents */\n    get nMatched(): number\n    /** Number of documents updated physically on disk */\n    get nModified(): number\n    /** Number of removed documents */\n    get nRemoved(): number\n    /** Returns an array of all inserted ids */\n    getInsertedIds(): Document[]\n    /** Returns an array of all upserted ids */\n    getUpsertedIds(): Document[]\n    /** Returns the upserted id at the given index */\n    getUpsertedIdAt(index: number): Document | undefined\n    /** Returns raw internal result */\n    getRawResponse(): Document\n    /** Returns true if the bulk operation contains a write error */\n    hasWriteErrors(): boolean\n    /** Returns the number of write errors off the bulk operation */\n    getWriteErrorCount(): number\n    /** Returns a specific write error object */\n    getWriteErrorAt(index: number): WriteError | undefined\n    /** Retrieve all write errors */\n    getWriteErrors(): WriteError[]\n    /** Retrieve the write concern error if one exists */\n    getWriteConcernError(): WriteConcernError | undefined\n    toString(): string\n    isOk(): boolean\n}\n\n/**\n * MongoDB Driver style callback\n * @public\n */\nexport declare type Callback<T = any> = (error?: AnyError, result?: T) => void\n\n/** @public */\nexport declare class CancellationToken extends TypedEventEmitter<{\n    cancel(): void\n}> {}\n\n/**\n * Creates a new Change Stream instance. Normally created using {@link Collection#watch|Collection.watch()}.\n * @public\n */\nexport declare class ChangeStream<\n    TSchema extends Document = Document,\n    TChange extends Document = ChangeStreamDocument<TSchema>,\n> extends TypedEventEmitter<ChangeStreamEvents<TSchema, TChange>> {\n    pipeline: Document[]\n    /**\n     * @remarks WriteConcern can still be present on the options because\n     * we inherit options from the client/db/collection.  The\n     * key must be present on the options in order to delete it.\n     * This allows typescript to delete the key but will\n     * not allow a writeConcern to be assigned as a property on options.\n     */\n    options: ChangeStreamOptions & {\n        writeConcern?: never\n    }\n    parent: MongoClient | Db | Collection\n    namespace: MongoDBNamespace\n    type: symbol\n    /* Excluded from this release type: cursor */\n    streamOptions?: CursorStreamOptions\n    /* Excluded from this release type: [kCursorStream] */\n    /* Excluded from this release type: [kClosed] */\n    /* Excluded from this release type: [kMode] */\n    /** @event */\n    static readonly RESPONSE: \"response\"\n    /** @event */\n    static readonly MORE: \"more\"\n    /** @event */\n    static readonly INIT: \"init\"\n    /** @event */\n    static readonly CLOSE: \"close\"\n    /**\n     * Fired for each new matching change in the specified namespace. Attaching a `change`\n     * event listener to a Change Stream will switch the stream into flowing mode. Data will\n     * then be passed as soon as it is available.\n     * @event\n     */\n    static readonly CHANGE: \"change\"\n    /** @event */\n    static readonly END: \"end\"\n    /** @event */\n    static readonly ERROR: \"error\"\n    /**\n     * Emitted each time the change stream stores a new resume token.\n     * @event\n     */\n    static readonly RESUME_TOKEN_CHANGED: \"resumeTokenChanged\"\n    /* Excluded from this release type: __constructor */\n    /* Excluded from this release type: cursorStream */\n    /** The cached resume token that is used to resume after the most recently returned change. */\n    get resumeToken(): ResumeToken\n    /** Check if there is any document still available in the Change Stream */\n    hasNext(): Promise<boolean>\n    /** Get the next available document from the Change Stream. */\n    next(): Promise<TChange>\n    /**\n     * Try to get the next available document from the Change Stream's cursor or `null` if an empty batch is returned\n     */\n    tryNext(): Promise<Document | null>\n    [Symbol.asyncIterator](): AsyncGenerator<TChange, void, void>\n    /** Is the cursor closed */\n    get closed(): boolean\n    /** Close the Change Stream */\n    close(): Promise<void>\n    /**\n     * Return a modified Readable stream including a possible transform method.\n     *\n     * NOTE: When using a Stream to process change stream events, the stream will\n     * NOT automatically resume in the case a resumable error is encountered.\n     *\n     * @throws MongoChangeStreamError if the underlying cursor or the change stream is closed\n     */\n    stream(options?: CursorStreamOptions): Readable & AsyncIterable<TChange>\n    /* Excluded from this release type: _setIsEmitter */\n    /* Excluded from this release type: _setIsIterator */\n    /* Excluded from this release type: _createChangeStreamCursor */\n    /* Excluded from this release type: _closeEmitterModeWithError */\n    /* Excluded from this release type: _streamEvents */\n    /* Excluded from this release type: _endStream */\n    /* Excluded from this release type: _processChange */\n    /* Excluded from this release type: _processErrorStreamMode */\n    /* Excluded from this release type: _processErrorIteratorMode */\n}\n\n/* Excluded from this release type: ChangeStreamAggregateRawResult */\n\n/**\n * Only present when the `showExpandedEvents` flag is enabled.\n * @public\n * @see https://www.mongodb.com/docs/manual/reference/change-events/\n */\nexport declare interface ChangeStreamCollModDocument\n    extends ChangeStreamDocumentCommon,\n        ChangeStreamDocumentCollectionUUID {\n    /** Describes the type of operation represented in this change notification */\n    operationType: \"modify\"\n}\n\n/**\n * @public\n * @see https://www.mongodb.com/docs/manual/reference/change-events/\n */\nexport declare interface ChangeStreamCreateDocument\n    extends ChangeStreamDocumentCommon,\n        ChangeStreamDocumentCollectionUUID {\n    /** Describes the type of operation represented in this change notification */\n    operationType: \"create\"\n}\n\n/**\n * Only present when the `showExpandedEvents` flag is enabled.\n * @public\n * @see https://www.mongodb.com/docs/manual/reference/change-events/\n */\nexport declare interface ChangeStreamCreateIndexDocument\n    extends ChangeStreamDocumentCommon,\n        ChangeStreamDocumentCollectionUUID,\n        ChangeStreamDocumentOperationDescription {\n    /** Describes the type of operation represented in this change notification */\n    operationType: \"createIndexes\"\n}\n\n/* Excluded from this release type: ChangeStreamCursor */\n\n/* Excluded from this release type: ChangeStreamCursorOptions */\n\n/**\n * @public\n * @see https://www.mongodb.com/docs/manual/reference/change-events/#delete-event\n */\nexport declare interface ChangeStreamDeleteDocument<\n    TSchema extends Document = Document,\n> extends ChangeStreamDocumentCommon,\n        ChangeStreamDocumentKey<TSchema>,\n        ChangeStreamDocumentCollectionUUID {\n    /** Describes the type of operation represented in this change notification */\n    operationType: \"delete\"\n    /** Namespace the delete event occurred on */\n    ns: ChangeStreamNameSpace\n    /**\n     * Contains the pre-image of the modified or deleted document if the\n     * pre-image is available for the change event and either 'required' or\n     * 'whenAvailable' was specified for the 'fullDocumentBeforeChange' option\n     * when creating the change stream. If 'whenAvailable' was specified but the\n     * pre-image is unavailable, this will be explicitly set to null.\n     */\n    fullDocumentBeforeChange?: TSchema\n}\n\n/** @public */\nexport declare type ChangeStreamDocument<TSchema extends Document = Document> =\n    | ChangeStreamInsertDocument<TSchema>\n    | ChangeStreamUpdateDocument<TSchema>\n    | ChangeStreamReplaceDocument<TSchema>\n    | ChangeStreamDeleteDocument<TSchema>\n    | ChangeStreamDropDocument\n    | ChangeStreamRenameDocument\n    | ChangeStreamDropDatabaseDocument\n    | ChangeStreamInvalidateDocument\n    | ChangeStreamCreateIndexDocument\n    | ChangeStreamCreateDocument\n    | ChangeStreamCollModDocument\n    | ChangeStreamDropIndexDocument\n    | ChangeStreamShardCollectionDocument\n    | ChangeStreamReshardCollectionDocument\n    | ChangeStreamRefineCollectionShardKeyDocument\n\n/** @public */\nexport declare interface ChangeStreamDocumentCollectionUUID {\n    /**\n     * The UUID (Binary subtype 4) of the collection that the operation was performed on.\n     *\n     * Only present when the `showExpandedEvents` flag is enabled.\n     *\n     * **NOTE:** collectionUUID will be converted to a NodeJS Buffer if the promoteBuffers\n     *    flag is enabled.\n     *\n     * @sinceServerVersion 6.1.0\n     */\n    collectionUUID: Binary\n}\n\n/** @public */\nexport declare interface ChangeStreamDocumentCommon {\n    /**\n     * The id functions as an opaque token for use when resuming an interrupted\n     * change stream.\n     */\n    _id: ResumeToken\n    /**\n     * The timestamp from the oplog entry associated with the event.\n     * For events that happened as part of a multi-document transaction, the associated change stream\n     * notifications will have the same clusterTime value, namely the time when the transaction was committed.\n     * On a sharded cluster, events that occur on different shards can have the same clusterTime but be\n     * associated with different transactions or even not be associated with any transaction.\n     * To identify events for a single transaction, you can use the combination of lsid and txnNumber in the change stream event document.\n     */\n    clusterTime?: Timestamp\n    /**\n     * The transaction number.\n     * Only present if the operation is part of a multi-document transaction.\n     *\n     * **NOTE:** txnNumber can be a Long if promoteLongs is set to false\n     */\n    txnNumber?: number\n    /**\n     * The identifier for the session associated with the transaction.\n     * Only present if the operation is part of a multi-document transaction.\n     */\n    lsid?: ServerSessionId\n}\n\n/** @public */\nexport declare interface ChangeStreamDocumentKey<\n    TSchema extends Document = Document,\n> {\n    /**\n     * For unsharded collections this contains a single field `_id`.\n     * For sharded collections, this will contain all the components of the shard key\n     */\n    documentKey: {\n        _id: InferIdType<TSchema>\n        [shardKey: string]: any\n    }\n}\n\n/** @public */\nexport declare interface ChangeStreamDocumentOperationDescription {\n    /**\n     * An description of the operation.\n     *\n     * Only present when the `showExpandedEvents` flag is enabled.\n     *\n     * @sinceServerVersion 6.1.0\n     */\n    operationDescription?: Document\n}\n\n/**\n * @public\n * @see https://www.mongodb.com/docs/manual/reference/change-events/#dropdatabase-event\n */\nexport declare interface ChangeStreamDropDatabaseDocument\n    extends ChangeStreamDocumentCommon {\n    /** Describes the type of operation represented in this change notification */\n    operationType: \"dropDatabase\"\n    /** The database dropped */\n    ns: {\n        db: string\n    }\n}\n\n/**\n * @public\n * @see https://www.mongodb.com/docs/manual/reference/change-events/#drop-event\n */\nexport declare interface ChangeStreamDropDocument\n    extends ChangeStreamDocumentCommon,\n        ChangeStreamDocumentCollectionUUID {\n    /** Describes the type of operation represented in this change notification */\n    operationType: \"drop\"\n    /** Namespace the drop event occurred on */\n    ns: ChangeStreamNameSpace\n}\n\n/**\n * Only present when the `showExpandedEvents` flag is enabled.\n * @public\n * @see https://www.mongodb.com/docs/manual/reference/change-events/\n */\nexport declare interface ChangeStreamDropIndexDocument\n    extends ChangeStreamDocumentCommon,\n        ChangeStreamDocumentCollectionUUID,\n        ChangeStreamDocumentOperationDescription {\n    /** Describes the type of operation represented in this change notification */\n    operationType: \"dropIndexes\"\n}\n\n/** @public */\nexport declare type ChangeStreamEvents<\n    TSchema extends Document = Document,\n    TChange extends Document = ChangeStreamDocument<TSchema>,\n> = {\n    resumeTokenChanged(token: ResumeToken): void\n    init(response: any): void\n    more(response?: any): void\n    response(): void\n    end(): void\n    error(error: Error): void\n    change(change: TChange): void\n} & AbstractCursorEvents\n\n/**\n * @public\n * @see https://www.mongodb.com/docs/manual/reference/change-events/#insert-event\n */\nexport declare interface ChangeStreamInsertDocument<\n    TSchema extends Document = Document,\n> extends ChangeStreamDocumentCommon,\n        ChangeStreamDocumentKey<TSchema>,\n        ChangeStreamDocumentCollectionUUID {\n    /** Describes the type of operation represented in this change notification */\n    operationType: \"insert\"\n    /** This key will contain the document being inserted */\n    fullDocument: TSchema\n    /** Namespace the insert event occurred on */\n    ns: ChangeStreamNameSpace\n}\n\n/**\n * @public\n * @see https://www.mongodb.com/docs/manual/reference/change-events/#invalidate-event\n */\nexport declare interface ChangeStreamInvalidateDocument\n    extends ChangeStreamDocumentCommon {\n    /** Describes the type of operation represented in this change notification */\n    operationType: \"invalidate\"\n}\n\n/** @public */\nexport declare interface ChangeStreamNameSpace {\n    db: string\n    coll: string\n}\n\n/**\n * Options that can be passed to a ChangeStream. Note that startAfter, resumeAfter, and startAtOperationTime are all mutually exclusive, and the server will error if more than one is specified.\n * @public\n */\nexport declare interface ChangeStreamOptions\n    extends Omit<AggregateOptions, \"writeConcern\"> {\n    /**\n     * Allowed values: 'updateLookup', 'whenAvailable', 'required'.\n     *\n     * When set to 'updateLookup', the change notification for partial updates\n     * will include both a delta describing the changes to the document as well\n     * as a copy of the entire document that was changed from some time after\n     * the change occurred.\n     *\n     * When set to 'whenAvailable', configures the change stream to return the\n     * post-image of the modified document for replace and update change events\n     * if the post-image for this event is available.\n     *\n     * When set to 'required', the same behavior as 'whenAvailable' except that\n     * an error is raised if the post-image is not available.\n     */\n    fullDocument?: string\n    /**\n     * Allowed values: 'whenAvailable', 'required', 'off'.\n     *\n     * The default is to not send a value, which is equivalent to 'off'.\n     *\n     * When set to 'whenAvailable', configures the change stream to return the\n     * pre-image of the modified document for replace, update, and delete change\n     * events if it is available.\n     *\n     * When set to 'required', the same behavior as 'whenAvailable' except that\n     * an error is raised if the pre-image is not available.\n     */\n    fullDocumentBeforeChange?: string\n    /** The maximum amount of time for the server to wait on new documents to satisfy a change stream query. */\n    maxAwaitTimeMS?: number\n    /**\n     * Allows you to start a changeStream after a specified event.\n     * @see https://www.mongodb.com/docs/manual/changeStreams/#resumeafter-for-change-streams\n     */\n    resumeAfter?: ResumeToken\n    /**\n     * Similar to resumeAfter, but will allow you to start after an invalidated event.\n     * @see https://www.mongodb.com/docs/manual/changeStreams/#startafter-for-change-streams\n     */\n    startAfter?: ResumeToken\n    /** Will start the changeStream after the specified operationTime. */\n    startAtOperationTime?: OperationTime\n    /**\n     * The number of documents to return per batch.\n     * @see https://www.mongodb.com/docs/manual/reference/command/aggregate\n     */\n    batchSize?: number\n    /**\n     * When enabled, configures the change stream to include extra change events.\n     *\n     * - createIndexes\n     * - dropIndexes\n     * - modify\n     * - create\n     * - shardCollection\n     * - reshardCollection\n     * - refineCollectionShardKey\n     */\n    showExpandedEvents?: boolean\n}\n\n/**\n * @public\n * @see https://www.mongodb.com/docs/manual/reference/change-events/\n */\nexport declare interface ChangeStreamRefineCollectionShardKeyDocument\n    extends ChangeStreamDocumentCommon,\n        ChangeStreamDocumentCollectionUUID,\n        ChangeStreamDocumentOperationDescription {\n    /** Describes the type of operation represented in this change notification */\n    operationType: \"refineCollectionShardKey\"\n}\n\n/**\n * @public\n * @see https://www.mongodb.com/docs/manual/reference/change-events/#rename-event\n */\nexport declare interface ChangeStreamRenameDocument\n    extends ChangeStreamDocumentCommon,\n        ChangeStreamDocumentCollectionUUID {\n    /** Describes the type of operation represented in this change notification */\n    operationType: \"rename\"\n    /** The new name for the `ns.coll` collection */\n    to: {\n        db: string\n        coll: string\n    }\n    /** The \"from\" namespace that the rename occurred on */\n    ns: ChangeStreamNameSpace\n}\n\n/**\n * @public\n * @see https://www.mongodb.com/docs/manual/reference/change-events/#replace-event\n */\nexport declare interface ChangeStreamReplaceDocument<\n    TSchema extends Document = Document,\n> extends ChangeStreamDocumentCommon,\n        ChangeStreamDocumentKey<TSchema> {\n    /** Describes the type of operation represented in this change notification */\n    operationType: \"replace\"\n    /** The fullDocument of a replace event represents the document after the insert of the replacement document */\n    fullDocument: TSchema\n    /** Namespace the replace event occurred on */\n    ns: ChangeStreamNameSpace\n    /**\n     * Contains the pre-image of the modified or deleted document if the\n     * pre-image is available for the change event and either 'required' or\n     * 'whenAvailable' was specified for the 'fullDocumentBeforeChange' option\n     * when creating the change stream. If 'whenAvailable' was specified but the\n     * pre-image is unavailable, this will be explicitly set to null.\n     */\n    fullDocumentBeforeChange?: TSchema\n}\n\n/**\n * @public\n * @see https://www.mongodb.com/docs/manual/reference/change-events/\n */\nexport declare interface ChangeStreamReshardCollectionDocument\n    extends ChangeStreamDocumentCommon,\n        ChangeStreamDocumentCollectionUUID,\n        ChangeStreamDocumentOperationDescription {\n    /** Describes the type of operation represented in this change notification */\n    operationType: \"reshardCollection\"\n}\n\n/**\n * @public\n * @see https://www.mongodb.com/docs/manual/reference/change-events/\n */\nexport declare interface ChangeStreamShardCollectionDocument\n    extends ChangeStreamDocumentCommon,\n        ChangeStreamDocumentCollectionUUID,\n        ChangeStreamDocumentOperationDescription {\n    /** Describes the type of operation represented in this change notification */\n    operationType: \"shardCollection\"\n}\n\n/**\n * @public\n * @see https://www.mongodb.com/docs/manual/reference/change-events/#update-event\n */\nexport declare interface ChangeStreamUpdateDocument<\n    TSchema extends Document = Document,\n> extends ChangeStreamDocumentCommon,\n        ChangeStreamDocumentKey<TSchema>,\n        ChangeStreamDocumentCollectionUUID {\n    /** Describes the type of operation represented in this change notification */\n    operationType: \"update\"\n    /**\n     * This is only set if `fullDocument` is set to `'updateLookup'`\n     * Contains the point-in-time post-image of the modified document if the\n     * post-image is available and either 'required' or 'whenAvailable' was\n     * specified for the 'fullDocument' option when creating the change stream.\n     */\n    fullDocument?: TSchema\n    /** Contains a description of updated and removed fields in this operation */\n    updateDescription: UpdateDescription<TSchema>\n    /** Namespace the update event occurred on */\n    ns: ChangeStreamNameSpace\n    /**\n     * Contains the pre-image of the modified or deleted document if the\n     * pre-image is available for the change event and either 'required' or\n     * 'whenAvailable' was specified for the 'fullDocumentBeforeChange' option\n     * when creating the change stream. If 'whenAvailable' was specified but the\n     * pre-image is unavailable, this will be explicitly set to null.\n     */\n    fullDocumentBeforeChange?: TSchema\n}\n\n/**\n * @public\n * @see https://github.com/mongodb/specifications/blob/master/source/mongodb-handshake/handshake.rst#hello-command\n */\nexport declare interface ClientMetadata {\n    driver: {\n        name: string\n        version: string\n    }\n    os: {\n        type: string\n        name: NodeJS.Platform\n        architecture: string\n        version: string\n    }\n    platform: string\n    application?: {\n        name: string\n    }\n}\n\n/** @public */\nexport declare interface ClientMetadataOptions {\n    driverInfo?: {\n        name?: string\n        version?: string\n        platform?: string\n    }\n    appName?: string\n}\n\n/**\n * A class representing a client session on the server\n *\n * NOTE: not meant to be instantiated directly.\n * @public\n */\nexport declare class ClientSession extends TypedEventEmitter<ClientSessionEvents> {\n    /* Excluded from this release type: client */\n    /* Excluded from this release type: sessionPool */\n    hasEnded: boolean\n    clientOptions?: MongoOptions\n    supports: {\n        causalConsistency: boolean\n    }\n    clusterTime?: ClusterTime\n    operationTime?: Timestamp\n    explicit: boolean\n    /* Excluded from this release type: owner */\n    defaultTransactionOptions: TransactionOptions\n    transaction: Transaction\n    /* Excluded from this release type: [kServerSession] */\n    /* Excluded from this release type: [kSnapshotTime] */\n    /* Excluded from this release type: [kSnapshotEnabled] */\n    /* Excluded from this release type: [kPinnedConnection] */\n    /* Excluded from this release type: [kTxnNumberIncrement] */\n    /* Excluded from this release type: __constructor */\n    /** The server id associated with this session */\n    get id(): ServerSessionId | undefined\n    get serverSession(): ServerSession\n    /** Whether or not this session is configured for snapshot reads */\n    get snapshotEnabled(): boolean\n    get loadBalanced(): boolean\n    /* Excluded from this release type: pinnedConnection */\n    /* Excluded from this release type: pin */\n    /* Excluded from this release type: unpin */\n    get isPinned(): boolean\n    /**\n     * Ends this session on the server\n     *\n     * @param options - Optional settings. Currently reserved for future use\n     */\n    endSession(options?: EndSessionOptions): Promise<void>\n    /**\n     * Advances the operationTime for a ClientSession.\n     *\n     * @param operationTime - the `BSON.Timestamp` of the operation type it is desired to advance to\n     */\n    advanceOperationTime(operationTime: Timestamp): void\n    /**\n     * Advances the clusterTime for a ClientSession to the provided clusterTime of another ClientSession\n     *\n     * @param clusterTime - the $clusterTime returned by the server from another session in the form of a document containing the `BSON.Timestamp` clusterTime and signature\n     */\n    advanceClusterTime(clusterTime: ClusterTime): void\n    /**\n     * Used to determine if this session equals another\n     *\n     * @param session - The session to compare to\n     */\n    equals(session: ClientSession): boolean\n    /**\n     * Increment the transaction number on the internal ServerSession\n     *\n     * @privateRemarks\n     * This helper increments a value stored on the client session that will be\n     * added to the serverSession's txnNumber upon applying it to a command.\n     * This is because the serverSession is lazily acquired after a connection is obtained\n     */\n    incrementTransactionNumber(): void\n    /** @returns whether this session is currently in a transaction or not */\n    inTransaction(): boolean\n    /**\n     * Starts a new transaction with the given options.\n     *\n     * @param options - Options for the transaction\n     */\n    startTransaction(options?: TransactionOptions): void\n    /**\n     * Commits the currently active transaction in this session.\n     */\n    commitTransaction(): Promise<Document>\n    /**\n     * Aborts the currently active transaction in this session.\n     */\n    abortTransaction(): Promise<Document>\n    /**\n     * This is here to ensure that ClientSession is never serialized to BSON.\n     */\n    toBSON(): never\n    /**\n     * Runs a provided callback within a transaction, retrying either the commitTransaction operation\n     * or entire transaction as needed (and when the error permits) to better ensure that\n     * the transaction can complete successfully.\n     *\n     * **IMPORTANT:** This method requires the user to return a Promise, and `await` all operations.\n     * Any callbacks that do not return a Promise will result in undefined behavior.\n     *\n     * @remarks\n     * This function:\n     * - Will return the command response from the final commitTransaction if every operation is successful (can be used as a truthy object)\n     * - Will return `undefined` if the transaction is explicitly aborted with `await session.abortTransaction()`\n     * - Will throw if one of the operations throws or `throw` statement is used inside the `withTransaction` callback\n     *\n     * Checkout a descriptive example here:\n     * @see https://www.mongodb.com/developer/quickstart/node-transactions/\n     *\n     * @param fn - callback to run within a transaction\n     * @param options - optional settings for the transaction\n     * @returns A raw command response or undefined\n     */\n    withTransaction<T = void>(\n        fn: WithTransactionCallback<T>,\n        options?: TransactionOptions,\n    ): Promise<Document | undefined>\n}\n\n/** @public */\nexport declare type ClientSessionEvents = {\n    ended(session: ClientSession): void\n}\n\n/** @public */\nexport declare interface ClientSessionOptions {\n    /** Whether causal consistency should be enabled on this session */\n    causalConsistency?: boolean\n    /** Whether all read operations should be read from the same snapshot for this session (NOTE: not compatible with `causalConsistency=true`) */\n    snapshot?: boolean\n    /** The default TransactionOptions to use for transactions started on this session. */\n    defaultTransactionOptions?: TransactionOptions\n    /* Excluded from this release type: owner */\n    /* Excluded from this release type: explicit */\n    /* Excluded from this release type: initialClusterTime */\n}\n\n/** @public */\nexport declare interface CloseOptions {\n    force?: boolean\n}\n\n/** @public\n * Configuration options for clustered collections\n * @see https://www.mongodb.com/docs/manual/core/clustered-collections/\n */\nexport declare interface ClusteredCollectionOptions extends Document {\n    name?: string\n    key: Document\n    unique: boolean\n}\n\n/** @public */\nexport declare interface ClusterTime {\n    clusterTime: Timestamp\n    signature: {\n        hash: Binary\n        keyId: Long\n    }\n}\n\nexport { Code }\n\n/** @public */\nexport declare interface CollationOptions {\n    locale: string\n    caseLevel?: boolean\n    caseFirst?: string\n    strength?: number\n    numericOrdering?: boolean\n    alternate?: string\n    maxVariable?: string\n    backwards?: boolean\n    normalization?: boolean\n}\n\n/**\n * The **Collection** class is an internal class that embodies a MongoDB collection\n * allowing for insert/find/update/delete and other command operation on that MongoDB collection.\n *\n * **COLLECTION Cannot directly be instantiated**\n * @public\n *\n * @example\n * ```ts\n * import { MongoClient } from 'mongodb';\n *\n * interface Pet {\n *   name: string;\n *   kind: 'dog' | 'cat' | 'fish';\n * }\n *\n * const client = new MongoClient('mongodb://localhost:27017');\n * const pets = client.db().collection<Pet>('pets');\n *\n * const petCursor = pets.find();\n *\n * for await (const pet of petCursor) {\n *   console.log(`${pet.name} is a ${pet.kind}!`);\n * }\n * ```\n */\nexport declare class Collection<TSchema extends Document = Document> {\n    /* Excluded from this release type: s */\n    /* Excluded from this release type: __constructor */\n    /**\n     * The name of the database this collection belongs to\n     */\n    get dbName(): string\n    /**\n     * The name of this collection\n     */\n    get collectionName(): string\n    /**\n     * The namespace of this collection, in the format `${this.dbName}.${this.collectionName}`\n     */\n    get namespace(): string\n    /**\n     * The current readConcern of the collection. If not explicitly defined for\n     * this collection, will be inherited from the parent DB\n     */\n    get readConcern(): ReadConcern | undefined\n    /**\n     * The current readPreference of the collection. If not explicitly defined for\n     * this collection, will be inherited from the parent DB\n     */\n    get readPreference(): ReadPreference | undefined\n    get bsonOptions(): BSONSerializeOptions\n    /**\n     * The current writeConcern of the collection. If not explicitly defined for\n     * this collection, will be inherited from the parent DB\n     */\n    get writeConcern(): WriteConcern | undefined\n    /** The current index hint for the collection */\n    get hint(): Hint | undefined\n    set hint(v: Hint | undefined)\n    /**\n     * Inserts a single document into MongoDB. If documents passed in do not contain the **_id** field,\n     * one will be added to each of the documents missing it by the driver, mutating the document. This behavior\n     * can be overridden by setting the **forceServerObjectId** flag.\n     *\n     * @param doc - The document to insert\n     * @param options - Optional settings for the command\n     */\n    insertOne(\n        doc: OptionalUnlessRequiredId<TSchema>,\n        options?: InsertOneOptions,\n    ): Promise<InsertOneResult<TSchema>>\n    /**\n     * Inserts an array of documents into MongoDB. If documents passed in do not contain the **_id** field,\n     * one will be added to each of the documents missing it by the driver, mutating the document. This behavior\n     * can be overridden by setting the **forceServerObjectId** flag.\n     *\n     * @param docs - The documents to insert\n     * @param options - Optional settings for the command\n     */\n    insertMany(\n        docs: OptionalUnlessRequiredId<TSchema>[],\n        options?: BulkWriteOptions,\n    ): Promise<InsertManyResult<TSchema>>\n    /**\n     * Perform a bulkWrite operation without a fluent API\n     *\n     * Legal operation types are\n     * - `insertOne`\n     * - `replaceOne`\n     * - `updateOne`\n     * - `updateMany`\n     * - `deleteOne`\n     * - `deleteMany`\n     *\n     * If documents passed in do not contain the **_id** field,\n     * one will be added to each of the documents missing it by the driver, mutating the document. This behavior\n     * can be overridden by setting the **forceServerObjectId** flag.\n     *\n     * @param operations - Bulk operations to perform\n     * @param options - Optional settings for the command\n     * @throws MongoDriverError if operations is not an array\n     */\n    bulkWrite(\n        operations: AnyBulkWriteOperation<TSchema>[],\n        options?: BulkWriteOptions,\n    ): Promise<BulkWriteResult>\n    /**\n     * Update a single document in a collection\n     *\n     * @param filter - The filter used to select the document to update\n     * @param update - The update operations to be applied to the document\n     * @param options - Optional settings for the command\n     */\n    updateOne(\n        filter: Filter<TSchema>,\n        update: UpdateFilter<TSchema> | Partial<TSchema>,\n        options?: UpdateOptions,\n    ): Promise<UpdateResult>\n    /**\n     * Replace a document in a collection with another document\n     *\n     * @param filter - The filter used to select the document to replace\n     * @param replacement - The Document that replaces the matching document\n     * @param options - Optional settings for the command\n     */\n    replaceOne(\n        filter: Filter<TSchema>,\n        replacement: WithoutId<TSchema>,\n        options?: ReplaceOptions,\n    ): Promise<UpdateResult | Document>\n    /**\n     * Update multiple documents in a collection\n     *\n     * @param filter - The filter used to select the documents to update\n     * @param update - The update operations to be applied to the documents\n     * @param options - Optional settings for the command\n     */\n    updateMany(\n        filter: Filter<TSchema>,\n        update: UpdateFilter<TSchema>,\n        options?: UpdateOptions,\n    ): Promise<UpdateResult>\n    /**\n     * Delete a document from a collection\n     *\n     * @param filter - The filter used to select the document to remove\n     * @param options - Optional settings for the command\n     */\n    deleteOne(\n        filter?: Filter<TSchema>,\n        options?: DeleteOptions,\n    ): Promise<DeleteResult>\n    /**\n     * Delete multiple documents from a collection\n     *\n     * @param filter - The filter used to select the documents to remove\n     * @param options - Optional settings for the command\n     */\n    deleteMany(\n        filter?: Filter<TSchema>,\n        options?: DeleteOptions,\n    ): Promise<DeleteResult>\n    /**\n     * Rename the collection.\n     *\n     * @remarks\n     * This operation does not inherit options from the Db or MongoClient.\n     *\n     * @param newName - New name of of the collection.\n     * @param options - Optional settings for the command\n     */\n    rename(newName: string, options?: RenameOptions): Promise<Collection>\n    /**\n     * Drop the collection from the database, removing it permanently. New accesses will create a new collection.\n     *\n     * @param options - Optional settings for the command\n     */\n    drop(options?: DropCollectionOptions): Promise<boolean>\n    /**\n     * Fetches the first document that matches the filter\n     *\n     * @param filter - Query for find Operation\n     * @param options - Optional settings for the command\n     */\n    findOne(): Promise<WithId<TSchema> | null>\n    findOne(filter: Filter<TSchema>): Promise<WithId<TSchema> | null>\n    findOne(\n        filter: Filter<TSchema>,\n        options: FindOptions,\n    ): Promise<WithId<TSchema> | null>\n    findOne<T = TSchema>(): Promise<T | null>\n    findOne<T = TSchema>(filter: Filter<TSchema>): Promise<T | null>\n    findOne<T = TSchema>(\n        filter: Filter<TSchema>,\n        options?: FindOptions,\n    ): Promise<T | null>\n    /**\n     * Creates a cursor for a filter that can be used to iterate over results from MongoDB\n     *\n     * @param filter - The filter predicate. If unspecified, then all documents in the collection will match the predicate\n     */\n    find(): FindCursor<WithId<TSchema>>\n    find(\n        filter: Filter<TSchema>,\n        options?: FindOptions,\n    ): FindCursor<WithId<TSchema>>\n    find<T extends Document>(\n        filter: Filter<TSchema>,\n        options?: FindOptions,\n    ): FindCursor<T>\n    /**\n     * Returns the options of the collection.\n     *\n     * @param options - Optional settings for the command\n     */\n    options(options?: OperationOptions): Promise<Document>\n    /**\n     * Returns if the collection is a capped collection\n     *\n     * @param options - Optional settings for the command\n     */\n    isCapped(options?: OperationOptions): Promise<boolean>\n    /**\n     * Creates an index on the db and collection collection.\n     *\n     * @param indexSpec - The field name or index specification to create an index for\n     * @param options - Optional settings for the command\n     *\n     * @example\n     * ```ts\n     * const collection = client.db('foo').collection('bar');\n     *\n     * await collection.createIndex({ a: 1, b: -1 });\n     *\n     * // Alternate syntax for { c: 1, d: -1 } that ensures order of indexes\n     * await collection.createIndex([ [c, 1], [d, -1] ]);\n     *\n     * // Equivalent to { e: 1 }\n     * await collection.createIndex('e');\n     *\n     * // Equivalent to { f: 1, g: 1 }\n     * await collection.createIndex(['f', 'g'])\n     *\n     * // Equivalent to { h: 1, i: -1 }\n     * await collection.createIndex([ { h: 1 }, { i: -1 } ]);\n     *\n     * // Equivalent to { j: 1, k: -1, l: 2d }\n     * await collection.createIndex(['j', ['k', -1], { l: '2d' }])\n     * ```\n     */\n    createIndex(\n        indexSpec: IndexSpecification,\n        options?: CreateIndexesOptions,\n    ): Promise<string>\n    /**\n     * Creates multiple indexes in the collection, this method is only supported for\n     * MongoDB 2.6 or higher. Earlier version of MongoDB will throw a command not supported\n     * error.\n     *\n     * **Note**: Unlike {@link Collection#createIndex| createIndex}, this function takes in raw index specifications.\n     * Index specifications are defined {@link https://www.mongodb.com/docs/manual/reference/command/createIndexes/| here}.\n     *\n     * @param indexSpecs - An array of index specifications to be created\n     * @param options - Optional settings for the command\n     *\n     * @example\n     * ```ts\n     * const collection = client.db('foo').collection('bar');\n     * await collection.createIndexes([\n     *   // Simple index on field fizz\n     *   {\n     *     key: { fizz: 1 },\n     *   }\n     *   // wildcard index\n     *   {\n     *     key: { '$**': 1 }\n     *   },\n     *   // named index on darmok and jalad\n     *   {\n     *     key: { darmok: 1, jalad: -1 }\n     *     name: 'tanagra'\n     *   }\n     * ]);\n     * ```\n     */\n    createIndexes(\n        indexSpecs: IndexDescription[],\n        options?: CreateIndexesOptions,\n    ): Promise<string[]>\n    /**\n     * Drops an index from this collection.\n     *\n     * @param indexName - Name of the index to drop.\n     * @param options - Optional settings for the command\n     */\n    dropIndex(\n        indexName: string,\n        options?: DropIndexesOptions,\n    ): Promise<Document>\n    /**\n     * Drops all indexes from this collection.\n     *\n     * @param options - Optional settings for the command\n     */\n    dropIndexes(options?: DropIndexesOptions): Promise<Document>\n    /**\n     * Get the list of all indexes information for the collection.\n     *\n     * @param options - Optional settings for the command\n     */\n    listIndexes(options?: ListIndexesOptions): ListIndexesCursor\n    /**\n     * Checks if one or more indexes exist on the collection, fails on first non-existing index\n     *\n     * @param indexes - One or more index names to check.\n     * @param options - Optional settings for the command\n     */\n    indexExists(\n        indexes: string | string[],\n        options?: IndexInformationOptions,\n    ): Promise<boolean>\n    /**\n     * Retrieves this collections index info.\n     *\n     * @param options - Optional settings for the command\n     */\n    indexInformation(options?: IndexInformationOptions): Promise<Document>\n    /**\n     * Gets an estimate of the count of documents in a collection using collection metadata.\n     * This will always run a count command on all server versions.\n     *\n     * due to an oversight in versions 5.0.0-5.0.8 of MongoDB, the count command,\n     * which estimatedDocumentCount uses in its implementation, was not included in v1 of\n     * the Stable API, and so users of the Stable API with estimatedDocumentCount are\n     * recommended to upgrade their server version to 5.0.9+ or set apiStrict: false to avoid\n     * encountering errors.\n     *\n     * @see {@link https://www.mongodb.com/docs/manual/reference/command/count/#behavior|Count: Behavior}\n     * @param options - Optional settings for the command\n     */\n    estimatedDocumentCount(\n        options?: EstimatedDocumentCountOptions,\n    ): Promise<number>\n    /**\n     * Gets the number of documents matching the filter.\n     * For a fast count of the total documents in a collection see {@link Collection#estimatedDocumentCount| estimatedDocumentCount}.\n     * **Note**: When migrating from {@link Collection#count| count} to {@link Collection#countDocuments| countDocuments}\n     * the following query operators must be replaced:\n     *\n     * | Operator | Replacement |\n     * | -------- | ----------- |\n     * | `$where`   | [`$expr`][1] |\n     * | `$near`    | [`$geoWithin`][2] with [`$center`][3] |\n     * | `$nearSphere` | [`$geoWithin`][2] with [`$centerSphere`][4] |\n     *\n     * [1]: https://www.mongodb.com/docs/manual/reference/operator/query/expr/\n     * [2]: https://www.mongodb.com/docs/manual/reference/operator/query/geoWithin/\n     * [3]: https://www.mongodb.com/docs/manual/reference/operator/query/center/#op._S_center\n     * [4]: https://www.mongodb.com/docs/manual/reference/operator/query/centerSphere/#op._S_centerSphere\n     *\n     * @param filter - The filter for the count\n     * @param options - Optional settings for the command\n     *\n     * @see https://www.mongodb.com/docs/manual/reference/operator/query/expr/\n     * @see https://www.mongodb.com/docs/manual/reference/operator/query/geoWithin/\n     * @see https://www.mongodb.com/docs/manual/reference/operator/query/center/#op._S_center\n     * @see https://www.mongodb.com/docs/manual/reference/operator/query/centerSphere/#op._S_centerSphere\n     */\n    countDocuments(\n        filter?: Document,\n        options?: CountDocumentsOptions,\n    ): Promise<number>\n    /**\n     * The distinct command returns a list of distinct values for the given key across a collection.\n     *\n     * @param key - Field of the document to find distinct values for\n     * @param filter - The filter for filtering the set of documents to which we apply the distinct filter.\n     * @param options - Optional settings for the command\n     */\n    distinct<Key extends keyof WithId<TSchema>>(\n        key: Key,\n    ): Promise<Array<Flatten<WithId<TSchema>[Key]>>>\n    distinct<Key extends keyof WithId<TSchema>>(\n        key: Key,\n        filter: Filter<TSchema>,\n    ): Promise<Array<Flatten<WithId<TSchema>[Key]>>>\n    distinct<Key extends keyof WithId<TSchema>>(\n        key: Key,\n        filter: Filter<TSchema>,\n        options: DistinctOptions,\n    ): Promise<Array<Flatten<WithId<TSchema>[Key]>>>\n    distinct(key: string): Promise<any[]>\n    distinct(key: string, filter: Filter<TSchema>): Promise<any[]>\n    distinct(\n        key: string,\n        filter: Filter<TSchema>,\n        options: DistinctOptions,\n    ): Promise<any[]>\n    /**\n     * Retrieve all the indexes on the collection.\n     *\n     * @param options - Optional settings for the command\n     */\n    indexes(options?: IndexInformationOptions): Promise<Document[]>\n    /**\n     * Get all the collection statistics.\n     *\n     * @param options - Optional settings for the command\n     */\n    stats(options?: CollStatsOptions): Promise<CollStats>\n    /**\n     * Find a document and delete it in one atomic operation. Requires a write lock for the duration of the operation.\n     *\n     * @param filter - The filter used to select the document to remove\n     * @param options - Optional settings for the command\n     */\n    findOneAndDelete(\n        filter: Filter<TSchema>,\n        options?: FindOneAndDeleteOptions,\n    ): Promise<ModifyResult<TSchema> | null>\n    /**\n     * Find a document and replace it in one atomic operation. Requires a write lock for the duration of the operation.\n     *\n     * @param filter - The filter used to select the document to replace\n     * @param replacement - The Document that replaces the matching document\n     * @param options - Optional settings for the command\n     */\n    findOneAndReplace(\n        filter: Filter<TSchema>,\n        replacement: WithoutId<TSchema>,\n        options?: FindOneAndReplaceOptions,\n    ): Promise<ModifyResult<TSchema> | null>\n    /**\n     * Find a document and update it in one atomic operation. Requires a write lock for the duration of the operation.\n     *\n     * @param filter - The filter used to select the document to update\n     * @param update - Update operations to be performed on the document\n     * @param options - Optional settings for the command\n     */\n    findOneAndUpdate(\n        filter: Filter<TSchema>,\n        update: UpdateFilter<TSchema>,\n        options?: FindOneAndUpdateOptions,\n    ): Promise<ModifyResult<TSchema> | null>\n    /**\n     * Execute an aggregation framework pipeline against the collection, needs MongoDB \\>= 2.2\n     *\n     * @param pipeline - An array of aggregation pipelines to execute\n     * @param options - Optional settings for the command\n     */\n    aggregate<T extends Document = Document>(\n        pipeline?: Document[],\n        options?: AggregateOptions,\n    ): AggregationCursor<T>\n    /**\n     * Create a new Change Stream, watching for new changes (insertions, updates, replacements, deletions, and invalidations) in this collection.\n     *\n     * @remarks\n     * watch() accepts two generic arguments for distinct use cases:\n     * - The first is to override the schema that may be defined for this specific collection\n     * - The second is to override the shape of the change stream document entirely, if it is not provided the type will default to ChangeStreamDocument of the first argument\n     * @example\n     * By just providing the first argument I can type the change to be `ChangeStreamDocument<{ _id: number }>`\n     * ```ts\n     * collection.watch<{ _id: number }>()\n     *   .on('change', change => console.log(change._id.toFixed(4)));\n     * ```\n     *\n     * @example\n     * Passing a second argument provides a way to reflect the type changes caused by an advanced pipeline.\n     * Here, we are using a pipeline to have MongoDB filter for insert changes only and add a comment.\n     * No need start from scratch on the ChangeStreamInsertDocument type!\n     * By using an intersection we can save time and ensure defaults remain the same type!\n     * ```ts\n     * collection\n     *   .watch<Schema, ChangeStreamInsertDocument<Schema> & { comment: string }>([\n     *     { $addFields: { comment: 'big changes' } },\n     *     { $match: { operationType: 'insert' } }\n     *   ])\n     *   .on('change', change => {\n     *     change.comment.startsWith('big');\n     *     change.operationType === 'insert';\n     *     // No need to narrow in code because the generics did that for us!\n     *     expectType<Schema>(change.fullDocument);\n     *   });\n     * ```\n     *\n     * @param pipeline - An array of {@link https://www.mongodb.com/docs/manual/reference/operator/aggregation-pipeline/|aggregation pipeline stages} through which to pass change stream documents. This allows for filtering (using $match) and manipulating the change stream documents.\n     * @param options - Optional settings for the command\n     * @typeParam TLocal - Type of the data being detected by the change stream\n     * @typeParam TChange - Type of the whole change stream document emitted\n     */\n    watch<\n        TLocal extends Document = TSchema,\n        TChange extends Document = ChangeStreamDocument<TLocal>,\n    >(\n        pipeline?: Document[],\n        options?: ChangeStreamOptions,\n    ): ChangeStream<TLocal, TChange>\n    /**\n     * Initiate an Out of order batch write operation. All operations will be buffered into insert/update/remove commands executed out of order.\n     *\n     * @throws MongoNotConnectedError\n     * @remarks\n     * **NOTE:** MongoClient must be connected prior to calling this method due to a known limitation in this legacy implementation.\n     * However, `collection.bulkWrite()` provides an equivalent API that does not require prior connecting.\n     */\n    initializeUnorderedBulkOp(\n        options?: BulkWriteOptions,\n    ): UnorderedBulkOperation\n    /**\n     * Initiate an In order bulk write operation. Operations will be serially executed in the order they are added, creating a new operation for each switch in types.\n     *\n     * @throws MongoNotConnectedError\n     * @remarks\n     * **NOTE:** MongoClient must be connected prior to calling this method due to a known limitation in this legacy implementation.\n     * However, `collection.bulkWrite()` provides an equivalent API that does not require prior connecting.\n     */\n    initializeOrderedBulkOp(options?: BulkWriteOptions): OrderedBulkOperation\n    /**\n     * An estimated count of matching documents in the db to a filter.\n     *\n     * **NOTE:** This method has been deprecated, since it does not provide an accurate count of the documents\n     * in a collection. To obtain an accurate count of documents in the collection, use {@link Collection#countDocuments| countDocuments}.\n     * To obtain an estimated count of all documents in the collection, use {@link Collection#estimatedDocumentCount| estimatedDocumentCount}.\n     *\n     * @deprecated use {@link Collection#countDocuments| countDocuments} or {@link Collection#estimatedDocumentCount| estimatedDocumentCount} instead\n     *\n     * @param filter - The filter for the count.\n     * @param options - Optional settings for the command\n     */\n    count(filter?: Filter<TSchema>, options?: CountOptions): Promise<number>\n}\n\n/** @public */\nexport declare interface CollectionInfo extends Document {\n    name: string\n    type?: string\n    options?: Document\n    info?: {\n        readOnly?: false\n        uuid?: Binary\n    }\n    idIndex?: Document\n}\n\n/** @public */\nexport declare interface CollectionOptions\n    extends BSONSerializeOptions,\n        WriteConcernOptions {\n    /** Specify a read concern for the collection. (only MongoDB 3.2 or higher supported) */\n    readConcern?: ReadConcernLike\n    /** The preferred read preference (ReadPreference.PRIMARY, ReadPreference.PRIMARY_PREFERRED, ReadPreference.SECONDARY, ReadPreference.SECONDARY_PREFERRED, ReadPreference.NEAREST). */\n    readPreference?: ReadPreferenceLike\n}\n\n/* Excluded from this release type: CollectionPrivate */\n\n/**\n * @public\n * @see https://www.mongodb.com/docs/manual/reference/command/collStats/\n */\nexport declare interface CollStats extends Document {\n    /** Namespace */\n    ns: string\n    /** Number of documents */\n    count: number\n    /** Collection size in bytes */\n    size: number\n    /** Average object size in bytes */\n    avgObjSize: number\n    /** (Pre)allocated space for the collection in bytes */\n    storageSize: number\n    /** Number of extents (contiguously allocated chunks of datafile space) */\n    numExtents: number\n    /** Number of indexes */\n    nindexes: number\n    /** Size of the most recently created extent in bytes */\n    lastExtentSize: number\n    /** Padding can speed up updates if documents grow */\n    paddingFactor: number\n    /** A number that indicates the user-set flags on the collection. userFlags only appears when using the mmapv1 storage engine */\n    userFlags?: number\n    /** Total index size in bytes */\n    totalIndexSize: number\n    /** Size of specific indexes in bytes */\n    indexSizes: {\n        _id_: number\n        [index: string]: number\n    }\n    /** `true` if the collection is capped */\n    capped: boolean\n    /** The maximum number of documents that may be present in a capped collection */\n    max: number\n    /** The maximum size of a capped collection */\n    maxSize: number\n    /** This document contains data reported directly by the WiredTiger engine and other data for internal diagnostic use */\n    wiredTiger?: WiredTigerData\n    /** The fields in this document are the names of the indexes, while the values themselves are documents that contain statistics for the index provided by the storage engine */\n    indexDetails?: any\n    ok: number\n    /** The amount of storage available for reuse. The scale argument affects this value. */\n    freeStorageSize?: number\n    /** An array that contains the names of the indexes that are currently being built on the collection */\n    indexBuilds?: number\n    /** The sum of the storageSize and totalIndexSize. The scale argument affects this value */\n    totalSize: number\n    /** The scale value used by the command. */\n    scaleFactor: number\n}\n\n/** @public */\nexport declare interface CollStatsOptions extends CommandOperationOptions {\n    /** Divide the returned sizes by scale value. */\n    scale?: number\n}\n\n/**\n * An event indicating the failure of a given command\n * @public\n * @category Event\n */\nexport declare class CommandFailedEvent {\n    address: string\n    connectionId?: string | number\n    requestId: number\n    duration: number\n    commandName: string\n    failure: Error\n    serviceId?: ObjectId\n    /* Excluded from this release type: __constructor */\n    get hasServiceId(): boolean\n}\n\n/* Excluded from this release type: CommandOperation */\n\n/** @public */\nexport declare interface CommandOperationOptions\n    extends OperationOptions,\n        WriteConcernOptions,\n        ExplainOptions {\n    /** Specify a read concern and level for the collection. (only MongoDB 3.2 or higher supported) */\n    readConcern?: ReadConcernLike\n    /** Collation */\n    collation?: CollationOptions\n    maxTimeMS?: number\n    /**\n     * Comment to apply to the operation.\n     *\n     * In server versions pre-4.4, 'comment' must be string.  A server\n     * error will be thrown if any other type is provided.\n     *\n     * In server versions 4.4 and above, 'comment' can be any valid BSON type.\n     */\n    comment?: unknown\n    /** Should retry failed writes */\n    retryWrites?: boolean\n    dbName?: string\n    authdb?: string\n    noResponse?: boolean\n}\n\n/* Excluded from this release type: CommandOptions */\n\n/**\n * An event indicating the start of a given\n * @public\n * @category Event\n */\nexport declare class CommandStartedEvent {\n    commandObj?: Document\n    requestId: number\n    databaseName: string\n    commandName: string\n    command: Document\n    address: string\n    connectionId?: string | number\n    serviceId?: ObjectId\n    /* Excluded from this release type: __constructor */\n    get hasServiceId(): boolean\n}\n\n/**\n * An event indicating the success of a given command\n * @public\n * @category Event\n */\nexport declare class CommandSucceededEvent {\n    address: string\n    connectionId?: string | number\n    requestId: number\n    duration: number\n    commandName: string\n    reply: unknown\n    serviceId?: ObjectId\n    /* Excluded from this release type: __constructor */\n    get hasServiceId(): boolean\n}\n\n/** @public */\nexport declare type CommonEvents = \"newListener\" | \"removeListener\"\n\n/** @public */\nexport declare const Compressor: Readonly<{\n    readonly none: 0\n    readonly snappy: 1\n    readonly zlib: 2\n    readonly zstd: 3\n}>\n\n/** @public */\nexport declare type Compressor = (typeof Compressor)[CompressorName]\n\n/** @public */\nexport declare type CompressorName = keyof typeof Compressor\n\n/** @public */\nexport declare type Condition<T> =\n    | AlternativeType<T>\n    | FilterOperators<AlternativeType<T>>\n\n/* Excluded from this release type: Connection */\n\n/**\n * An event published when a connection is checked into the connection pool\n * @public\n * @category Event\n */\nexport declare class ConnectionCheckedInEvent extends ConnectionPoolMonitoringEvent {\n    /** The id of the connection */\n    connectionId: number | \"<monitor>\"\n    /* Excluded from this release type: __constructor */\n}\n\n/**\n * An event published when a connection is checked out of the connection pool\n * @public\n * @category Event\n */\nexport declare class ConnectionCheckedOutEvent extends ConnectionPoolMonitoringEvent {\n    /** The id of the connection */\n    connectionId: number | \"<monitor>\"\n    /* Excluded from this release type: __constructor */\n}\n\n/**\n * An event published when a request to check a connection out fails\n * @public\n * @category Event\n */\nexport declare class ConnectionCheckOutFailedEvent extends ConnectionPoolMonitoringEvent {\n    /** The reason the attempt to check out failed */\n    reason: AnyError | string\n    /* Excluded from this release type: __constructor */\n}\n\n/**\n * An event published when a request to check a connection out begins\n * @public\n * @category Event\n */\nexport declare class ConnectionCheckOutStartedEvent extends ConnectionPoolMonitoringEvent {\n    /* Excluded from this release type: __constructor */\n}\n\n/**\n * An event published when a connection is closed\n * @public\n * @category Event\n */\nexport declare class ConnectionClosedEvent extends ConnectionPoolMonitoringEvent {\n    /** The id of the connection */\n    connectionId: number | \"<monitor>\"\n    /** The reason the connection was closed */\n    reason: string\n    serviceId?: ObjectId\n    /* Excluded from this release type: __constructor */\n}\n\n/**\n * An event published when a connection pool creates a new connection\n * @public\n * @category Event\n */\nexport declare class ConnectionCreatedEvent extends ConnectionPoolMonitoringEvent {\n    /** A monotonically increasing, per-pool id for the newly created connection */\n    connectionId: number | \"<monitor>\"\n    /* Excluded from this release type: __constructor */\n}\n\n/** @public */\nexport declare type ConnectionEvents = {\n    commandStarted(event: CommandStartedEvent): void\n    commandSucceeded(event: CommandSucceededEvent): void\n    commandFailed(event: CommandFailedEvent): void\n    clusterTimeReceived(clusterTime: Document): void\n    close(): void\n    message(message: any): void\n    pinned(pinType: string): void\n    unpinned(pinType: string): void\n}\n\n/** @public */\nexport declare interface ConnectionOptions\n    extends SupportedNodeConnectionOptions,\n        StreamDescriptionOptions,\n        ProxyOptions {\n    id: number | \"<monitor>\"\n    generation: number\n    hostAddress: HostAddress\n    autoEncrypter?: AutoEncrypter\n    serverApi?: ServerApi\n    monitorCommands: boolean\n    /* Excluded from this release type: connectionType */\n    credentials?: MongoCredentials\n    connectTimeoutMS?: number\n    tls: boolean\n    keepAlive?: boolean\n    keepAliveInitialDelay?: number\n    noDelay?: boolean\n    socketTimeoutMS?: number\n    cancellationToken?: CancellationToken\n    metadata: ClientMetadata\n}\n\n/* Excluded from this release type: ConnectionPool */\n\n/**\n * An event published when a connection pool is cleared\n * @public\n * @category Event\n */\nexport declare class ConnectionPoolClearedEvent extends ConnectionPoolMonitoringEvent {\n    /* Excluded from this release type: serviceId */\n    interruptInUseConnections?: boolean\n    /* Excluded from this release type: __constructor */\n}\n\n/**\n * An event published when a connection pool is closed\n * @public\n * @category Event\n */\nexport declare class ConnectionPoolClosedEvent extends ConnectionPoolMonitoringEvent {\n    /* Excluded from this release type: __constructor */\n}\n\n/**\n * An event published when a connection pool is created\n * @public\n * @category Event\n */\nexport declare class ConnectionPoolCreatedEvent extends ConnectionPoolMonitoringEvent {\n    /** The options used to create this connection pool */\n    options?: ConnectionPoolOptions\n    /* Excluded from this release type: __constructor */\n}\n\n/** @public */\nexport declare type ConnectionPoolEvents = {\n    connectionPoolCreated(event: ConnectionPoolCreatedEvent): void\n    connectionPoolReady(event: ConnectionPoolReadyEvent): void\n    connectionPoolClosed(event: ConnectionPoolClosedEvent): void\n    connectionPoolCleared(event: ConnectionPoolClearedEvent): void\n    connectionCreated(event: ConnectionCreatedEvent): void\n    connectionReady(event: ConnectionReadyEvent): void\n    connectionClosed(event: ConnectionClosedEvent): void\n    connectionCheckOutStarted(event: ConnectionCheckOutStartedEvent): void\n    connectionCheckOutFailed(event: ConnectionCheckOutFailedEvent): void\n    connectionCheckedOut(event: ConnectionCheckedOutEvent): void\n    connectionCheckedIn(event: ConnectionCheckedInEvent): void\n} & Omit<ConnectionEvents, \"close\" | \"message\">\n\n/* Excluded from this release type: ConnectionPoolMetrics */\n\n/**\n * The base export class for all monitoring events published from the connection pool\n * @public\n * @category Event\n */\nexport declare class ConnectionPoolMonitoringEvent {\n    /** A timestamp when the event was created  */\n    time: Date\n    /** The address (host/port pair) of the pool */\n    address: string\n    /* Excluded from this release type: __constructor */\n}\n\n/** @public */\nexport declare interface ConnectionPoolOptions\n    extends Omit<ConnectionOptions, \"id\" | \"generation\"> {\n    /** The maximum number of connections that may be associated with a pool at a given time. This includes in use and available connections. */\n    maxPoolSize: number\n    /** The minimum number of connections that MUST exist at any moment in a single connection pool. */\n    minPoolSize: number\n    /** The maximum number of connections that may be in the process of being established concurrently by the connection pool. */\n    maxConnecting: number\n    /** The maximum amount of time a connection should remain idle in the connection pool before being marked idle. */\n    maxIdleTimeMS: number\n    /** The maximum amount of time operation execution should wait for a connection to become available. The default is 0 which means there is no limit. */\n    waitQueueTimeoutMS: number\n    /** If we are in load balancer mode. */\n    loadBalanced: boolean\n    /* Excluded from this release type: minPoolSizeCheckFrequencyMS */\n}\n\n/**\n * An event published when a connection pool is ready\n * @public\n * @category Event\n */\nexport declare class ConnectionPoolReadyEvent extends ConnectionPoolMonitoringEvent {\n    /* Excluded from this release type: __constructor */\n}\n\n/**\n * An event published when a connection is ready for use\n * @public\n * @category Event\n */\nexport declare class ConnectionReadyEvent extends ConnectionPoolMonitoringEvent {\n    /** The id of the connection */\n    connectionId: number | \"<monitor>\"\n    /* Excluded from this release type: __constructor */\n}\n\n/** @public */\nexport declare interface ConnectOptions {\n    readPreference?: ReadPreference\n}\n\n/** @public */\nexport declare interface CountDocumentsOptions extends AggregateOptions {\n    /** The number of documents to skip. */\n    skip?: number\n    /** The maximum amounts to count before aborting. */\n    limit?: number\n}\n\n/** @public */\nexport declare interface CountOptions extends CommandOperationOptions {\n    /** The number of documents to skip. */\n    skip?: number\n    /** The maximum amounts to count before aborting. */\n    limit?: number\n    /** Number of milliseconds to wait before aborting the query. */\n    maxTimeMS?: number\n    /** An index name hint for the query. */\n    hint?: string | Document\n}\n\n/** @public */\nexport declare interface CreateCollectionOptions\n    extends CommandOperationOptions {\n    /** Returns an error if the collection does not exist */\n    strict?: boolean\n    /** Create a capped collection */\n    capped?: boolean\n    /** @deprecated Create an index on the _id field of the document. This option is deprecated in MongoDB 3.2+ and will be removed once no longer supported by the server. */\n    autoIndexId?: boolean\n    /** The size of the capped collection in bytes */\n    size?: number\n    /** The maximum number of documents in the capped collection */\n    max?: number\n    /** Available for the MMAPv1 storage engine only to set the usePowerOf2Sizes and the noPadding flag */\n    flags?: number\n    /** Allows users to specify configuration to the storage engine on a per-collection basis when creating a collection */\n    storageEngine?: Document\n    /** Allows users to specify validation rules or expressions for the collection. For more information, see Document Validation */\n    validator?: Document\n    /** Determines how strictly MongoDB applies the validation rules to existing documents during an update */\n    validationLevel?: string\n    /** Determines whether to error on invalid documents or just warn about the violations but allow invalid documents to be inserted */\n    validationAction?: string\n    /** Allows users to specify a default configuration for indexes when creating a collection */\n    indexOptionDefaults?: Document\n    /** The name of the source collection or view from which to create the view. The name is not the full namespace of the collection or view (i.e., does not include the database name and implies the same database as the view to create) */\n    viewOn?: string\n    /** An array that consists of the aggregation pipeline stage. Creates the view by applying the specified pipeline to the viewOn collection or view */\n    pipeline?: Document[]\n    /** A primary key factory function for generation of custom _id keys. */\n    pkFactory?: PkFactory\n    /** A document specifying configuration options for timeseries collections. */\n    timeseries?: TimeSeriesCollectionOptions\n    /** A document specifying configuration options for clustered collections. For MongoDB 5.3 and above. */\n    clusteredIndex?: ClusteredCollectionOptions\n    /** The number of seconds after which a document in a timeseries or clustered collection expires. */\n    expireAfterSeconds?: number\n    /** @experimental */\n    encryptedFields?: Document\n    /**\n     * If set, enables pre-update and post-update document events to be included for any\n     * change streams that listen on this collection.\n     */\n    changeStreamPreAndPostImages?: {\n        enabled: boolean\n    }\n}\n\n/** @public */\nexport declare interface CreateIndexesOptions\n    extends Omit<CommandOperationOptions, \"writeConcern\"> {\n    /** Creates the index in the background, yielding whenever possible. */\n    background?: boolean\n    /** Creates an unique index. */\n    unique?: boolean\n    /** Override the autogenerated index name (useful if the resulting name is larger than 128 bytes) */\n    name?: string\n    /** Creates a partial index based on the given filter object (MongoDB 3.2 or higher) */\n    partialFilterExpression?: Document\n    /** Creates a sparse index. */\n    sparse?: boolean\n    /** Allows you to expire data on indexes applied to a data (MongoDB 2.2 or higher) */\n    expireAfterSeconds?: number\n    /** Allows users to configure the storage engine on a per-index basis when creating an index. (MongoDB 3.0 or higher) */\n    storageEngine?: Document\n    /** (MongoDB 4.4. or higher) Specifies how many data-bearing members of a replica set, including the primary, must complete the index builds successfully before the primary marks the indexes as ready. This option accepts the same values for the \"w\" field in a write concern plus \"votingMembers\", which indicates all voting data-bearing nodes. */\n    commitQuorum?: number | string\n    /** Specifies the index version number, either 0 or 1. */\n    version?: number\n    weights?: Document\n    default_language?: string\n    language_override?: string\n    textIndexVersion?: number\n    \"2dsphereIndexVersion\"?: number\n    bits?: number\n    /** For geospatial indexes set the lower bound for the co-ordinates. */\n    min?: number\n    /** For geospatial indexes set the high bound for the co-ordinates. */\n    max?: number\n    bucketSize?: number\n    wildcardProjection?: Document\n    /** Specifies that the index should exist on the target collection but should not be used by the query planner when executing operations. (MongoDB 4.4 or higher) */\n    hidden?: boolean\n}\n\n/** @public */\nexport declare const CURSOR_FLAGS: readonly [\n    \"tailable\",\n    \"oplogReplay\",\n    \"noCursorTimeout\",\n    \"awaitData\",\n    \"exhaust\",\n    \"partial\",\n]\n\n/** @public */\nexport declare type CursorFlag = (typeof CURSOR_FLAGS)[number]\n\n/** @public */\nexport declare interface CursorStreamOptions {\n    /** A transformation method applied to each document emitted by the stream */\n    transform?(this: void, doc: Document): Document\n}\n\n/**\n * The **Db** class is a class that represents a MongoDB Database.\n * @public\n *\n * @example\n * ```ts\n * import { MongoClient } from 'mongodb';\n *\n * interface Pet {\n *   name: string;\n *   kind: 'dog' | 'cat' | 'fish';\n * }\n *\n * const client = new MongoClient('mongodb://localhost:27017');\n * const db = client.db();\n *\n * // Create a collection that validates our union\n * await db.createCollection<Pet>('pets', {\n *   validator: { $expr: { $in: ['$kind', ['dog', 'cat', 'fish']] } }\n * })\n * ```\n */\nexport declare class Db {\n    /* Excluded from this release type: s */\n    static SYSTEM_NAMESPACE_COLLECTION: string\n    static SYSTEM_INDEX_COLLECTION: string\n    static SYSTEM_PROFILE_COLLECTION: string\n    static SYSTEM_USER_COLLECTION: string\n    static SYSTEM_COMMAND_COLLECTION: string\n    static SYSTEM_JS_COLLECTION: string\n    /**\n     * Creates a new Db instance\n     *\n     * @param client - The MongoClient for the database.\n     * @param databaseName - The name of the database this instance represents.\n     * @param options - Optional settings for Db construction\n     */\n    constructor(client: MongoClient, databaseName: string, options?: DbOptions)\n    get databaseName(): string\n    get options(): DbOptions | undefined\n    /**\n     * Check if a secondary can be used (because the read preference is *not* set to primary)\n     */\n    get secondaryOk(): boolean\n    get readConcern(): ReadConcern | undefined\n    /**\n     * The current readPreference of the Db. If not explicitly defined for\n     * this Db, will be inherited from the parent MongoClient\n     */\n    get readPreference(): ReadPreference\n    get bsonOptions(): BSONSerializeOptions\n    get writeConcern(): WriteConcern | undefined\n    get namespace(): string\n    /**\n     * Create a new collection on a server with the specified options. Use this to create capped collections.\n     * More information about command options available at https://www.mongodb.com/docs/manual/reference/command/create/\n     *\n     * @param name - The name of the collection to create\n     * @param options - Optional settings for the command\n     */\n    createCollection<TSchema extends Document = Document>(\n        name: string,\n        options?: CreateCollectionOptions,\n    ): Promise<Collection<TSchema>>\n    /**\n     * Execute a command\n     *\n     * @remarks\n     * This command does not inherit options from the MongoClient.\n     *\n     * @param command - The command to run\n     * @param options - Optional settings for the command\n     */\n    command(command: Document, options?: RunCommandOptions): Promise<Document>\n    /**\n     * Execute an aggregation framework pipeline against the database, needs MongoDB \\>= 3.6\n     *\n     * @param pipeline - An array of aggregation stages to be executed\n     * @param options - Optional settings for the command\n     */\n    aggregate<T extends Document = Document>(\n        pipeline?: Document[],\n        options?: AggregateOptions,\n    ): AggregationCursor<T>\n    /** Return the Admin db instance */\n    admin(): Admin\n    /**\n     * Returns a reference to a MongoDB Collection. If it does not exist it will be created implicitly.\n     *\n     * @param name - the collection name we wish to access.\n     * @returns return the new Collection instance\n     */\n    collection<TSchema extends Document = Document>(\n        name: string,\n        options?: CollectionOptions,\n    ): Collection<TSchema>\n    /**\n     * Get all the db statistics.\n     *\n     * @param options - Optional settings for the command\n     */\n    stats(options?: DbStatsOptions): Promise<Document>\n    /**\n     * List all collections of this database with optional filter\n     *\n     * @param filter - Query to filter collections by\n     * @param options - Optional settings for the command\n     */\n    listCollections(\n        filter: Document,\n        options: Exclude<ListCollectionsOptions, \"nameOnly\"> & {\n            nameOnly: true\n        },\n    ): ListCollectionsCursor<Pick<CollectionInfo, \"name\" | \"type\">>\n    listCollections(\n        filter: Document,\n        options: Exclude<ListCollectionsOptions, \"nameOnly\"> & {\n            nameOnly: false\n        },\n    ): ListCollectionsCursor<CollectionInfo>\n    listCollections<\n        T extends Pick<CollectionInfo, \"name\" | \"type\"> | CollectionInfo =\n            | Pick<CollectionInfo, \"name\" | \"type\">\n            | CollectionInfo,\n    >(\n        filter?: Document,\n        options?: ListCollectionsOptions,\n    ): ListCollectionsCursor<T>\n    /**\n     * Rename a collection.\n     *\n     * @remarks\n     * This operation does not inherit options from the MongoClient.\n     *\n     * @param fromCollection - Name of current collection to rename\n     * @param toCollection - New name of of the collection\n     * @param options - Optional settings for the command\n     */\n    renameCollection<TSchema extends Document = Document>(\n        fromCollection: string,\n        toCollection: string,\n        options?: RenameOptions,\n    ): Promise<Collection<TSchema>>\n    /**\n     * Drop a collection from the database, removing it permanently. New accesses will create a new collection.\n     *\n     * @param name - Name of collection to drop\n     * @param options - Optional settings for the command\n     */\n    dropCollection(\n        name: string,\n        options?: DropCollectionOptions,\n    ): Promise<boolean>\n    /**\n     * Drop a database, removing it permanently from the server.\n     *\n     * @param options - Optional settings for the command\n     */\n    dropDatabase(options?: DropDatabaseOptions): Promise<boolean>\n    /**\n     * Fetch all collections for the current db.\n     *\n     * @param options - Optional settings for the command\n     */\n    collections(options?: ListCollectionsOptions): Promise<Collection[]>\n    /**\n     * Creates an index on the db and collection.\n     *\n     * @param name - Name of the collection to create the index on.\n     * @param indexSpec - Specify the field to index, or an index specification\n     * @param options - Optional settings for the command\n     */\n    createIndex(\n        name: string,\n        indexSpec: IndexSpecification,\n        options?: CreateIndexesOptions,\n    ): Promise<string>\n    /**\n     * Add a user to the database\n     *\n     * @param username - The username for the new user\n     * @param passwordOrOptions - An optional password for the new user, or the options for the command\n     * @param options - Optional settings for the command\n     */\n    addUser(\n        username: string,\n        passwordOrOptions?: string | AddUserOptions,\n        options?: AddUserOptions,\n    ): Promise<Document>\n    /**\n     * Remove a user from a database\n     *\n     * @param username - The username to remove\n     * @param options - Optional settings for the command\n     */\n    removeUser(username: string, options?: RemoveUserOptions): Promise<boolean>\n    /**\n     * Set the current profiling level of MongoDB\n     *\n     * @param level - The new profiling level (off, slow_only, all).\n     * @param options - Optional settings for the command\n     */\n    setProfilingLevel(\n        level: ProfilingLevel,\n        options?: SetProfilingLevelOptions,\n    ): Promise<ProfilingLevel>\n    /**\n     * Retrieve the current profiling Level for MongoDB\n     *\n     * @param options - Optional settings for the command\n     */\n    profilingLevel(options?: ProfilingLevelOptions): Promise<string>\n    /**\n     * Retrieves this collections index info.\n     *\n     * @param name - The name of the collection.\n     * @param options - Optional settings for the command\n     */\n    indexInformation(\n        name: string,\n        options?: IndexInformationOptions,\n    ): Promise<Document>\n    /**\n     * Create a new Change Stream, watching for new changes (insertions, updates,\n     * replacements, deletions, and invalidations) in this database. Will ignore all\n     * changes to system collections.\n     *\n     * @remarks\n     * watch() accepts two generic arguments for distinct use cases:\n     * - The first is to provide the schema that may be defined for all the collections within this database\n     * - The second is to override the shape of the change stream document entirely, if it is not provided the type will default to ChangeStreamDocument of the first argument\n     *\n     * @param pipeline - An array of {@link https://www.mongodb.com/docs/manual/reference/operator/aggregation-pipeline/|aggregation pipeline stages} through which to pass change stream documents. This allows for filtering (using $match) and manipulating the change stream documents.\n     * @param options - Optional settings for the command\n     * @typeParam TSchema - Type of the data being detected by the change stream\n     * @typeParam TChange - Type of the whole change stream document emitted\n     */\n    watch<\n        TSchema extends Document = Document,\n        TChange extends Document = ChangeStreamDocument<TSchema>,\n    >(\n        pipeline?: Document[],\n        options?: ChangeStreamOptions,\n    ): ChangeStream<TSchema, TChange>\n}\n\n/* Excluded from this release type: DB_AGGREGATE_COLLECTION */\n\n/** @public */\nexport declare interface DbOptions\n    extends BSONSerializeOptions,\n        WriteConcernOptions {\n    /** If the database authentication is dependent on another databaseName. */\n    authSource?: string\n    /** Force server to assign _id values instead of driver. */\n    forceServerObjectId?: boolean\n    /** The preferred read preference (ReadPreference.PRIMARY, ReadPreference.PRIMARY_PREFERRED, ReadPreference.SECONDARY, ReadPreference.SECONDARY_PREFERRED, ReadPreference.NEAREST). */\n    readPreference?: ReadPreferenceLike\n    /** A primary key factory object for generation of custom _id keys. */\n    pkFactory?: PkFactory\n    /** Specify a read concern for the collection. (only MongoDB 3.2 or higher supported) */\n    readConcern?: ReadConcern\n    /** Should retry failed writes */\n    retryWrites?: boolean\n}\n\n/* Excluded from this release type: DbPrivate */\nexport { DBRef }\n\n/** @public */\nexport declare interface DbStatsOptions extends CommandOperationOptions {\n    /** Divide the returned sizes by scale value. */\n    scale?: number\n}\n\nexport { Decimal128 }\n\n/** @public */\nexport declare interface DeleteManyModel<TSchema extends Document = Document> {\n    /** The filter to limit the deleted documents. */\n    filter: Filter<TSchema>\n    /** Specifies a collation. */\n    collation?: CollationOptions\n    /** The index to use. If specified, then the query system will only consider plans using the hinted index. */\n    hint?: Hint\n}\n\n/** @public */\nexport declare interface DeleteOneModel<TSchema extends Document = Document> {\n    /** The filter to limit the deleted documents. */\n    filter: Filter<TSchema>\n    /** Specifies a collation. */\n    collation?: CollationOptions\n    /** The index to use. If specified, then the query system will only consider plans using the hinted index. */\n    hint?: Hint\n}\n\n/** @public */\nexport declare interface DeleteOptions\n    extends CommandOperationOptions,\n        WriteConcernOptions {\n    /** If true, when an insert fails, don't execute the remaining writes. If false, continue with remaining inserts when one fails. */\n    ordered?: boolean\n    /** Specifies the collation to use for the operation */\n    collation?: CollationOptions\n    /** Specify that the update query should only consider plans using the hinted index */\n    hint?: string | Document\n    /** Map of parameter names and values that can be accessed using $$var (requires MongoDB 5.0). */\n    let?: Document\n}\n\n/** @public */\nexport declare interface DeleteResult {\n    /** Indicates whether this write result was acknowledged. If not, then all other members of this result will be undefined. */\n    acknowledged: boolean\n    /** The number of documents that were deleted */\n    deletedCount: number\n}\n\n/** @public */\nexport declare interface DeleteStatement {\n    /** The query that matches documents to delete. */\n    q: Document\n    /** The number of matching documents to delete. */\n    limit: number\n    /** Specifies the collation to use for the operation. */\n    collation?: CollationOptions\n    /** A document or string that specifies the index to use to support the query predicate. */\n    hint?: Hint\n}\n\nexport { deserialize }\n\n/* Excluded from this release type: DestroyOptions */\n\n/** @public */\nexport declare type DistinctOptions = CommandOperationOptions\n\nexport { Document }\n\nexport { Double }\n\n/** @public */\nexport declare interface DriverInfo {\n    name?: string\n    version?: string\n    platform?: string\n}\n\n/** @public */\nexport declare interface DropCollectionOptions extends CommandOperationOptions {\n    /** @experimental */\n    encryptedFields?: Document\n}\n\n/** @public */\nexport declare type DropDatabaseOptions = CommandOperationOptions\n\n/** @public */\nexport declare type DropIndexesOptions = CommandOperationOptions\n\n/* Excluded from this release type: Encrypter */\n\n/* Excluded from this release type: EncrypterOptions */\n\n/** @public */\nexport declare interface EndSessionOptions {\n    /* Excluded from this release type: error */\n    force?: boolean\n    forceClear?: boolean\n}\n\n/** TypeScript Omit (Exclude to be specific) does not work for objects with an \"any\" indexed type, and breaks discriminated unions @public */\nexport declare type EnhancedOmit<TRecordOrUnion, KeyUnion> =\n    string extends keyof TRecordOrUnion\n        ? TRecordOrUnion\n        : TRecordOrUnion extends any\n        ? Pick<TRecordOrUnion, Exclude<keyof TRecordOrUnion, KeyUnion>>\n        : never\n\n/** @public */\nexport declare interface ErrorDescription extends Document {\n    message?: string\n    errmsg?: string\n    $err?: string\n    errorLabels?: string[]\n    errInfo?: Document\n}\n\n/** @public */\nexport declare interface EstimatedDocumentCountOptions\n    extends CommandOperationOptions {\n    /**\n     * The maximum amount of time to allow the operation to run.\n     *\n     * This option is sent only if the caller explicitly provides a value. The default is to not send a value.\n     */\n    maxTimeMS?: number\n}\n\n/** @public */\nexport declare interface EvalOptions extends CommandOperationOptions {\n    nolock?: boolean\n}\n\n/** @public */\nexport declare type EventEmitterWithState = {\n    /* Excluded from this release type: stateChanged */\n}\n\n/**\n * Event description type\n * @public\n */\nexport declare type EventsDescription = Record<string, GenericListener>\n\n/* Excluded from this release type: ExecutionResult */\n\n/* Excluded from this release type: Explain */\n\n/** @public */\nexport declare interface ExplainOptions {\n    /** Specifies the verbosity mode for the explain output. */\n    explain?: ExplainVerbosityLike\n}\n\n/** @public */\nexport declare const ExplainVerbosity: Readonly<{\n    readonly queryPlanner: \"queryPlanner\"\n    readonly queryPlannerExtended: \"queryPlannerExtended\"\n    readonly executionStats: \"executionStats\"\n    readonly allPlansExecution: \"allPlansExecution\"\n}>\n\n/** @public */\nexport declare type ExplainVerbosity = string\n\n/**\n * For backwards compatibility, true is interpreted as \"allPlansExecution\"\n * and false as \"queryPlanner\". Prior to server version 3.6, aggregate()\n * ignores the verbosity parameter and executes in \"queryPlanner\".\n * @public\n */\nexport declare type ExplainVerbosityLike = ExplainVerbosity | boolean\n\n/** A MongoDB filter can be some portion of the schema or a set of operators @public */\nexport declare type Filter<TSchema> = {\n    [P in keyof WithId<TSchema>]?: Condition<WithId<TSchema>[P]>\n} & RootFilterOperators<WithId<TSchema>>\n\n/** @public */\nexport declare type FilterOperations<T> = T extends Record<string, any>\n    ? {\n          [key in keyof T]?: FilterOperators<T[key]>\n      }\n    : FilterOperators<T>\n\n/** @public */\nexport declare interface FilterOperators<TValue>\n    extends NonObjectIdLikeDocument {\n    $eq?: TValue\n    $gt?: TValue\n    $gte?: TValue\n    $in?: ReadonlyArray<TValue>\n    $lt?: TValue\n    $lte?: TValue\n    $ne?: TValue\n    $nin?: ReadonlyArray<TValue>\n    $not?: TValue extends string\n        ? FilterOperators<TValue> | RegExp\n        : FilterOperators<TValue>\n    /**\n     * When `true`, `$exists` matches the documents that contain the field,\n     * including documents where the field value is null.\n     */\n    $exists?: boolean\n    $type?: BSONType | BSONTypeAlias\n    $expr?: Record<string, any>\n    $jsonSchema?: Record<string, any>\n    $mod?: TValue extends number ? [number, number] : never\n    $regex?: TValue extends string ? RegExp | BSONRegExp | string : never\n    $options?: TValue extends string ? string : never\n    $geoIntersects?: {\n        $geometry: Document\n    }\n    $geoWithin?: Document\n    $near?: Document\n    $nearSphere?: Document\n    $maxDistance?: number\n    $all?: ReadonlyArray<any>\n    $elemMatch?: Document\n    $size?: TValue extends ReadonlyArray<any> ? number : never\n    $bitsAllClear?: BitwiseFilter\n    $bitsAllSet?: BitwiseFilter\n    $bitsAnyClear?: BitwiseFilter\n    $bitsAnySet?: BitwiseFilter\n    $rand?: Record<string, never>\n}\n\n/** @public */\nexport declare class FindCursor<TSchema = any> extends AbstractCursor<TSchema> {\n    /* Excluded from this release type: [kFilter] */\n    /* Excluded from this release type: [kNumReturned] */\n    /* Excluded from this release type: [kBuiltOptions] */\n    /* Excluded from this release type: __constructor */\n    clone(): FindCursor<TSchema>\n    map<T>(transform: (doc: TSchema) => T): FindCursor<T>\n    /* Excluded from this release type: _initialize */\n    /* Excluded from this release type: _getMore */\n    /**\n     * Get the count of documents for this cursor\n     * @deprecated Use `collection.estimatedDocumentCount` or `collection.countDocuments` instead\n     */\n    count(options?: CountOptions): Promise<number>\n    /** Execute the explain for the cursor */\n    explain(verbosity?: ExplainVerbosityLike): Promise<Document>\n    /** Set the cursor query */\n    filter(filter: Document): this\n    /**\n     * Set the cursor hint\n     *\n     * @param hint - If specified, then the query system will only consider plans using the hinted index.\n     */\n    hint(hint: Hint): this\n    /**\n     * Set the cursor min\n     *\n     * @param min - Specify a $min value to specify the inclusive lower bound for a specific index in order to constrain the results of find(). The $min specifies the lower bound for all keys of a specific index in order.\n     */\n    min(min: Document): this\n    /**\n     * Set the cursor max\n     *\n     * @param max - Specify a $max value to specify the exclusive upper bound for a specific index in order to constrain the results of find(). The $max specifies the upper bound for all keys of a specific index in order.\n     */\n    max(max: Document): this\n    /**\n     * Set the cursor returnKey.\n     * If set to true, modifies the cursor to only return the index field or fields for the results of the query, rather than documents.\n     * If set to true and the query does not use an index to perform the read operation, the returned documents will not contain any fields.\n     *\n     * @param value - the returnKey value.\n     */\n    returnKey(value: boolean): this\n    /**\n     * Modifies the output of a query by adding a field $recordId to matching documents. $recordId is the internal key which uniquely identifies a document in a collection.\n     *\n     * @param value - The $showDiskLoc option has now been deprecated and replaced with the showRecordId field. $showDiskLoc will still be accepted for OP_QUERY stye find.\n     */\n    showRecordId(value: boolean): this\n    /**\n     * Add a query modifier to the cursor query\n     *\n     * @param name - The query modifier (must start with $, such as $orderby etc)\n     * @param value - The modifier value.\n     */\n    addQueryModifier(\n        name: string,\n        value: string | boolean | number | Document,\n    ): this\n    /**\n     * Add a comment to the cursor query allowing for tracking the comment in the log.\n     *\n     * @param value - The comment attached to this query.\n     */\n    comment(value: string): this\n    /**\n     * Set a maxAwaitTimeMS on a tailing cursor query to allow to customize the timeout value for the option awaitData (Only supported on MongoDB 3.2 or higher, ignored otherwise)\n     *\n     * @param value - Number of milliseconds to wait before aborting the tailed query.\n     */\n    maxAwaitTimeMS(value: number): this\n    /**\n     * Set a maxTimeMS on the cursor query, allowing for hard timeout limits on queries (Only supported on MongoDB 2.6 or higher)\n     *\n     * @param value - Number of milliseconds to wait before aborting the query.\n     */\n    maxTimeMS(value: number): this\n    /**\n     * Add a project stage to the aggregation pipeline\n     *\n     * @remarks\n     * In order to strictly type this function you must provide an interface\n     * that represents the effect of your projection on the result documents.\n     *\n     * By default chaining a projection to your cursor changes the returned type to the generic\n     * {@link Document} type.\n     * You should specify a parameterized type to have assertions on your final results.\n     *\n     * @example\n     * ```typescript\n     * // Best way\n     * const docs: FindCursor<{ a: number }> = cursor.project<{ a: number }>({ _id: 0, a: true });\n     * // Flexible way\n     * const docs: FindCursor<Document> = cursor.project({ _id: 0, a: true });\n     * ```\n     *\n     * @remarks\n     *\n     * **Note for Typescript Users:** adding a transform changes the return type of the iteration of this cursor,\n     * it **does not** return a new instance of a cursor. This means when calling project,\n     * you should always assign the result to a new variable in order to get a correctly typed cursor variable.\n     * Take note of the following example:\n     *\n     * @example\n     * ```typescript\n     * const cursor: FindCursor<{ a: number; b: string }> = coll.find();\n     * const projectCursor = cursor.project<{ a: number }>({ _id: 0, a: true });\n     * const aPropOnlyArray: {a: number}[] = await projectCursor.toArray();\n     *\n     * // or always use chaining and save the final cursor\n     *\n     * const cursor = coll.find().project<{ a: string }>({\n     *   _id: 0,\n     *   a: { $convert: { input: '$a', to: 'string' }\n     * }});\n     * ```\n     */\n    project<T extends Document = Document>(value: Document): FindCursor<T>\n    /**\n     * Sets the sort order of the cursor query.\n     *\n     * @param sort - The key or keys set for the sort.\n     * @param direction - The direction of the sorting (1 or -1).\n     */\n    sort(sort: Sort | string, direction?: SortDirection): this\n    /**\n     * Allows disk use for blocking sort operations exceeding 100MB memory. (MongoDB 3.2 or higher)\n     *\n     * @remarks\n     * {@link https://www.mongodb.com/docs/manual/reference/command/find/#find-cmd-allowdiskuse | find command allowDiskUse documentation}\n     */\n    allowDiskUse(allow?: boolean): this\n    /**\n     * Set the collation options for the cursor.\n     *\n     * @param value - The cursor collation options (MongoDB 3.4 or higher) settings for update operation (see 3.4 documentation for available fields).\n     */\n    collation(value: CollationOptions): this\n    /**\n     * Set the limit for the cursor.\n     *\n     * @param value - The limit for the cursor query.\n     */\n    limit(value: number): this\n    /**\n     * Set the skip for the cursor.\n     *\n     * @param value - The skip for the cursor query.\n     */\n    skip(value: number): this\n}\n\n/** @public */\nexport declare interface FindOneAndDeleteOptions\n    extends CommandOperationOptions {\n    /** An optional hint for query optimization. See the {@link https://www.mongodb.com/docs/manual/reference/command/update/#update-command-hint|update command} reference for more information.*/\n    hint?: Document\n    /** Limits the fields to return for all matching documents. */\n    projection?: Document\n    /** Determines which document the operation modifies if the query selects multiple documents. */\n    sort?: Sort\n    /** Map of parameter names and values that can be accessed using $$var (requires MongoDB 5.0). */\n    let?: Document\n}\n\n/** @public */\nexport declare interface FindOneAndReplaceOptions\n    extends CommandOperationOptions {\n    /** Allow driver to bypass schema validation in MongoDB 3.2 or higher. */\n    bypassDocumentValidation?: boolean\n    /** An optional hint for query optimization. See the {@link https://www.mongodb.com/docs/manual/reference/command/update/#update-command-hint|update command} reference for more information.*/\n    hint?: Document\n    /** Limits the fields to return for all matching documents. */\n    projection?: Document\n    /** When set to 'after', returns the updated document rather than the original. The default is 'before'.  */\n    returnDocument?: ReturnDocument\n    /** Determines which document the operation modifies if the query selects multiple documents. */\n    sort?: Sort\n    /** Upsert the document if it does not exist. */\n    upsert?: boolean\n    /** Map of parameter names and values that can be accessed using $$var (requires MongoDB 5.0). */\n    let?: Document\n}\n\n/** @public */\nexport declare interface FindOneAndUpdateOptions\n    extends CommandOperationOptions {\n    /** Optional list of array filters referenced in filtered positional operators */\n    arrayFilters?: Document[]\n    /** Allow driver to bypass schema validation in MongoDB 3.2 or higher. */\n    bypassDocumentValidation?: boolean\n    /** An optional hint for query optimization. See the {@link https://www.mongodb.com/docs/manual/reference/command/update/#update-command-hint|update command} reference for more information.*/\n    hint?: Document\n    /** Limits the fields to return for all matching documents. */\n    projection?: Document\n    /** When set to 'after', returns the updated document rather than the original. The default is 'before'.  */\n    returnDocument?: ReturnDocument\n    /** Determines which document the operation modifies if the query selects multiple documents. */\n    sort?: Sort\n    /** Upsert the document if it does not exist. */\n    upsert?: boolean\n    /** Map of parameter names and values that can be accessed using $$var (requires MongoDB 5.0). */\n    let?: Document\n}\n\n/**\n * A builder object that is returned from {@link BulkOperationBase#find}.\n * Is used to build a write operation that involves a query filter.\n *\n * @public\n */\nexport declare class FindOperators {\n    bulkOperation: BulkOperationBase\n    /* Excluded from this release type: __constructor */\n    /** Add a multiple update operation to the bulk operation */\n    update(updateDocument: Document | Document[]): BulkOperationBase\n    /** Add a single update operation to the bulk operation */\n    updateOne(updateDocument: Document | Document[]): BulkOperationBase\n    /** Add a replace one operation to the bulk operation */\n    replaceOne(replacement: Document): BulkOperationBase\n    /** Add a delete one operation to the bulk operation */\n    deleteOne(): BulkOperationBase\n    /** Add a delete many operation to the bulk operation */\n    delete(): BulkOperationBase\n    /** Upsert modifier for update bulk operation, noting that this operation is an upsert. */\n    upsert(): this\n    /** Specifies the collation for the query condition. */\n    collation(collation: CollationOptions): this\n    /** Specifies arrayFilters for UpdateOne or UpdateMany bulk operations. */\n    arrayFilters(arrayFilters: Document[]): this\n    /** Specifies hint for the bulk operation. */\n    hint(hint: Hint): this\n}\n\n/**\n * @public\n * @typeParam TSchema - Unused schema definition, deprecated usage, only specify `FindOptions` with no generic\n */\nexport declare interface FindOptions<TSchema extends Document = Document>\n    extends Omit<CommandOperationOptions, \"writeConcern\"> {\n    /** Sets the limit of documents returned in the query. */\n    limit?: number\n    /** Set to sort the documents coming back from the query. Array of indexes, `[['a', 1]]` etc. */\n    sort?: Sort\n    /** The fields to return in the query. Object of fields to either include or exclude (one of, not both), `{'a':1, 'b': 1}` **or** `{'a': 0, 'b': 0}` */\n    projection?: Document\n    /** Set to skip N documents ahead in your query (useful for pagination). */\n    skip?: number\n    /** Tell the query to use specific indexes in the query. Object of indexes to use, `{'_id':1}` */\n    hint?: Hint\n    /** Specify if the cursor can timeout. */\n    timeout?: boolean\n    /** Specify if the cursor is tailable. */\n    tailable?: boolean\n    /** Specify if the cursor is a tailable-await cursor. Requires `tailable` to be true */\n    awaitData?: boolean\n    /** Set the batchSize for the getMoreCommand when iterating over the query results. */\n    batchSize?: number\n    /** If true, returns only the index keys in the resulting documents. */\n    returnKey?: boolean\n    /** The inclusive lower bound for a specific index */\n    min?: Document\n    /** The exclusive upper bound for a specific index */\n    max?: Document\n    /** Number of milliseconds to wait before aborting the query. */\n    maxTimeMS?: number\n    /** The maximum amount of time for the server to wait on new documents to satisfy a tailable cursor query. Requires `tailable` and `awaitData` to be true */\n    maxAwaitTimeMS?: number\n    /** The server normally times out idle cursors after an inactivity period (10 minutes) to prevent excess memory use. Set this option to prevent that. */\n    noCursorTimeout?: boolean\n    /** Specify collation (MongoDB 3.4 or higher) settings for update operation (see 3.4 documentation for available fields). */\n    collation?: CollationOptions\n    /** Allows disk use for blocking sort operations exceeding 100MB memory. (MongoDB 3.2 or higher) */\n    allowDiskUse?: boolean\n    /** Determines whether to close the cursor after the first batch. Defaults to false. */\n    singleBatch?: boolean\n    /** For queries against a sharded collection, allows the command (or subsequent getMore commands) to return partial results, rather than an error, if one or more queried shards are unavailable. */\n    allowPartialResults?: boolean\n    /** Determines whether to return the record identifier for each document. If true, adds a field $recordId to the returned documents. */\n    showRecordId?: boolean\n    /** Map of parameter names and values that can be accessed using $$var (requires MongoDB 5.0). */\n    let?: Document\n    /**\n     * Option to enable an optimized code path for queries looking for a particular range of `ts` values in the oplog. Requires `tailable` to be true.\n     * @deprecated Starting from MongoDB 4.4 this flag is not needed and will be ignored.\n     */\n    oplogReplay?: boolean\n}\n\n/** @public */\nexport declare type Flatten<Type> = Type extends ReadonlyArray<infer Item>\n    ? Item\n    : Type\n\n/** @public */\nexport declare type GenericListener = (...args: any[]) => void\n\n/**\n * Constructor for a streaming GridFS interface\n * @public\n */\nexport declare class GridFSBucket extends TypedEventEmitter<GridFSBucketEvents> {\n    /* Excluded from this release type: s */\n    /**\n     * When the first call to openUploadStream is made, the upload stream will\n     * check to see if it needs to create the proper indexes on the chunks and\n     * files collections. This event is fired either when 1) it determines that\n     * no index creation is necessary, 2) when it successfully creates the\n     * necessary indexes.\n     * @event\n     */\n    static readonly INDEX: \"index\"\n    constructor(db: Db, options?: GridFSBucketOptions)\n    /**\n     * Returns a writable stream (GridFSBucketWriteStream) for writing\n     * buffers to GridFS. The stream's 'id' property contains the resulting\n     * file's id.\n     *\n     * @param filename - The value of the 'filename' key in the files doc\n     * @param options - Optional settings.\n     */\n    openUploadStream(\n        filename: string,\n        options?: GridFSBucketWriteStreamOptions,\n    ): GridFSBucketWriteStream\n    /**\n     * Returns a writable stream (GridFSBucketWriteStream) for writing\n     * buffers to GridFS for a custom file id. The stream's 'id' property contains the resulting\n     * file's id.\n     */\n    openUploadStreamWithId(\n        id: ObjectId,\n        filename: string,\n        options?: GridFSBucketWriteStreamOptions,\n    ): GridFSBucketWriteStream\n    /** Returns a readable stream (GridFSBucketReadStream) for streaming file data from GridFS. */\n    openDownloadStream(\n        id: ObjectId,\n        options?: GridFSBucketReadStreamOptions,\n    ): GridFSBucketReadStream\n    /**\n     * Deletes a file with the given id\n     *\n     * @param id - The id of the file doc\n     */\n    delete(id: ObjectId): Promise<void>\n    /** Convenience wrapper around find on the files collection */\n    find(\n        filter?: Filter<GridFSFile>,\n        options?: FindOptions,\n    ): FindCursor<GridFSFile>\n    /**\n     * Returns a readable stream (GridFSBucketReadStream) for streaming the\n     * file with the given name from GridFS. If there are multiple files with\n     * the same name, this will stream the most recent file with the given name\n     * (as determined by the `uploadDate` field). You can set the `revision`\n     * option to change this behavior.\n     */\n    openDownloadStreamByName(\n        filename: string,\n        options?: GridFSBucketReadStreamOptionsWithRevision,\n    ): GridFSBucketReadStream\n    /**\n     * Renames the file with the given _id to the given string\n     *\n     * @param id - the id of the file to rename\n     * @param filename - new name for the file\n     */\n    rename(id: ObjectId, filename: string): Promise<void>\n    /** Removes this bucket's files collection, followed by its chunks collection. */\n    drop(): Promise<void>\n}\n\n/** @public */\nexport declare type GridFSBucketEvents = {\n    index(): void\n}\n\n/** @public */\nexport declare interface GridFSBucketOptions extends WriteConcernOptions {\n    /** The 'files' and 'chunks' collections will be prefixed with the bucket name followed by a dot. */\n    bucketName?: string\n    /** Number of bytes stored in each chunk. Defaults to 255KB */\n    chunkSizeBytes?: number\n    /** Read preference to be passed to read operations */\n    readPreference?: ReadPreference\n}\n\n/* Excluded from this release type: GridFSBucketPrivate */\n\n/**\n * A readable stream that enables you to read buffers from GridFS.\n *\n * Do not instantiate this class directly. Use `openDownloadStream()` instead.\n * @public\n */\nexport declare class GridFSBucketReadStream\n    extends Readable\n    implements NodeJS.ReadableStream\n{\n    /* Excluded from this release type: s */\n    /**\n     * An error occurred\n     * @event\n     */\n    static readonly ERROR: \"error\"\n    /**\n     * Fires when the stream loaded the file document corresponding to the provided id.\n     * @event\n     */\n    static readonly FILE: \"file\"\n    /**\n     * Emitted when a chunk of data is available to be consumed.\n     * @event\n     */\n    static readonly DATA: \"data\"\n    /**\n     * Fired when the stream is exhausted (no more data events).\n     * @event\n     */\n    static readonly END: \"end\"\n    /**\n     * Fired when the stream is exhausted and the underlying cursor is killed\n     * @event\n     */\n    static readonly CLOSE: \"close\"\n    /* Excluded from this release type: __constructor */\n    /* Excluded from this release type: _read */\n    /**\n     * Sets the 0-based offset in bytes to start streaming from. Throws\n     * an error if this stream has entered flowing mode\n     * (e.g. if you've already called `on('data')`)\n     *\n     * @param start - 0-based offset in bytes to start streaming from\n     */\n    start(start?: number): this\n    /**\n     * Sets the 0-based offset in bytes to start streaming from. Throws\n     * an error if this stream has entered flowing mode\n     * (e.g. if you've already called `on('data')`)\n     *\n     * @param end - Offset in bytes to stop reading at\n     */\n    end(end?: number): this\n    /**\n     * Marks this stream as aborted (will never push another `data` event)\n     * and kills the underlying cursor. Will emit the 'end' event, and then\n     * the 'close' event once the cursor is successfully killed.\n     */\n    abort(): Promise<void>\n}\n\n/** @public */\nexport declare interface GridFSBucketReadStreamOptions {\n    sort?: Sort\n    skip?: number\n    /**\n     * 0-indexed non-negative byte offset from the beginning of the file\n     */\n    start?: number\n    /**\n     * 0-indexed non-negative byte offset to the end of the file contents\n     * to be returned by the stream. `end` is non-inclusive\n     */\n    end?: number\n}\n\n/** @public */\nexport declare interface GridFSBucketReadStreamOptionsWithRevision\n    extends GridFSBucketReadStreamOptions {\n    /** The revision number relative to the oldest file with the given filename. 0\n     * gets you the oldest file, 1 gets you the 2nd oldest, -1 gets you the\n     * newest. */\n    revision?: number\n}\n\n/* Excluded from this release type: GridFSBucketReadStreamPrivate */\n\n/**\n * A writable stream that enables you to write buffers to GridFS.\n *\n * Do not instantiate this class directly. Use `openUploadStream()` instead.\n * @public\n */\nexport declare class GridFSBucketWriteStream {\n    /*\n    `implements NodeJS.WritableStream`\n    Has to be removed, otherwise tsc places a `/// <reference types=\"vinyl-fs\" />` in the output file,\n    because vinyl-fs messed with NodeJS.WritableStream in the global scope:\n    https://github.com/DefinitelyTyped/DefinitelyTyped/blob/45372bb8a679f310f65765321ed38399055dcf1b/types/vinyl-fs/v1/index.d.ts#L9 */\n\n    bucket: GridFSBucket\n    chunks: Collection<GridFSChunk>\n    filename: string\n    files: Collection<GridFSFile>\n    options: GridFSBucketWriteStreamOptions\n    done: boolean\n    id: ObjectId\n    chunkSizeBytes: number\n    bufToStore: Buffer\n    length: number\n    n: number\n    pos: number\n    state: {\n        streamEnd: boolean\n        outstandingRequests: number\n        errored: boolean\n        aborted: boolean\n    }\n    writeConcern?: WriteConcern\n    /** @event */\n    static readonly CLOSE = \"close\"\n    /** @event */\n    static readonly ERROR = \"error\"\n    /**\n     * `end()` was called and the write stream successfully wrote the file metadata and all the chunks to MongoDB.\n     * @event\n     */\n    static readonly FINISH = \"finish\"\n    /* Excluded from this release type: __constructor */\n    /**\n     * Write a buffer to the stream.\n     *\n     * @param chunk - Buffer to write\n     * @param encodingOrCallback - Optional encoding for the buffer\n     * @param callback - Function to call when the chunk was added to the buffer, or if the entire chunk was persisted to MongoDB if this chunk caused a flush.\n     * @returns False if this write required flushing a chunk to MongoDB. True otherwise.\n     */\n    write(chunk: Buffer | string): boolean\n    write(chunk: Buffer | string, callback: Callback<void>): boolean\n    write(chunk: Buffer | string, encoding: BufferEncoding | undefined): boolean\n    write(\n        chunk: Buffer | string,\n        encoding: BufferEncoding | undefined,\n        callback: Callback<void>,\n    ): boolean\n    /**\n     * Places this write stream into an aborted state (all future writes fail)\n     * and deletes all chunks that have already been written.\n     */\n    abort(): Promise<void>\n    /**\n     * Tells the stream that no more data will be coming in. The stream will\n     * persist the remaining data to MongoDB, write the files document, and\n     * then emit a 'finish' event.\n     *\n     * @param chunk - Buffer to write\n     * @param encoding - Optional encoding for the buffer\n     * @param callback - Function to call when all files and chunks have been persisted to MongoDB\n     */\n    end(): this\n    end(chunk: Buffer): this\n    end(callback: Callback<GridFSFile | void>): this\n    end(chunk: Buffer, callback: Callback<GridFSFile | void>): this\n    end(chunk: Buffer, encoding: BufferEncoding): this\n    end(\n        chunk: Buffer,\n        encoding: BufferEncoding | undefined,\n        callback: Callback<GridFSFile | void>,\n    ): this\n}\n\n/** @public */\nexport declare interface GridFSBucketWriteStreamOptions\n    extends WriteConcernOptions {\n    /** Overwrite this bucket's chunkSizeBytes for this file */\n    chunkSizeBytes?: number\n    /** Custom file id for the GridFS file. */\n    id?: ObjectId\n    /** Object to store in the file document's `metadata` field */\n    metadata?: Document\n    /** String to store in the file document's `contentType` field */\n    contentType?: string\n    /** Array of strings to store in the file document's `aliases` field */\n    aliases?: string[]\n}\n\n/** @public */\nexport declare interface GridFSChunk {\n    _id: ObjectId\n    files_id: ObjectId\n    n: number\n    data: Buffer | Uint8Array\n}\n\n/** @public */\nexport declare interface GridFSFile {\n    _id: ObjectId\n    length: number\n    chunkSize: number\n    filename: string\n    contentType?: string\n    aliases?: string[]\n    metadata?: Document\n    uploadDate: Date\n}\n\n/** @public */\nexport declare const GSSAPICanonicalizationValue: Readonly<{\n    readonly on: true\n    readonly off: false\n    readonly none: \"none\"\n    readonly forward: \"forward\"\n    readonly forwardAndReverse: \"forwardAndReverse\"\n}>\n\n/** @public */\nexport declare type GSSAPICanonicalizationValue =\n    (typeof GSSAPICanonicalizationValue)[keyof typeof GSSAPICanonicalizationValue]\n\n/** @public */\nexport declare interface HedgeOptions {\n    /** Explicitly enable or disable hedged reads. */\n    enabled?: boolean\n}\n\n/** @public */\nexport declare type Hint = string | Document\n\n/** @public */\nexport declare class HostAddress {\n    host: string | undefined\n    port: number | undefined\n    socketPath: string | undefined\n    isIPv6: boolean\n    constructor(hostString: string)\n    inspect(): string\n    toString(): string\n    static fromString(this: void, s: string): HostAddress\n    static fromHostPort(host: string, port: number): HostAddress\n    static fromSrvRecord({ name, port }: SrvRecord): HostAddress\n}\n\n/** @public */\nexport declare interface IndexDescription\n    extends Pick<\n        CreateIndexesOptions,\n        | \"background\"\n        | \"unique\"\n        | \"partialFilterExpression\"\n        | \"sparse\"\n        | \"hidden\"\n        | \"expireAfterSeconds\"\n        | \"storageEngine\"\n        | \"version\"\n        | \"weights\"\n        | \"default_language\"\n        | \"language_override\"\n        | \"textIndexVersion\"\n        | \"2dsphereIndexVersion\"\n        | \"bits\"\n        | \"min\"\n        | \"max\"\n        | \"bucketSize\"\n        | \"wildcardProjection\"\n    > {\n    collation?: CollationOptions\n    name?: string\n    key:\n        | {\n              [key: string]: IndexDirection\n          }\n        | Map<string, IndexDirection>\n}\n\n/** @public */\nexport declare type IndexDirection =\n    | -1\n    | 1\n    | \"2d\"\n    | \"2dsphere\"\n    | \"text\"\n    | \"geoHaystack\"\n    | \"hashed\"\n    | number\n\n/** @public */\nexport declare interface IndexInformationOptions {\n    full?: boolean\n    readPreference?: ReadPreference\n    session?: ClientSession\n}\n\n/** @public */\nexport declare type IndexSpecification = OneOrMore<\n    | string\n    | [string, IndexDirection]\n    | {\n          [key: string]: IndexDirection\n      }\n    | Map<string, IndexDirection>\n>\n\n/** Given an object shaped type, return the type of the _id field or default to ObjectId @public */\nexport declare type InferIdType<TSchema> = TSchema extends {\n    _id: infer IdType\n}\n    ? Record<any, never> extends IdType\n        ? never\n        : IdType\n    : TSchema extends {\n          _id?: infer IdType\n      }\n    ? unknown extends IdType\n        ? ObjectId\n        : IdType\n    : ObjectId\n\n/** @public */\nexport declare interface InsertManyResult<TSchema = Document> {\n    /** Indicates whether this write result was acknowledged. If not, then all other members of this result will be undefined */\n    acknowledged: boolean\n    /** The number of inserted documents for this operations */\n    insertedCount: number\n    /** Map of the index of the inserted document to the id of the inserted document */\n    insertedIds: {\n        [key: number]: InferIdType<TSchema>\n    }\n}\n\n/** @public */\nexport declare interface InsertOneModel<TSchema extends Document = Document> {\n    /** The document to insert. */\n    document: OptionalId<TSchema>\n}\n\n/** @public */\nexport declare interface InsertOneOptions extends CommandOperationOptions {\n    /** Allow driver to bypass schema validation in MongoDB 3.2 or higher. */\n    bypassDocumentValidation?: boolean\n    /** Force server to assign _id values instead of driver. */\n    forceServerObjectId?: boolean\n}\n\n/** @public */\nexport declare interface InsertOneResult<TSchema = Document> {\n    /** Indicates whether this write result was acknowledged. If not, then all other members of this result will be undefined */\n    acknowledged: boolean\n    /** The identifier that was inserted. If the server generated the identifier, this value will be null as the driver does not have access to that data */\n    insertedId: InferIdType<TSchema>\n}\n\nexport { Int32 }\n\n/** @public */\nexport declare type IntegerType = number | Int32 | Long | bigint\n\n/* Excluded from this release type: InternalAbstractCursorOptions */\n\n/** @public */\nexport declare type IsAny<Type, ResultIfAny, ResultIfNotAny> =\n    true extends false & Type ? ResultIfAny : ResultIfNotAny\n\n/**\n * Helper types for dot-notation filter attributes\n */\n/** @public */\nexport declare type Join<T extends unknown[], D extends string> = T extends []\n    ? \"\"\n    : T extends [string | number]\n    ? `${T[0]}`\n    : T extends [string | number, ...infer R]\n    ? `${T[0]}${D}${Join<R, D>}`\n    : string\n\n/* Excluded from this release type: kBeforeHandshake */\n\n/* Excluded from this release type: kBuffer */\n\n/* Excluded from this release type: kBuiltOptions */\n\n/* Excluded from this release type: kCancellationToken */\n\n/* Excluded from this release type: kCancellationToken_2 */\n\n/* Excluded from this release type: kCancelled */\n\n/* Excluded from this release type: kCancelled_2 */\n\n/* Excluded from this release type: kCheckedOut */\n\n/* Excluded from this release type: kClient */\n\n/* Excluded from this release type: kClosed */\n\n/* Excluded from this release type: kClosed_2 */\n\n/* Excluded from this release type: kClusterTime */\n\n/* Excluded from this release type: kConnection */\n\n/* Excluded from this release type: kConnectionCounter */\n\n/* Excluded from this release type: kConnections */\n\n/* Excluded from this release type: kCursorStream */\n\n/* Excluded from this release type: kDelayedTimeoutId */\n\n/* Excluded from this release type: kDescription */\n\n/* Excluded from this release type: kDocuments */\n\n/* Excluded from this release type: kErrorLabels */\n\n/** @public */\nexport declare type KeysOfAType<TSchema, Type> = {\n    [key in keyof TSchema]: NonNullable<TSchema[key]> extends Type ? key : never\n}[keyof TSchema]\n\n/** @public */\nexport declare type KeysOfOtherType<TSchema, Type> = {\n    [key in keyof TSchema]: NonNullable<TSchema[key]> extends Type ? never : key\n}[keyof TSchema]\n\n/* Excluded from this release type: kFilter */\n\n/* Excluded from this release type: kGeneration */\n\n/* Excluded from this release type: kGeneration_2 */\n\n/* Excluded from this release type: kHello */\n\n/* Excluded from this release type: kId */\n\n/* Excluded from this release type: kInit */\n\n/* Excluded from this release type: kInitialized */\n\n/* Excluded from this release type: kInternalClient */\n\n/* Excluded from this release type: kKilled */\n\n/* Excluded from this release type: kLastUseTime */\n\n/* Excluded from this release type: kMessageStream */\n\n/* Excluded from this release type: kMetrics */\n\n/* Excluded from this release type: kMinPoolSizeTimer */\n\n/* Excluded from this release type: kMode */\n\n/* Excluded from this release type: kMonitor */\n\n/* Excluded from this release type: kMonitorId */\n\n/* Excluded from this release type: kNamespace */\n\n/* Excluded from this release type: kNumReturned */\n\n/* Excluded from this release type: kOptions */\n\n/* Excluded from this release type: kOptions_2 */\n\n/* Excluded from this release type: kOptions_3 */\n\n/* Excluded from this release type: kPending */\n\n/* Excluded from this release type: kPinnedConnection */\n\n/* Excluded from this release type: kPipeline */\n\n/* Excluded from this release type: kPoolState */\n\n/* Excluded from this release type: kProcessingWaitQueue */\n\n/* Excluded from this release type: kQueue */\n\n/* Excluded from this release type: kRoundTripTime */\n\n/* Excluded from this release type: kRTTPinger */\n\n/* Excluded from this release type: kServer */\n\n/* Excluded from this release type: kServer_2 */\n\n/* Excluded from this release type: kServer_3 */\n\n/* Excluded from this release type: kServerError */\n\n/* Excluded from this release type: kServerSession */\n\n/* Excluded from this release type: kServiceGenerations */\n\n/* Excluded from this release type: kSession */\n\n/* Excluded from this release type: kSession_2 */\n\n/* Excluded from this release type: kSnapshotEnabled */\n\n/* Excluded from this release type: kSnapshotTime */\n\n/* Excluded from this release type: kStream */\n\n/* Excluded from this release type: kTransform */\n\n/* Excluded from this release type: kTxnNumberIncrement */\n\n/* Excluded from this release type: kWaitQueue */\n\n/* Excluded from this release type: kWaitQueue_2 */\n\n/** @public */\nexport declare const LEGAL_TCP_SOCKET_OPTIONS: readonly [\n    \"family\",\n    \"hints\",\n    \"localAddress\",\n    \"localPort\",\n    \"lookup\",\n]\n\n/** @public */\nexport declare const LEGAL_TLS_SOCKET_OPTIONS: readonly [\n    \"ALPNProtocols\",\n    \"ca\",\n    \"cert\",\n    \"checkServerIdentity\",\n    \"ciphers\",\n    \"crl\",\n    \"ecdhCurve\",\n    \"key\",\n    \"minDHSize\",\n    \"passphrase\",\n    \"pfx\",\n    \"rejectUnauthorized\",\n    \"secureContext\",\n    \"secureProtocol\",\n    \"servername\",\n    \"session\",\n]\n\n/* Excluded from this release type: List */\n\n/** @public */\nexport declare class ListCollectionsCursor<\n    T extends Pick<CollectionInfo, \"name\" | \"type\"> | CollectionInfo =\n        | Pick<CollectionInfo, \"name\" | \"type\">\n        | CollectionInfo,\n> extends AbstractCursor<T> {\n    parent: Db\n    filter: Document\n    options?: ListCollectionsOptions\n    constructor(db: Db, filter: Document, options?: ListCollectionsOptions)\n    clone(): ListCollectionsCursor<T>\n    /* Excluded from this release type: _initialize */\n}\n\n/** @public */\nexport declare interface ListCollectionsOptions\n    extends Omit<CommandOperationOptions, \"writeConcern\"> {\n    /** Since 4.0: If true, will only return the collection name in the response, and will omit additional info */\n    nameOnly?: boolean\n    /** Since 4.0: If true and nameOnly is true, allows a user without the required privilege (i.e. listCollections action on the database) to run the command when access control is enforced. */\n    authorizedCollections?: boolean\n    /** The batchSize for the returned command cursor or if pre 2.8 the systems batch collection */\n    batchSize?: number\n}\n\n/** @public */\nexport declare interface ListDatabasesOptions extends CommandOperationOptions {\n    /** A query predicate that determines which databases are listed */\n    filter?: Document\n    /** A flag to indicate whether the command should return just the database names, or return both database names and size information */\n    nameOnly?: boolean\n    /** A flag that determines which databases are returned based on the user privileges when access control is enabled */\n    authorizedDatabases?: boolean\n}\n\n/** @public */\nexport declare interface ListDatabasesResult {\n    databases: ({\n        name: string\n        sizeOnDisk?: number\n        empty?: boolean\n    } & Document)[]\n    totalSize?: number\n    totalSizeMb?: number\n    ok: 1 | 0\n}\n\n/** @public */\nexport declare class ListIndexesCursor extends AbstractCursor {\n    parent: Collection\n    options?: ListIndexesOptions\n    constructor(collection: Collection, options?: ListIndexesOptions)\n    clone(): ListIndexesCursor\n    /* Excluded from this release type: _initialize */\n}\n\n/** @public */\nexport declare interface ListIndexesOptions\n    extends Omit<CommandOperationOptions, \"writeConcern\"> {\n    /** The batchSize for the returned command cursor or if pre 2.8 the systems batch collection */\n    batchSize?: number\n}\n\nexport { Long }\n\n/** @public */\nexport declare type MatchKeysAndValues<TSchema> = Readonly<Partial<TSchema>> &\n    Record<string, any>\n\nexport { MaxKey }\n\n/* Excluded from this release type: MessageHeader */\n\n/* Excluded from this release type: MessageStream */\n\n/* Excluded from this release type: MessageStreamOptions */\nexport { MinKey }\n\n/**\n * @public\n * @deprecated This type will be completely removed and findOneAndUpdate,\n *             findOneAndDelete, and findOneAndReplace will then return the\n *             actual result document.\n */\nexport declare interface ModifyResult<TSchema = Document> {\n    value: WithId<TSchema> | null\n    lastErrorObject?: Document\n    ok: 0 | 1\n}\n\n/** @public */\nexport declare const MONGO_CLIENT_EVENTS: readonly [\n    \"connectionPoolCreated\",\n    \"connectionPoolReady\",\n    \"connectionPoolCleared\",\n    \"connectionPoolClosed\",\n    \"connectionCreated\",\n    \"connectionReady\",\n    \"connectionClosed\",\n    \"connectionCheckOutStarted\",\n    \"connectionCheckOutFailed\",\n    \"connectionCheckedOut\",\n    \"connectionCheckedIn\",\n    \"commandStarted\",\n    \"commandSucceeded\",\n    \"commandFailed\",\n    \"serverOpening\",\n    \"serverClosed\",\n    \"serverDescriptionChanged\",\n    \"topologyOpening\",\n    \"topologyClosed\",\n    \"topologyDescriptionChanged\",\n    \"error\",\n    \"timeout\",\n    \"close\",\n    \"serverHeartbeatStarted\",\n    \"serverHeartbeatSucceeded\",\n    \"serverHeartbeatFailed\",\n]\n\n/**\n * An error generated when the driver API is used incorrectly\n *\n * @privateRemarks\n * Should **never** be directly instantiated\n *\n * @public\n * @category Error\n */\nexport declare class MongoAPIError extends MongoDriverError {\n    constructor(message: string)\n    get name(): string\n}\n\n/**\n * A error generated when the user attempts to authenticate\n * via AWS, but fails\n *\n * @public\n * @category Error\n */\nexport declare class MongoAWSError extends MongoRuntimeError {\n    constructor(message: string)\n    get name(): string\n}\n\n/**\n * An error generated when a batch command is re-executed after one of the commands in the batch\n * has failed\n *\n * @public\n * @category Error\n */\nexport declare class MongoBatchReExecutionError extends MongoAPIError {\n    constructor(message?: string)\n    get name(): string\n}\n\n/**\n * An error indicating an unsuccessful Bulk Write\n * @public\n * @category Error\n */\nexport declare class MongoBulkWriteError extends MongoServerError {\n    result: BulkWriteResult\n    writeErrors: OneOrMore<WriteError>\n    err?: WriteConcernError\n    /** Creates a new MongoBulkWriteError */\n    constructor(\n        error:\n            | {\n                  message: string\n                  code: number\n                  writeErrors?: WriteError[]\n              }\n            | WriteConcernError\n            | AnyError,\n        result: BulkWriteResult,\n    )\n    get name(): string\n    /** Number of documents inserted. */\n    get insertedCount(): number\n    /** Number of documents matched for update. */\n    get matchedCount(): number\n    /** Number of documents modified. */\n    get modifiedCount(): number\n    /** Number of documents deleted. */\n    get deletedCount(): number\n    /** Number of documents upserted. */\n    get upsertedCount(): number\n    /** Inserted document generated Id's, hash key is the index of the originating operation */\n    get insertedIds(): {\n        [key: number]: any\n    }\n    /** Upserted document generated Id's, hash key is the index of the originating operation */\n    get upsertedIds(): {\n        [key: number]: any\n    }\n}\n\n/**\n * An error generated when a ChangeStream operation fails to execute.\n *\n * @public\n * @category Error\n */\nexport declare class MongoChangeStreamError extends MongoRuntimeError {\n    constructor(message: string)\n    get name(): string\n}\n\n/**\n * The **MongoClient** class is a class that allows for making Connections to MongoDB.\n * @public\n *\n * @remarks\n * The programmatically provided options take precedence over the URI options.\n *\n * @example\n * ```ts\n * import { MongoClient } from 'mongodb';\n *\n * // Enable command monitoring for debugging\n * const client = new MongoClient('mongodb://localhost:27017', { monitorCommands: true });\n *\n * client.on('commandStarted', started => console.log(started));\n * client.db().collection('pets');\n * await client.insertOne({ name: 'spot', kind: 'dog' });\n * ```\n */\nexport declare class MongoClient extends TypedEventEmitter<MongoClientEvents> {\n    /* Excluded from this release type: s */\n    /* Excluded from this release type: topology */\n    /* Excluded from this release type: mongoLogger */\n    /* Excluded from this release type: connectionLock */\n    /* Excluded from this release type: [kOptions] */\n    constructor(url: string, options?: MongoClientOptions)\n    get options(): Readonly<MongoOptions>\n    get serverApi(): Readonly<ServerApi | undefined>\n    /* Excluded from this release type: monitorCommands */\n    /* Excluded from this release type: monitorCommands */\n    get autoEncrypter(): AutoEncrypter | undefined\n    get readConcern(): ReadConcern | undefined\n    get writeConcern(): WriteConcern | undefined\n    get readPreference(): ReadPreference\n    get bsonOptions(): BSONSerializeOptions\n    /**\n     * Connect to MongoDB using a url\n     *\n     * @see docs.mongodb.org/manual/reference/connection-string/\n     */\n    connect(): Promise<this>\n    /* Excluded from this release type: _connect */\n    /**\n     * Close the client and its underlying connections\n     *\n     * @param force - Force close, emitting no events\n     */\n    close(force?: boolean): Promise<void>\n    /**\n     * Create a new Db instance sharing the current socket connections.\n     *\n     * @param dbName - The name of the database we want to use. If not provided, use database name from connection string.\n     * @param options - Optional settings for Db construction\n     */\n    db(dbName?: string, options?: DbOptions): Db\n    /**\n     * Connect to MongoDB using a url\n     *\n     * @remarks\n     * The programmatically provided options take precedence over the URI options.\n     *\n     * @see https://www.mongodb.com/docs/manual/reference/connection-string/\n     */\n    static connect(\n        url: string,\n        options?: MongoClientOptions,\n    ): Promise<MongoClient>\n    /** Starts a new session on the server */\n    startSession(options?: ClientSessionOptions): ClientSession\n    /**\n     * Runs a given operation with an implicitly created session. The lifetime of the session\n     * will be handled without the need for user interaction.\n     *\n     * NOTE: presently the operation MUST return a Promise (either explicit or implicitly as an async function)\n     *\n     * @param options - Optional settings for the command\n     * @param callback - An callback to execute with an implicitly created session\n     */\n    withSession(callback: WithSessionCallback): Promise<void>\n    withSession(\n        options: ClientSessionOptions,\n        callback: WithSessionCallback,\n    ): Promise<void>\n    /**\n     * Create a new Change Stream, watching for new changes (insertions, updates,\n     * replacements, deletions, and invalidations) in this cluster. Will ignore all\n     * changes to system collections, as well as the local, admin, and config databases.\n     *\n     * @remarks\n     * watch() accepts two generic arguments for distinct use cases:\n     * - The first is to provide the schema that may be defined for all the data within the current cluster\n     * - The second is to override the shape of the change stream document entirely, if it is not provided the type will default to ChangeStreamDocument of the first argument\n     *\n     * @param pipeline - An array of {@link https://www.mongodb.com/docs/manual/reference/operator/aggregation-pipeline/|aggregation pipeline stages} through which to pass change stream documents. This allows for filtering (using $match) and manipulating the change stream documents.\n     * @param options - Optional settings for the command\n     * @typeParam TSchema - Type of the data being detected by the change stream\n     * @typeParam TChange - Type of the whole change stream document emitted\n     */\n    watch<\n        TSchema extends Document = Document,\n        TChange extends Document = ChangeStreamDocument<TSchema>,\n    >(\n        pipeline?: Document[],\n        options?: ChangeStreamOptions,\n    ): ChangeStream<TSchema, TChange>\n}\n\n/** @public */\nexport declare type MongoClientEvents = Pick<\n    TopologyEvents,\n    (typeof MONGO_CLIENT_EVENTS)[number]\n> & {\n    open(mongoClient: MongoClient): void\n}\n\n/**\n * Describes all possible URI query options for the mongo client\n * @public\n * @see https://www.mongodb.com/docs/manual/reference/connection-string\n */\nexport declare interface MongoClientOptions\n    extends BSONSerializeOptions,\n        SupportedNodeConnectionOptions {\n    /** Specifies the name of the replica set, if the mongod is a member of a replica set. */\n    replicaSet?: string\n    /** Enables or disables TLS/SSL for the connection. */\n    tls?: boolean\n    /** A boolean to enable or disables TLS/SSL for the connection. (The ssl option is equivalent to the tls option.) */\n    ssl?: boolean\n    /** Specifies the location of a local TLS Certificate */\n    tlsCertificateFile?: string\n    /** Specifies the location of a local .pem file that contains either the client's TLS/SSL certificate and key or only the client's TLS/SSL key when tlsCertificateFile is used to provide the certificate. */\n    tlsCertificateKeyFile?: string\n    /** Specifies the password to de-crypt the tlsCertificateKeyFile. */\n    tlsCertificateKeyFilePassword?: string\n    /** Specifies the location of a local .pem file that contains the root certificate chain from the Certificate Authority. This file is used to validate the certificate presented by the mongod/mongos instance. */\n    tlsCAFile?: string\n    /** Bypasses validation of the certificates presented by the mongod/mongos instance */\n    tlsAllowInvalidCertificates?: boolean\n    /** Disables hostname validation of the certificate presented by the mongod/mongos instance. */\n    tlsAllowInvalidHostnames?: boolean\n    /** Disables various certificate validations. */\n    tlsInsecure?: boolean\n    /** The time in milliseconds to attempt a connection before timing out. */\n    connectTimeoutMS?: number\n    /** The time in milliseconds to attempt a send or receive on a socket before the attempt times out. */\n    socketTimeoutMS?: number\n    /** An array or comma-delimited string of compressors to enable network compression for communication between this client and a mongod/mongos instance. */\n    compressors?: CompressorName[] | string\n    /** An integer that specifies the compression level if using zlib for network compression. */\n    zlibCompressionLevel?: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | undefined\n    /** The maximum number of hosts to connect to when using an srv connection string, a setting of `0` means unlimited hosts */\n    srvMaxHosts?: number\n    /**\n     * Modifies the srv URI to look like:\n     *\n     * `_{srvServiceName}._tcp.{hostname}.{domainname}`\n     *\n     * Querying this DNS URI is expected to respond with SRV records\n     */\n    srvServiceName?: string\n    /** The maximum number of connections in the connection pool. */\n    maxPoolSize?: number\n    /** The minimum number of connections in the connection pool. */\n    minPoolSize?: number\n    /** The maximum number of connections that may be in the process of being established concurrently by the connection pool. */\n    maxConnecting?: number\n    /** The maximum number of milliseconds that a connection can remain idle in the pool before being removed and closed. */\n    maxIdleTimeMS?: number\n    /** The maximum time in milliseconds that a thread can wait for a connection to become available. */\n    waitQueueTimeoutMS?: number\n    /** Specify a read concern for the collection (only MongoDB 3.2 or higher supported) */\n    readConcern?: ReadConcernLike\n    /** The level of isolation */\n    readConcernLevel?: ReadConcernLevel\n    /** Specifies the read preferences for this connection */\n    readPreference?: ReadPreferenceMode | ReadPreference\n    /** Specifies, in seconds, how stale a secondary can be before the client stops using it for read operations. */\n    maxStalenessSeconds?: number\n    /** Specifies the tags document as a comma-separated list of colon-separated key-value pairs.  */\n    readPreferenceTags?: TagSet[]\n    /** The auth settings for when connection to server. */\n    auth?: Auth\n    /** Specify the database name associated with the user’s credentials. */\n    authSource?: string\n    /** Specify the authentication mechanism that MongoDB will use to authenticate the connection. */\n    authMechanism?: AuthMechanism\n    /** Specify properties for the specified authMechanism as a comma-separated list of colon-separated key-value pairs. */\n    authMechanismProperties?: AuthMechanismProperties\n    /** The size (in milliseconds) of the latency window for selecting among multiple suitable MongoDB instances. */\n    localThresholdMS?: number\n    /** Specifies how long (in milliseconds) to block for server selection before throwing an exception.  */\n    serverSelectionTimeoutMS?: number\n    /** heartbeatFrequencyMS controls when the driver checks the state of the MongoDB deployment. Specify the interval (in milliseconds) between checks, counted from the end of the previous check until the beginning of the next one. */\n    heartbeatFrequencyMS?: number\n    /** Sets the minimum heartbeat frequency. In the event that the driver has to frequently re-check a server's availability, it will wait at least this long since the previous check to avoid wasted effort. */\n    minHeartbeatFrequencyMS?: number\n    /** The name of the application that created this MongoClient instance. MongoDB 3.4 and newer will print this value in the server log upon establishing each connection. It is also recorded in the slow query log and profile collections */\n    appName?: string\n    /** Enables retryable reads. */\n    retryReads?: boolean\n    /** Enable retryable writes. */\n    retryWrites?: boolean\n    /** Allow a driver to force a Single topology type with a connection string containing one host */\n    directConnection?: boolean\n    /** Instruct the driver it is connecting to a load balancer fronting a mongos like service */\n    loadBalanced?: boolean\n    /**\n     * The write concern w value\n     * @deprecated Please use the `writeConcern` option instead\n     */\n    w?: W\n    /**\n     * The write concern timeout\n     * @deprecated Please use the `writeConcern` option instead\n     */\n    wtimeoutMS?: number\n    /**\n     * The journal write concern\n     * @deprecated Please use the `writeConcern` option instead\n     */\n    journal?: boolean\n    /**\n     * A MongoDB WriteConcern, which describes the level of acknowledgement\n     * requested from MongoDB for write operations.\n     *\n     * @see https://www.mongodb.com/docs/manual/reference/write-concern/\n     */\n    writeConcern?: WriteConcern | WriteConcernSettings\n    /** Validate mongod server certificate against Certificate Authority */\n    sslValidate?: boolean\n    /** SSL Certificate file path. */\n    sslCA?: string\n    /** SSL Certificate file path. */\n    sslCert?: string\n    /** SSL Key file file path. */\n    sslKey?: string\n    /** SSL Certificate pass phrase. */\n    sslPass?: string\n    /** SSL Certificate revocation list file path. */\n    sslCRL?: string\n    /** TCP Connection no delay */\n    noDelay?: boolean\n    /** TCP Connection keep alive enabled */\n    keepAlive?: boolean\n    /** The number of milliseconds to wait before initiating keepAlive on the TCP socket */\n    keepAliveInitialDelay?: number\n    /** Force server to assign `_id` values instead of driver */\n    forceServerObjectId?: boolean\n    /** A primary key factory function for generation of custom `_id` keys */\n    pkFactory?: PkFactory\n    /** Enable command monitoring for this client */\n    monitorCommands?: boolean\n    /** Server API version */\n    serverApi?: ServerApi | ServerApiVersion\n    /**\n     * Optionally enable in-use auto encryption\n     *\n     * @remarks\n     *  Automatic encryption is an enterprise only feature that only applies to operations on a collection. Automatic encryption is not supported for operations on a database or view, and operations that are not bypassed will result in error\n     *  (see [libmongocrypt: Auto Encryption Allow-List](https://github.com/mongodb/specifications/blob/master/source/client-side-encryption/client-side-encryption.rst#libmongocrypt-auto-encryption-allow-list)). To bypass automatic encryption for all operations, set bypassAutoEncryption=true in AutoEncryptionOpts.\n     *\n     *  Automatic encryption requires the authenticated user to have the [listCollections privilege action](https://www.mongodb.com/docs/manual/reference/command/listCollections/#dbcmd.listCollections).\n     *\n     *  If a MongoClient with a limited connection pool size (i.e a non-zero maxPoolSize) is configured with AutoEncryptionOptions, a separate internal MongoClient is created if any of the following are true:\n     *  - AutoEncryptionOptions.keyVaultClient is not passed.\n     *  - AutoEncryptionOptions.bypassAutomaticEncryption is false.\n     *\n     * If an internal MongoClient is created, it is configured with the same options as the parent MongoClient except minPoolSize is set to 0 and AutoEncryptionOptions is omitted.\n     */\n    autoEncryption?: AutoEncryptionOptions\n    /** Allows a wrapping driver to amend the client metadata generated by the driver to include information about the wrapping driver */\n    driverInfo?: DriverInfo\n    /** Configures a Socks5 proxy host used for creating TCP connections. */\n    proxyHost?: string\n    /** Configures a Socks5 proxy port used for creating TCP connections. */\n    proxyPort?: number\n    /** Configures a Socks5 proxy username when the proxy in proxyHost requires username/password authentication. */\n    proxyUsername?: string\n    /** Configures a Socks5 proxy password when the proxy in proxyHost requires username/password authentication. */\n    proxyPassword?: string\n    /* Excluded from this release type: srvPoller */\n    /* Excluded from this release type: connectionType */\n    /* Excluded from this release type: __index */\n}\n\n/* Excluded from this release type: MongoClientPrivate */\n\n/**\n * An error generated when a feature that is not enabled or allowed for the current server\n * configuration is used\n *\n *\n * @public\n * @category Error\n */\nexport declare class MongoCompatibilityError extends MongoAPIError {\n    constructor(message: string)\n    get name(): string\n}\n\n/**\n * A representation of the credentials used by MongoDB\n * @public\n */\nexport declare class MongoCredentials {\n    /** The username used for authentication */\n    readonly username: string\n    /** The password used for authentication */\n    readonly password: string\n    /** The database that the user should authenticate against */\n    readonly source: string\n    /** The method used to authenticate */\n    readonly mechanism: AuthMechanism\n    /** Special properties used by some types of auth mechanisms */\n    readonly mechanismProperties: AuthMechanismProperties\n    constructor(options: MongoCredentialsOptions)\n    /** Determines if two MongoCredentials objects are equivalent */\n    equals(other: MongoCredentials): boolean\n    /**\n     * If the authentication mechanism is set to \"default\", resolves the authMechanism\n     * based on the server version and server supported sasl mechanisms.\n     *\n     * @param hello - A hello response from the server\n     */\n    resolveAuthMechanism(hello?: Document): MongoCredentials\n    validate(): void\n    static merge(\n        creds: MongoCredentials | undefined,\n        options: Partial<MongoCredentialsOptions>,\n    ): MongoCredentials\n}\n\n/** @public */\nexport declare interface MongoCredentialsOptions {\n    username: string\n    password: string\n    source: string\n    db?: string\n    mechanism?: AuthMechanism\n    mechanismProperties: AuthMechanismProperties\n}\n\n/**\n * An error thrown when an attempt is made to read from a cursor that has been exhausted\n *\n * @public\n * @category Error\n */\nexport declare class MongoCursorExhaustedError extends MongoAPIError {\n    constructor(message?: string)\n    get name(): string\n}\n\n/**\n * An error thrown when the user attempts to add options to a cursor that has already been\n * initialized\n *\n * @public\n * @category Error\n */\nexport declare class MongoCursorInUseError extends MongoAPIError {\n    constructor(message?: string)\n    get name(): string\n}\n\n/** @public */\nexport declare class MongoDBNamespace {\n    db: string\n    collection: string | undefined\n    /**\n     * Create a namespace object\n     *\n     * @param db - database name\n     * @param collection - collection name\n     */\n    constructor(db: string, collection?: string)\n    toString(): string\n    withCollection(collection: string): MongoDBNamespace\n    static fromString(namespace?: string): MongoDBNamespace\n}\n\n/**\n * An error generated when the driver fails to decompress\n * data received from the server.\n *\n * @public\n * @category Error\n */\nexport declare class MongoDecompressionError extends MongoRuntimeError {\n    constructor(message: string)\n    get name(): string\n}\n\n/**\n * An error generated by the driver\n *\n * @public\n * @category Error\n */\nexport declare class MongoDriverError extends MongoError {\n    constructor(message: string)\n    get name(): string\n}\n\n/**\n * @public\n * @category Error\n *\n * @privateRemarks\n * mongodb-client-encryption has a dependency on this error, it uses the constructor with a string argument\n */\nexport declare class MongoError extends Error {\n    /* Excluded from this release type: [kErrorLabels] */\n    /**\n     * This is a number in MongoServerError and a string in MongoDriverError\n     * @privateRemarks\n     * Define the type override on the subclasses when we can use the override keyword\n     */\n    code?: number | string\n    topologyVersion?: TopologyVersion\n    connectionGeneration?: number\n    cause?: Error\n    constructor(message: string | Error)\n    get name(): string\n    /** Legacy name for server error responses */\n    get errmsg(): string\n    /**\n     * Checks the error to see if it has an error label\n     *\n     * @param label - The error label to check for\n     * @returns returns true if the error has the provided error label\n     */\n    hasErrorLabel(label: string): boolean\n    addErrorLabel(label: string): void\n    get errorLabels(): string[]\n}\n\n/** @public */\nexport declare const MongoErrorLabel: Readonly<{\n    readonly RetryableWriteError: \"RetryableWriteError\"\n    readonly TransientTransactionError: \"TransientTransactionError\"\n    readonly UnknownTransactionCommitResult: \"UnknownTransactionCommitResult\"\n    readonly ResumableChangeStreamError: \"ResumableChangeStreamError\"\n    readonly HandshakeError: \"HandshakeError\"\n    readonly ResetPool: \"ResetPool\"\n    readonly InterruptInUseConnections: \"InterruptInUseConnections\"\n    readonly NoWritesPerformed: \"NoWritesPerformed\"\n}>\n\n/** @public */\nexport declare type MongoErrorLabel =\n    (typeof MongoErrorLabel)[keyof typeof MongoErrorLabel]\n\n/**\n * An error generated when the user attempts to operate\n * on a session that has expired or has been closed.\n *\n * @public\n * @category Error\n */\nexport declare class MongoExpiredSessionError extends MongoAPIError {\n    constructor(message?: string)\n    get name(): string\n}\n\n/**\n * An error generated when a malformed or invalid chunk is\n * encountered when reading from a GridFSStream.\n *\n * @public\n * @category Error\n */\nexport declare class MongoGridFSChunkError extends MongoRuntimeError {\n    constructor(message: string)\n    get name(): string\n}\n\n/** An error generated when a GridFSStream operation fails to execute.\n *\n * @public\n * @category Error\n */\nexport declare class MongoGridFSStreamError extends MongoRuntimeError {\n    constructor(message: string)\n    get name(): string\n}\n\n/**\n * An error generated when the user supplies malformed or unexpected arguments\n * or when a required argument or field is not provided.\n *\n *\n * @public\n * @category Error\n */\nexport declare class MongoInvalidArgumentError extends MongoAPIError {\n    constructor(message: string)\n    get name(): string\n}\n\n/**\n * A error generated when the user attempts to authenticate\n * via Kerberos, but fails to connect to the Kerberos client.\n *\n * @public\n * @category Error\n */\nexport declare class MongoKerberosError extends MongoRuntimeError {\n    constructor(message: string)\n    get name(): string\n}\n\n/* Excluded from this release type: MongoLoggableComponent */\n\n/* Excluded from this release type: MongoLogger */\n\n/* Excluded from this release type: MongoLoggerEnvOptions */\n\n/* Excluded from this release type: MongoLoggerMongoClientOptions */\n\n/* Excluded from this release type: MongoLoggerOptions */\n\n/**\n * An error generated when the user fails to provide authentication credentials before attempting\n * to connect to a mongo server instance.\n *\n *\n * @public\n * @category Error\n */\nexport declare class MongoMissingCredentialsError extends MongoAPIError {\n    constructor(message: string)\n    get name(): string\n}\n\n/**\n * An error generated when a required module or dependency is not present in the local environment\n *\n * @public\n * @category Error\n */\nexport declare class MongoMissingDependencyError extends MongoAPIError {\n    constructor(message: string)\n    get name(): string\n}\n\n/**\n * An error indicating an issue with the network, including TCP errors and timeouts.\n * @public\n * @category Error\n */\nexport declare class MongoNetworkError extends MongoError {\n    /* Excluded from this release type: [kBeforeHandshake] */\n    constructor(message: string | Error, options?: MongoNetworkErrorOptions)\n    get name(): string\n}\n\n/** @public */\nexport declare interface MongoNetworkErrorOptions {\n    /** Indicates the timeout happened before a connection handshake completed */\n    beforeHandshake: boolean\n}\n\n/**\n * An error indicating a network timeout occurred\n * @public\n * @category Error\n *\n * @privateRemarks\n * mongodb-client-encryption has a dependency on this error with an instanceof check\n */\nexport declare class MongoNetworkTimeoutError extends MongoNetworkError {\n    constructor(message: string, options?: MongoNetworkErrorOptions)\n    get name(): string\n}\n\n/**\n * An error thrown when the user attempts to operate on a database or collection through a MongoClient\n * that has not yet successfully called the \"connect\" method\n *\n * @public\n * @category Error\n */\nexport declare class MongoNotConnectedError extends MongoAPIError {\n    constructor(message: string)\n    get name(): string\n}\n\n/**\n * Mongo Client Options\n * @public\n */\nexport declare interface MongoOptions\n    extends Required<\n            Pick<\n                MongoClientOptions,\n                | \"autoEncryption\"\n                | \"connectTimeoutMS\"\n                | \"directConnection\"\n                | \"driverInfo\"\n                | \"forceServerObjectId\"\n                | \"minHeartbeatFrequencyMS\"\n                | \"heartbeatFrequencyMS\"\n                | \"keepAlive\"\n                | \"keepAliveInitialDelay\"\n                | \"localThresholdMS\"\n                | \"maxConnecting\"\n                | \"maxIdleTimeMS\"\n                | \"maxPoolSize\"\n                | \"minPoolSize\"\n                | \"monitorCommands\"\n                | \"noDelay\"\n                | \"pkFactory\"\n                | \"raw\"\n                | \"replicaSet\"\n                | \"retryReads\"\n                | \"retryWrites\"\n                | \"serverSelectionTimeoutMS\"\n                | \"socketTimeoutMS\"\n                | \"srvMaxHosts\"\n                | \"srvServiceName\"\n                | \"tlsAllowInvalidCertificates\"\n                | \"tlsAllowInvalidHostnames\"\n                | \"tlsInsecure\"\n                | \"waitQueueTimeoutMS\"\n                | \"zlibCompressionLevel\"\n            >\n        >,\n        SupportedNodeConnectionOptions {\n    appName?: string\n    hosts: HostAddress[]\n    srvHost?: string\n    credentials?: MongoCredentials\n    readPreference: ReadPreference\n    readConcern: ReadConcern\n    loadBalanced: boolean\n    serverApi: ServerApi\n    compressors: CompressorName[]\n    writeConcern: WriteConcern\n    dbName: string\n    metadata: ClientMetadata\n    autoEncrypter?: AutoEncrypter\n    proxyHost?: string\n    proxyPort?: number\n    proxyUsername?: string\n    proxyPassword?: string\n    /* Excluded from this release type: connectionType */\n    /* Excluded from this release type: encrypter */\n    /* Excluded from this release type: userSpecifiedAuthSource */\n    /* Excluded from this release type: userSpecifiedReplicaSet */\n    /**\n     * # NOTE ABOUT TLS Options\n     *\n     * If set TLS enabled, equivalent to setting the ssl option.\n     *\n     * ### Additional options:\n     *\n     * |    nodejs option     | MongoDB equivalent                                       | type                                   |\n     * |:---------------------|--------------------------------------------------------- |:---------------------------------------|\n     * | `ca`                 | `sslCA`, `tlsCAFile`                                     | `string \\| Buffer \\| Buffer[]`         |\n     * | `crl`                | `sslCRL`                                                 | `string \\| Buffer \\| Buffer[]`         |\n     * | `cert`               | `sslCert`, `tlsCertificateFile`, `tlsCertificateKeyFile` | `string \\| Buffer \\| Buffer[]`         |\n     * | `key`                | `sslKey`, `tlsCertificateKeyFile`                        | `string \\| Buffer \\| KeyObject[]`      |\n     * | `passphrase`         | `sslPass`, `tlsCertificateKeyFilePassword`               | `string`                               |\n     * | `rejectUnauthorized` | `sslValidate`                                            | `boolean`                              |\n     *\n     */\n    tls: boolean\n    /* Excluded from this release type: __index */\n    /* Excluded from this release type: mongoLoggerOptions */\n}\n\n/**\n * An error used when attempting to parse a value (like a connection string)\n * @public\n * @category Error\n */\nexport declare class MongoParseError extends MongoDriverError {\n    constructor(message: string)\n    get name(): string\n}\n\n/**\n * An error generated when the driver encounters unexpected input\n * or reaches an unexpected/invalid internal state\n *\n * @privateRemarks\n * Should **never** be directly instantiated.\n *\n * @public\n * @category Error\n */\nexport declare class MongoRuntimeError extends MongoDriverError {\n    constructor(message: string)\n    get name(): string\n}\n\n/**\n * An error generated when an attempt is made to operate\n * on a closed/closing server.\n *\n * @public\n * @category Error\n */\nexport declare class MongoServerClosedError extends MongoAPIError {\n    constructor(message?: string)\n    get name(): string\n}\n\n/**\n * An error coming from the mongo server\n *\n * @public\n * @category Error\n */\nexport declare class MongoServerError extends MongoError {\n    codeName?: string\n    writeConcernError?: Document\n    errInfo?: Document\n    ok?: number;\n    [key: string]: any\n    constructor(message: ErrorDescription)\n    get name(): string\n}\n\n/**\n * An error signifying a client-side server selection error\n * @public\n * @category Error\n */\nexport declare class MongoServerSelectionError extends MongoSystemError {\n    constructor(message: string, reason: TopologyDescription)\n    get name(): string\n}\n\n/**\n * An error signifying a general system issue\n * @public\n * @category Error\n */\nexport declare class MongoSystemError extends MongoError {\n    /** An optional reason context, such as an error saved during flow of monitoring and selecting servers */\n    reason?: TopologyDescription\n    constructor(message: string, reason: TopologyDescription)\n    get name(): string\n}\n\n/**\n * An error thrown when the user calls a function or method not supported on a tailable cursor\n *\n * @public\n * @category Error\n */\nexport declare class MongoTailableCursorError extends MongoAPIError {\n    constructor(message?: string)\n    get name(): string\n}\n\n/**\n * An error generated when an attempt is made to operate on a\n * dropped, or otherwise unavailable, database.\n *\n * @public\n * @category Error\n */\nexport declare class MongoTopologyClosedError extends MongoAPIError {\n    constructor(message?: string)\n    get name(): string\n}\n\n/**\n * An error generated when the user makes a mistake in the usage of transactions.\n * (e.g. attempting to commit a transaction with a readPreference other than primary)\n *\n * @public\n * @category Error\n */\nexport declare class MongoTransactionError extends MongoAPIError {\n    constructor(message: string)\n    get name(): string\n}\n\n/**\n * An error generated when a **parsable** unexpected response comes from the server.\n * This is generally an error where the driver in a state expecting a certain behavior to occur in\n * the next message from MongoDB but it receives something else.\n * This error **does not** represent an issue with wire message formatting.\n *\n * #### Example\n * When an operation fails, it is the driver's job to retry it. It must perform serverSelection\n * again to make sure that it attempts the operation against a server in a good state. If server\n * selection returns a server that does not support retryable operations, this error is used.\n * This scenario is unlikely as retryable support would also have been determined on the first attempt\n * but it is possible the state change could report a selectable server that does not support retries.\n *\n * @public\n * @category Error\n */\nexport declare class MongoUnexpectedServerResponseError extends MongoRuntimeError {\n    constructor(message: string)\n    get name(): string\n}\n\n/**\n * An error thrown when the server reports a writeConcernError\n * @public\n * @category Error\n */\nexport declare class MongoWriteConcernError extends MongoServerError {\n    /** The result document (provided if ok: 1) */\n    result?: Document\n    constructor(message: ErrorDescription, result?: Document)\n    get name(): string\n}\n\n/* Excluded from this release type: Monitor */\n\n/** @public */\nexport declare type MonitorEvents = {\n    serverHeartbeatStarted(event: ServerHeartbeatStartedEvent): void\n    serverHeartbeatSucceeded(event: ServerHeartbeatSucceededEvent): void\n    serverHeartbeatFailed(event: ServerHeartbeatFailedEvent): void\n    resetServer(error?: MongoError): void\n    resetConnectionPool(): void\n    close(): void\n} & EventEmitterWithState\n\n/* Excluded from this release type: MonitorInterval */\n\n/* Excluded from this release type: MonitorIntervalOptions */\n\n/** @public */\nexport declare interface MonitorOptions\n    extends Omit<ConnectionOptions, \"id\" | \"generation\" | \"hostAddress\"> {\n    connectTimeoutMS: number\n    heartbeatFrequencyMS: number\n    minHeartbeatFrequencyMS: number\n}\n\n/* Excluded from this release type: MonitorPrivate */\n\n/* Excluded from this release type: Msg */\n\n/**\n * @public\n * returns tuple of strings (keys to be joined on '.') that represent every path into a schema\n * https://www.mongodb.com/docs/manual/tutorial/query-embedded-documents/\n *\n * @remarks\n * Through testing we determined that a depth of 8 is safe for the typescript compiler\n * and provides reasonable compilation times. This number is otherwise not special and\n * should be changed if issues are found with this level of checking. Beyond this\n * depth any helpers that make use of NestedPaths should devolve to not asserting any\n * type safety on the input.\n */\nexport declare type NestedPaths<\n    Type,\n    Depth extends number[],\n> = Depth[\"length\"] extends 8\n    ? []\n    : Type extends\n          | string\n          | number\n          | bigint\n          | boolean\n          | Date\n          | RegExp\n          | Buffer\n          | Uint8Array\n          | ((...args: any[]) => any)\n          | {\n                _bsontype: string\n            }\n    ? []\n    : Type extends ReadonlyArray<infer ArrayType>\n    ? [] | [number, ...NestedPaths<ArrayType, [...Depth, 1]>]\n    : Type extends Map<string, any>\n    ? [string]\n    : Type extends object\n    ? {\n          [Key in Extract<keyof Type, string>]: Type[Key] extends Type\n              ? [Key]\n              : Type extends Type[Key]\n              ? [Key]\n              : Type[Key] extends ReadonlyArray<infer ArrayType>\n              ? Type extends ArrayType\n                  ? [Key]\n                  : ArrayType extends Type\n                  ? [Key]\n                  : [Key, ...NestedPaths<Type[Key], [...Depth, 1]>] // child is not structured the same as the parent\n              : [Key, ...NestedPaths<Type[Key], [...Depth, 1]>] | [Key]\n      }[Extract<keyof Type, string>]\n    : []\n\n/**\n * @public\n * returns keys (strings) for every path into a schema with a value of type\n * https://www.mongodb.com/docs/manual/tutorial/query-embedded-documents/\n */\nexport declare type NestedPathsOfType<TSchema, Type> = KeysOfAType<\n    {\n        [Property in Join<NestedPaths<TSchema, []>, \".\">]: PropertyType<\n            TSchema,\n            Property\n        >\n    },\n    Type\n>\n\n/**\n * @public\n * A type that extends Document but forbids anything that \"looks like\" an object id.\n */\nexport declare type NonObjectIdLikeDocument = {\n    [key in keyof ObjectIdLike]?: never\n} & Document\n\n/** It avoids using fields with not acceptable types @public */\nexport declare type NotAcceptedFields<TSchema, FieldType> = {\n    readonly [key in KeysOfOtherType<TSchema, FieldType>]?: never\n}\n\n/** @public */\nexport declare type NumericType = IntegerType | Decimal128 | Double\n\nexport { ObjectId }\n\n/**\n * @public\n * @experimental\n */\nexport declare interface OIDCMechanismServerStep1 {\n    authorizationEndpoint?: string\n    tokenEndpoint?: string\n    deviceAuthorizationEndpoint?: string\n    clientId: string\n    clientSecret?: string\n    requestScopes?: string[]\n}\n\n/**\n * @public\n * @experimental\n */\nexport declare type OIDCRefreshFunction = (\n    principalName: string,\n    serverResult: OIDCMechanismServerStep1,\n    result: OIDCRequestTokenResult,\n    timeout: AbortSignal | number,\n) => Promise<OIDCRequestTokenResult>\n\n/**\n * @public\n * @experimental\n */\nexport declare type OIDCRequestFunction = (\n    principalName: string,\n    serverResult: OIDCMechanismServerStep1,\n    timeout: AbortSignal | number,\n) => Promise<OIDCRequestTokenResult>\n\n/**\n * @public\n * @experimental\n */\nexport declare interface OIDCRequestTokenResult {\n    accessToken: string\n    expiresInSeconds?: number\n    refreshToken?: string\n}\n\n/** @public */\nexport declare type OneOrMore<T> = T | ReadonlyArray<T>\n\n/** @public */\nexport declare type OnlyFieldsOfType<\n    TSchema,\n    FieldType = any,\n    AssignableType = FieldType,\n> = IsAny<\n    TSchema[keyof TSchema],\n    Record<string, FieldType>,\n    AcceptedFields<TSchema, FieldType, AssignableType> &\n        NotAcceptedFields<TSchema, FieldType> &\n        Record<string, AssignableType>\n>\n\n/* Excluded from this release type: OperationDescription */\n\n/** @public */\nexport declare interface OperationOptions extends BSONSerializeOptions {\n    /** Specify ClientSession for this command */\n    session?: ClientSession\n    willRetryWrite?: boolean\n    /** The preferred read preference (ReadPreference.primary, ReadPreference.primary_preferred, ReadPreference.secondary, ReadPreference.secondary_preferred, ReadPreference.nearest). */\n    readPreference?: ReadPreferenceLike\n    /* Excluded from this release type: bypassPinningCheck */\n    omitReadPreference?: boolean\n}\n\n/* Excluded from this release type: OperationParent */\n\n/**\n * Represents a specific point in time on a server. Can be retrieved by using `db.command()`\n * @public\n * @see https://www.mongodb.com/docs/manual/reference/method/db.runCommand/#response\n */\nexport declare type OperationTime = Timestamp\n\n/* Excluded from this release type: OpMsgOptions */\n\n/* Excluded from this release type: OpQueryOptions */\n\n/* Excluded from this release type: OpResponseOptions */\n\n/**\n * Add an optional _id field to an object shaped type\n * @public\n */\nexport declare type OptionalId<TSchema> = EnhancedOmit<TSchema, \"_id\"> & {\n    _id?: InferIdType<TSchema>\n}\n\n/**\n * Adds an optional _id field to an object shaped type, unless the _id field is required on that type.\n * In the case _id is required, this method continues to require_id.\n *\n * @public\n *\n * @privateRemarks\n * `ObjectId extends TSchema['_id']` is a confusing ordering at first glance. Rather than ask\n * `TSchema['_id'] extends ObjectId` which translated to \"Is the _id property ObjectId?\"\n * we instead ask \"Does ObjectId look like (have the same shape) as the _id?\"\n */\nexport declare type OptionalUnlessRequiredId<TSchema> = TSchema extends {\n    _id: any\n}\n    ? TSchema\n    : OptionalId<TSchema>\n\n/** @public */\nexport declare class OrderedBulkOperation extends BulkOperationBase {\n    /* Excluded from this release type: __constructor */\n    addToOperationsList(\n        batchType: BatchType,\n        document: Document | UpdateStatement | DeleteStatement,\n    ): this\n}\n\n/** @public */\nexport declare interface PkFactory {\n    createPk(): any\n}\n\n/* Excluded from this release type: PoolState */\n\n/** @public */\nexport declare const ProfilingLevel: Readonly<{\n    readonly off: \"off\"\n    readonly slowOnly: \"slow_only\"\n    readonly all: \"all\"\n}>\n\n/** @public */\nexport declare type ProfilingLevel =\n    (typeof ProfilingLevel)[keyof typeof ProfilingLevel]\n\n/** @public */\nexport declare type ProfilingLevelOptions = CommandOperationOptions\n\n/** @public */\nexport declare type PropertyType<\n    Type,\n    Property extends string,\n> = string extends Property\n    ? unknown\n    : Property extends keyof Type\n    ? Type[Property]\n    : Property extends `${number}`\n    ? Type extends ReadonlyArray<infer ArrayType>\n        ? ArrayType\n        : unknown\n    : Property extends `${infer Key}.${infer Rest}`\n    ? Key extends `${number}`\n        ? Type extends ReadonlyArray<infer ArrayType>\n            ? PropertyType<ArrayType, Rest>\n            : unknown\n        : Key extends keyof Type\n        ? Type[Key] extends Map<string, infer MapType>\n            ? MapType\n            : PropertyType<Type[Key], Rest>\n        : unknown\n    : unknown\n\n/** @public */\nexport declare interface ProxyOptions {\n    proxyHost?: string\n    proxyPort?: number\n    proxyUsername?: string\n    proxyPassword?: string\n}\n\n/** @public */\nexport declare type PullAllOperator<TSchema> = ({\n    readonly [key in KeysOfAType<TSchema, ReadonlyArray<any>>]?: TSchema[key]\n} & NotAcceptedFields<TSchema, ReadonlyArray<any>>) & {\n    readonly [key: string]: ReadonlyArray<any>\n}\n\n/** @public */\nexport declare type PullOperator<TSchema> = ({\n    readonly [key in KeysOfAType<TSchema, ReadonlyArray<any>>]?:\n        | Partial<Flatten<TSchema[key]>>\n        | FilterOperations<Flatten<TSchema[key]>>\n} & NotAcceptedFields<TSchema, ReadonlyArray<any>>) & {\n    readonly [key: string]: FilterOperators<any> | any\n}\n\n/** @public */\nexport declare type PushOperator<TSchema> = ({\n    readonly [key in KeysOfAType<TSchema, ReadonlyArray<any>>]?:\n        | Flatten<TSchema[key]>\n        | ArrayOperator<Array<Flatten<TSchema[key]>>>\n} & NotAcceptedFields<TSchema, ReadonlyArray<any>>) & {\n    readonly [key: string]: ArrayOperator<any> | any\n}\n\n/* Excluded from this release type: Query */\n\n/**\n * The MongoDB ReadConcern, which allows for control of the consistency and isolation properties\n * of the data read from replica sets and replica set shards.\n * @public\n *\n * @see https://www.mongodb.com/docs/manual/reference/read-concern/index.html\n */\nexport declare class ReadConcern {\n    level: ReadConcernLevel | string\n    /** Constructs a ReadConcern from the read concern level.*/\n    constructor(level: ReadConcernLevel)\n    /**\n     * Construct a ReadConcern given an options object.\n     *\n     * @param options - The options object from which to extract the write concern.\n     */\n    static fromOptions(options?: {\n        readConcern?: ReadConcernLike\n        level?: ReadConcernLevel\n    }): ReadConcern | undefined\n    static get MAJORITY(): \"majority\"\n    static get AVAILABLE(): \"available\"\n    static get LINEARIZABLE(): \"linearizable\"\n    static get SNAPSHOT(): \"snapshot\"\n    toJSON(): Document\n}\n\n/** @public */\nexport declare const ReadConcernLevel: Readonly<{\n    readonly local: \"local\"\n    readonly majority: \"majority\"\n    readonly linearizable: \"linearizable\"\n    readonly available: \"available\"\n    readonly snapshot: \"snapshot\"\n}>\n\n/** @public */\nexport declare type ReadConcernLevel =\n    (typeof ReadConcernLevel)[keyof typeof ReadConcernLevel]\n\n/** @public */\nexport declare type ReadConcernLike =\n    | ReadConcern\n    | {\n          level: ReadConcernLevel\n      }\n    | ReadConcernLevel\n\n/**\n * The **ReadPreference** class is a class that represents a MongoDB ReadPreference and is\n * used to construct connections.\n * @public\n *\n * @see https://www.mongodb.com/docs/manual/core/read-preference/\n */\nexport declare class ReadPreference {\n    mode: ReadPreferenceMode\n    tags?: TagSet[]\n    hedge?: HedgeOptions\n    maxStalenessSeconds?: number\n    minWireVersion?: number\n    static PRIMARY: \"primary\"\n    static PRIMARY_PREFERRED: \"primaryPreferred\"\n    static SECONDARY: \"secondary\"\n    static SECONDARY_PREFERRED: \"secondaryPreferred\"\n    static NEAREST: \"nearest\"\n    static primary: ReadPreference\n    static primaryPreferred: ReadPreference\n    static secondary: ReadPreference\n    static secondaryPreferred: ReadPreference\n    static nearest: ReadPreference\n    /**\n     * @param mode - A string describing the read preference mode (primary|primaryPreferred|secondary|secondaryPreferred|nearest)\n     * @param tags - A tag set used to target reads to members with the specified tag(s). tagSet is not available if using read preference mode primary.\n     * @param options - Additional read preference options\n     */\n    constructor(\n        mode: ReadPreferenceMode,\n        tags?: TagSet[],\n        options?: ReadPreferenceOptions,\n    )\n    get preference(): ReadPreferenceMode\n    static fromString(mode: string): ReadPreference\n    /**\n     * Construct a ReadPreference given an options object.\n     *\n     * @param options - The options object from which to extract the read preference.\n     */\n    static fromOptions(\n        options?: ReadPreferenceFromOptions,\n    ): ReadPreference | undefined\n    /**\n     * Replaces options.readPreference with a ReadPreference instance\n     */\n    static translate(\n        options: ReadPreferenceLikeOptions,\n    ): ReadPreferenceLikeOptions\n    /**\n     * Validate if a mode is legal\n     *\n     * @param mode - The string representing the read preference mode.\n     */\n    static isValid(mode: string): boolean\n    /**\n     * Validate if a mode is legal\n     *\n     * @param mode - The string representing the read preference mode.\n     */\n    isValid(mode?: string): boolean\n    /**\n     * Indicates that this readPreference needs the \"SecondaryOk\" bit when sent over the wire\n     * @see https://www.mongodb.com/docs/manual/reference/mongodb-wire-protocol/#op-query\n     */\n    secondaryOk(): boolean\n    /**\n     * Check if the two ReadPreferences are equivalent\n     *\n     * @param readPreference - The read preference with which to check equality\n     */\n    equals(readPreference: ReadPreference): boolean\n    /** Return JSON representation */\n    toJSON(): Document\n}\n\n/** @public */\nexport declare interface ReadPreferenceFromOptions\n    extends ReadPreferenceLikeOptions {\n    session?: ClientSession\n    readPreferenceTags?: TagSet[]\n    hedge?: HedgeOptions\n}\n\n/** @public */\nexport declare type ReadPreferenceLike = ReadPreference | ReadPreferenceMode\n\n/** @public */\nexport declare interface ReadPreferenceLikeOptions\n    extends ReadPreferenceOptions {\n    readPreference?:\n        | ReadPreferenceLike\n        | {\n              mode?: ReadPreferenceMode\n              preference?: ReadPreferenceMode\n              tags?: TagSet[]\n              maxStalenessSeconds?: number\n          }\n}\n\n/** @public */\nexport declare const ReadPreferenceMode: Readonly<{\n    readonly primary: \"primary\"\n    readonly primaryPreferred: \"primaryPreferred\"\n    readonly secondary: \"secondary\"\n    readonly secondaryPreferred: \"secondaryPreferred\"\n    readonly nearest: \"nearest\"\n}>\n\n/** @public */\nexport declare type ReadPreferenceMode =\n    (typeof ReadPreferenceMode)[keyof typeof ReadPreferenceMode]\n\n/** @public */\nexport declare interface ReadPreferenceOptions {\n    /** Max secondary read staleness in seconds, Minimum value is 90 seconds.*/\n    maxStalenessSeconds?: number\n    /** Server mode in which the same query is dispatched in parallel to multiple replica set members. */\n    hedge?: HedgeOptions\n}\n\n/** @public */\nexport declare type RegExpOrString<T> = T extends string\n    ? BSONRegExp | RegExp | T\n    : T\n\n/** @public */\nexport declare type RemoveUserOptions = CommandOperationOptions\n\n/** @public */\nexport declare interface RenameOptions extends CommandOperationOptions {\n    /** Drop the target name collection if it previously exists. */\n    dropTarget?: boolean\n    /** Unclear */\n    new_collection?: boolean\n}\n\n/** @public */\nexport declare interface ReplaceOneModel<TSchema extends Document = Document> {\n    /** The filter to limit the replaced document. */\n    filter: Filter<TSchema>\n    /** The document with which to replace the matched document. */\n    replacement: WithoutId<TSchema>\n    /** Specifies a collation. */\n    collation?: CollationOptions\n    /** The index to use. If specified, then the query system will only consider plans using the hinted index. */\n    hint?: Hint\n    /** When true, creates a new document if no document matches the query. */\n    upsert?: boolean\n}\n\n/** @public */\nexport declare interface ReplaceOptions extends CommandOperationOptions {\n    /** If true, allows the write to opt-out of document level validation */\n    bypassDocumentValidation?: boolean\n    /** Specifies a collation */\n    collation?: CollationOptions\n    /** Specify that the update query should only consider plans using the hinted index */\n    hint?: string | Document\n    /** When true, creates a new document if no document matches the query */\n    upsert?: boolean\n    /** Map of parameter names and values that can be accessed using $$var (requires MongoDB 5.0). */\n    let?: Document\n}\n\n/* Excluded from this release type: Response */\n\n/**\n * @public\n * @deprecated Please use the ChangeStreamCursorOptions type instead.\n */\nexport declare interface ResumeOptions {\n    startAtOperationTime?: Timestamp\n    batchSize?: number\n    maxAwaitTimeMS?: number\n    collation?: CollationOptions\n    readPreference?: ReadPreference\n    resumeAfter?: ResumeToken\n    startAfter?: ResumeToken\n    fullDocument?: string\n}\n\n/**\n * Represents the logical starting point for a new ChangeStream or resuming a ChangeStream on the server.\n * @see https://www.mongodb.com/docs/manual/changeStreams/#std-label-change-stream-resume\n * @public\n */\nexport declare type ResumeToken = unknown\n\n/** @public */\nexport declare const ReturnDocument: Readonly<{\n    readonly BEFORE: \"before\"\n    readonly AFTER: \"after\"\n}>\n\n/** @public */\nexport declare type ReturnDocument =\n    (typeof ReturnDocument)[keyof typeof ReturnDocument]\n\n/** @public */\nexport declare interface RoleSpecification {\n    /**\n     * A role grants privileges to perform sets of actions on defined resources.\n     * A given role applies to the database on which it is defined and can grant access down to a collection level of granularity.\n     */\n    role: string\n    /** The database this user's role should effect. */\n    db: string\n}\n\n/** @public */\nexport declare interface RootFilterOperators<TSchema> extends Document {\n    $and?: Filter<TSchema>[]\n    $nor?: Filter<TSchema>[]\n    $or?: Filter<TSchema>[]\n    $text?: {\n        $search: string\n        $language?: string\n        $caseSensitive?: boolean\n        $diacriticSensitive?: boolean\n    }\n    $where?: string | ((this: TSchema) => boolean)\n    $comment?: string | Document\n}\n\n/* Excluded from this release type: RTTPinger */\n\n/* Excluded from this release type: RTTPingerOptions */\n\n/** @public */\nexport declare type RunCommandOptions = CommandOperationOptions\n\n/** @public */\nexport declare type SchemaMember<T, V> =\n    | {\n          [P in keyof T]?: V\n      }\n    | {\n          [key: string]: V\n      }\n\n/** @public */\nexport declare interface SelectServerOptions {\n    readPreference?: ReadPreferenceLike\n    /** How long to block for server selection before throwing an error */\n    serverSelectionTimeoutMS?: number\n    session?: ClientSession\n}\n\nexport { serialize }\n\n/* Excluded from this release type: Server */\n\n/** @public */\nexport declare interface ServerApi {\n    version: ServerApiVersion\n    strict?: boolean\n    deprecationErrors?: boolean\n}\n\n/** @public */\nexport declare const ServerApiVersion: Readonly<{\n    readonly v1: \"1\"\n}>\n\n/** @public */\nexport declare type ServerApiVersion =\n    (typeof ServerApiVersion)[keyof typeof ServerApiVersion]\n\n/** @public */\nexport declare class ServerCapabilities {\n    maxWireVersion: number\n    minWireVersion: number\n    constructor(hello: Document)\n    get hasAggregationCursor(): boolean\n    get hasWriteCommands(): boolean\n    get hasTextSearch(): boolean\n    get hasAuthCommands(): boolean\n    get hasListCollectionsCommand(): boolean\n    get hasListIndexesCommand(): boolean\n    get supportsSnapshotReads(): boolean\n    get commandsTakeWriteConcern(): boolean\n    get commandsTakeCollation(): boolean\n}\n\n/**\n * Emitted when server is closed.\n * @public\n * @category Event\n */\nexport declare class ServerClosedEvent {\n    /** A unique identifier for the topology */\n    topologyId: number\n    /** The address (host/port pair) of the server */\n    address: string\n    /* Excluded from this release type: __constructor */\n}\n\n/**\n * The client's view of a single server, based on the most recent hello outcome.\n *\n * Internal type, not meant to be directly instantiated\n * @public\n */\nexport declare class ServerDescription {\n    address: string\n    type: ServerType\n    hosts: string[]\n    passives: string[]\n    arbiters: string[]\n    tags: TagSet\n    error: MongoError | null\n    topologyVersion: TopologyVersion | null\n    minWireVersion: number\n    maxWireVersion: number\n    roundTripTime: number\n    lastUpdateTime: number\n    lastWriteDate: number\n    me: string | null\n    primary: string | null\n    setName: string | null\n    setVersion: number | null\n    electionId: ObjectId | null\n    logicalSessionTimeoutMinutes: number | null\n    $clusterTime?: ClusterTime\n    /* Excluded from this release type: __constructor */\n    get hostAddress(): HostAddress\n    get allHosts(): string[]\n    /** Is this server available for reads*/\n    get isReadable(): boolean\n    /** Is this server data bearing */\n    get isDataBearing(): boolean\n    /** Is this server available for writes */\n    get isWritable(): boolean\n    get host(): string\n    get port(): number\n    /**\n     * Determines if another `ServerDescription` is equal to this one per the rules defined\n     * in the {@link https://github.com/mongodb/specifications/blob/master/source/server-discovery-and-monitoring/server-discovery-and-monitoring.rst#serverdescription|SDAM spec}\n     */\n    equals(other?: ServerDescription | null): boolean\n}\n\n/**\n * Emitted when server description changes, but does NOT include changes to the RTT.\n * @public\n * @category Event\n */\nexport declare class ServerDescriptionChangedEvent {\n    /** A unique identifier for the topology */\n    topologyId: number\n    /** The address (host/port pair) of the server */\n    address: string\n    /** The previous server description */\n    previousDescription: ServerDescription\n    /** The new server description */\n    newDescription: ServerDescription\n    /* Excluded from this release type: __constructor */\n}\n\n/* Excluded from this release type: ServerDescriptionOptions */\n\n/** @public */\nexport declare type ServerEvents = {\n    serverHeartbeatStarted(event: ServerHeartbeatStartedEvent): void\n    serverHeartbeatSucceeded(event: ServerHeartbeatSucceededEvent): void\n    serverHeartbeatFailed(event: ServerHeartbeatFailedEvent): void\n    /* Excluded from this release type: connect */\n    descriptionReceived(description: ServerDescription): void\n    closed(): void\n    ended(): void\n} & ConnectionPoolEvents &\n    EventEmitterWithState\n\n/**\n * Emitted when the server monitor’s hello fails, either with an “ok: 0” or a socket exception.\n * @public\n * @category Event\n */\nexport declare class ServerHeartbeatFailedEvent {\n    /** The connection id for the command */\n    connectionId: string\n    /** The execution time of the event in ms */\n    duration: number\n    /** The command failure */\n    failure: Error\n    /* Excluded from this release type: __constructor */\n}\n\n/**\n * Emitted when the server monitor’s hello command is started - immediately before\n * the hello command is serialized into raw BSON and written to the socket.\n *\n * @public\n * @category Event\n */\nexport declare class ServerHeartbeatStartedEvent {\n    /** The connection id for the command */\n    connectionId: string\n    /* Excluded from this release type: __constructor */\n}\n\n/**\n * Emitted when the server monitor’s hello succeeds.\n * @public\n * @category Event\n */\nexport declare class ServerHeartbeatSucceededEvent {\n    /** The connection id for the command */\n    connectionId: string\n    /** The execution time of the event in ms */\n    duration: number\n    /** The command reply */\n    reply: Document\n    /* Excluded from this release type: __constructor */\n}\n\n/**\n * Emitted when server is initialized.\n * @public\n * @category Event\n */\nexport declare class ServerOpeningEvent {\n    /** A unique identifier for the topology */\n    topologyId: number\n    /** The address (host/port pair) of the server */\n    address: string\n    /* Excluded from this release type: __constructor */\n}\n\n/* Excluded from this release type: ServerOptions */\n\n/* Excluded from this release type: ServerPrivate */\n\n/* Excluded from this release type: ServerSelectionCallback */\n\n/* Excluded from this release type: ServerSelectionRequest */\n\n/* Excluded from this release type: ServerSelector */\n\n/**\n * Reflects the existence of a session on the server. Can be reused by the session pool.\n * WARNING: not meant to be instantiated directly. For internal use only.\n * @public\n */\nexport declare class ServerSession {\n    id: ServerSessionId\n    lastUse: number\n    txnNumber: number\n    isDirty: boolean\n    /* Excluded from this release type: __constructor */\n    /**\n     * Determines if the server session has timed out.\n     *\n     * @param sessionTimeoutMinutes - The server's \"logicalSessionTimeoutMinutes\"\n     */\n    hasTimedOut(sessionTimeoutMinutes: number): boolean\n    /* Excluded from this release type: clone */\n}\n\n/** @public */\nexport declare type ServerSessionId = {\n    id: Binary\n}\n\n/* Excluded from this release type: ServerSessionPool */\n\n/**\n * An enumeration of server types we know about\n * @public\n */\nexport declare const ServerType: Readonly<{\n    readonly Standalone: \"Standalone\"\n    readonly Mongos: \"Mongos\"\n    readonly PossiblePrimary: \"PossiblePrimary\"\n    readonly RSPrimary: \"RSPrimary\"\n    readonly RSSecondary: \"RSSecondary\"\n    readonly RSArbiter: \"RSArbiter\"\n    readonly RSOther: \"RSOther\"\n    readonly RSGhost: \"RSGhost\"\n    readonly Unknown: \"Unknown\"\n    readonly LoadBalancer: \"LoadBalancer\"\n}>\n\n/** @public */\nexport declare type ServerType = (typeof ServerType)[keyof typeof ServerType]\n\n/** @public */\nexport declare type SetFields<TSchema> = ({\n    readonly [key in KeysOfAType<TSchema, ReadonlyArray<any> | undefined>]?:\n        | OptionalId<Flatten<TSchema[key]>>\n        | AddToSetOperators<Array<OptionalId<Flatten<TSchema[key]>>>>\n} & NotAcceptedFields<TSchema, ReadonlyArray<any> | undefined>) & {\n    readonly [key: string]: AddToSetOperators<any> | any\n}\n\n/** @public */\nexport declare type SetProfilingLevelOptions = CommandOperationOptions\n\n/* Excluded from this release type: SeverityLevel */\n\n/** @public */\nexport declare type Sort =\n    | string\n    | Exclude<\n          SortDirection,\n          {\n              $meta: string\n          }\n      >\n    | string[]\n    | {\n          [key: string]: SortDirection\n      }\n    | Map<string, SortDirection>\n    | [string, SortDirection][]\n    | [string, SortDirection]\n\n/** @public */\nexport declare type SortDirection =\n    | 1\n    | -1\n    | \"asc\"\n    | \"desc\"\n    | \"ascending\"\n    | \"descending\"\n    | {\n          $meta: string\n      }\n\n/* Excluded from this release type: SortDirectionForCmd */\n\n/* Excluded from this release type: SortForCmd */\n\n/* Excluded from this release type: SrvPoller */\n\n/* Excluded from this release type: SrvPollerEvents */\n\n/* Excluded from this release type: SrvPollerOptions */\n\n/* Excluded from this release type: SrvPollingEvent */\n\n/** @public */\nexport declare type Stream = Socket | TLSSocket\n\n/** @public */\nexport declare class StreamDescription {\n    address: string\n    type: string\n    minWireVersion?: number\n    maxWireVersion?: number\n    maxBsonObjectSize: number\n    maxMessageSizeBytes: number\n    maxWriteBatchSize: number\n    compressors: CompressorName[]\n    compressor?: CompressorName\n    logicalSessionTimeoutMinutes?: number\n    loadBalanced: boolean\n    __nodejs_mock_server__?: boolean\n    zlibCompressionLevel?: number\n    constructor(address: string, options?: StreamDescriptionOptions)\n    receiveResponse(response: Document | null): void\n}\n\n/** @public */\nexport declare interface StreamDescriptionOptions {\n    compressors?: CompressorName[]\n    logicalSessionTimeoutMinutes?: number\n    loadBalanced: boolean\n}\n\n/**\n * @public\n * @experimental\n */\nexport declare type StrictFilter<TSchema> =\n    | Partial<TSchema>\n    | ({\n          [Property in Join<NestedPaths<WithId<TSchema>, []>, \".\">]?: Condition<\n              PropertyType<WithId<TSchema>, Property>\n          >\n      } & RootFilterOperators<WithId<TSchema>>)\n\n/**\n * @public\n * @experimental\n */\nexport declare type StrictMatchKeysAndValues<TSchema> = Readonly<\n    {\n        [Property in Join<NestedPaths<TSchema, []>, \".\">]?: PropertyType<\n            TSchema,\n            Property\n        >\n    } & {\n        [Property in `${NestedPathsOfType<TSchema, any[]>}.$${\n            | `[${string}]`\n            | \"\"}`]?: ArrayElement<\n            PropertyType<\n                TSchema,\n                Property extends `${infer Key}.$${string}` ? Key : never\n            >\n        >\n    } & {\n        [Property in `${NestedPathsOfType<TSchema, Record<string, any>[]>}.$${\n            | `[${string}]`\n            | \"\"}.${string}`]?: any\n    } & Document\n>\n\n/**\n * @public\n * @experimental\n */\nexport declare type StrictUpdateFilter<TSchema> = {\n    $currentDate?: OnlyFieldsOfType<\n        TSchema,\n        Date | Timestamp,\n        | true\n        | {\n              $type: \"date\" | \"timestamp\"\n          }\n    >\n    $inc?: OnlyFieldsOfType<TSchema, NumericType | undefined>\n    $min?: StrictMatchKeysAndValues<TSchema>\n    $max?: StrictMatchKeysAndValues<TSchema>\n    $mul?: OnlyFieldsOfType<TSchema, NumericType | undefined>\n    $rename?: Record<string, string>\n    $set?: StrictMatchKeysAndValues<TSchema>\n    $setOnInsert?: StrictMatchKeysAndValues<TSchema>\n    $unset?: OnlyFieldsOfType<TSchema, any, \"\" | true | 1>\n    $addToSet?: SetFields<TSchema>\n    $pop?: OnlyFieldsOfType<TSchema, ReadonlyArray<any>, 1 | -1>\n    $pull?: PullOperator<TSchema>\n    $push?: PushOperator<TSchema>\n    $pullAll?: PullAllOperator<TSchema>\n    $bit?: OnlyFieldsOfType<\n        TSchema,\n        NumericType | undefined,\n        | {\n              and: IntegerType\n          }\n        | {\n              or: IntegerType\n          }\n        | {\n              xor: IntegerType\n          }\n    >\n} & Document\n\n/** @public */\nexport declare type SupportedNodeConnectionOptions =\n    SupportedTLSConnectionOptions &\n        SupportedTLSSocketOptions &\n        SupportedSocketOptions\n\n/** @public */\nexport declare type SupportedSocketOptions = Pick<\n    TcpNetConnectOpts,\n    (typeof LEGAL_TCP_SOCKET_OPTIONS)[number]\n>\n\n/** @public */\nexport declare type SupportedTLSConnectionOptions = Pick<\n    ConnectionOptions_2,\n    Extract<\n        keyof ConnectionOptions_2,\n        (typeof LEGAL_TLS_SOCKET_OPTIONS)[number]\n    >\n>\n\n/** @public */\nexport declare type SupportedTLSSocketOptions = Pick<\n    TLSSocketOptions,\n    Extract<keyof TLSSocketOptions, (typeof LEGAL_TLS_SOCKET_OPTIONS)[number]>\n>\n\n/** @public */\nexport declare type TagSet = {\n    [key: string]: string\n}\n\n/* Excluded from this release type: TimerQueue */\n\n/** @public\n * Configuration options for timeseries collections\n * @see https://www.mongodb.com/docs/manual/core/timeseries-collections/\n */\nexport declare interface TimeSeriesCollectionOptions extends Document {\n    timeField: string\n    metaField?: string\n    granularity?: \"seconds\" | \"minutes\" | \"hours\" | string\n}\n\nexport { Timestamp }\n\n/* Excluded from this release type: Topology */\n\n/**\n * Emitted when topology is closed.\n * @public\n * @category Event\n */\nexport declare class TopologyClosedEvent {\n    /** A unique identifier for the topology */\n    topologyId: number\n    /* Excluded from this release type: __constructor */\n}\n\n/**\n * Representation of a deployment of servers\n * @public\n */\nexport declare class TopologyDescription {\n    type: TopologyType\n    setName: string | null\n    maxSetVersion: number | null\n    maxElectionId: ObjectId | null\n    servers: Map<string, ServerDescription>\n    stale: boolean\n    compatible: boolean\n    compatibilityError?: string\n    logicalSessionTimeoutMinutes: number | null\n    heartbeatFrequencyMS: number\n    localThresholdMS: number\n    commonWireVersion: number\n    /**\n     * Create a TopologyDescription\n     */\n    constructor(\n        topologyType: TopologyType,\n        serverDescriptions?: Map<string, ServerDescription> | null,\n        setName?: string | null,\n        maxSetVersion?: number | null,\n        maxElectionId?: ObjectId | null,\n        commonWireVersion?: number | null,\n        options?: TopologyDescriptionOptions | null,\n    )\n    /* Excluded from this release type: updateFromSrvPollingEvent */\n    /* Excluded from this release type: update */\n    get error(): MongoServerError | null\n    /**\n     * Determines if the topology description has any known servers\n     */\n    get hasKnownServers(): boolean\n    /**\n     * Determines if this topology description has a data-bearing server available.\n     */\n    get hasDataBearingServers(): boolean\n    /* Excluded from this release type: hasServer */\n}\n\n/**\n * Emitted when topology description changes.\n * @public\n * @category Event\n */\nexport declare class TopologyDescriptionChangedEvent {\n    /** A unique identifier for the topology */\n    topologyId: number\n    /** The old topology description */\n    previousDescription: TopologyDescription\n    /** The new topology description */\n    newDescription: TopologyDescription\n    /* Excluded from this release type: __constructor */\n}\n\n/** @public */\nexport declare interface TopologyDescriptionOptions {\n    heartbeatFrequencyMS?: number\n    localThresholdMS?: number\n}\n\n/** @public */\nexport declare type TopologyEvents = {\n    /* Excluded from this release type: connect */\n    serverOpening(event: ServerOpeningEvent): void\n    serverClosed(event: ServerClosedEvent): void\n    serverDescriptionChanged(event: ServerDescriptionChangedEvent): void\n    topologyClosed(event: TopologyClosedEvent): void\n    topologyOpening(event: TopologyOpeningEvent): void\n    topologyDescriptionChanged(event: TopologyDescriptionChangedEvent): void\n    error(error: Error): void\n    /* Excluded from this release type: open */\n    close(): void\n    timeout(): void\n} & Omit<ServerEvents, \"connect\"> &\n    ConnectionPoolEvents &\n    ConnectionEvents &\n    EventEmitterWithState\n\n/**\n * Emitted when topology is initialized.\n * @public\n * @category Event\n */\nexport declare class TopologyOpeningEvent {\n    /** A unique identifier for the topology */\n    topologyId: number\n    /* Excluded from this release type: __constructor */\n}\n\n/* Excluded from this release type: TopologyOptions */\n\n/* Excluded from this release type: TopologyPrivate */\n\n/**\n * An enumeration of topology types we know about\n * @public\n */\nexport declare const TopologyType: Readonly<{\n    readonly Single: \"Single\"\n    readonly ReplicaSetNoPrimary: \"ReplicaSetNoPrimary\"\n    readonly ReplicaSetWithPrimary: \"ReplicaSetWithPrimary\"\n    readonly Sharded: \"Sharded\"\n    readonly Unknown: \"Unknown\"\n    readonly LoadBalanced: \"LoadBalanced\"\n}>\n\n/** @public */\nexport declare type TopologyType =\n    (typeof TopologyType)[keyof typeof TopologyType]\n\n/** @public */\nexport declare interface TopologyVersion {\n    processId: ObjectId\n    counter: Long\n}\n\n/**\n * @public\n * A class maintaining state related to a server transaction. Internal Only\n */\nexport declare class Transaction {\n    /* Excluded from this release type: state */\n    options: TransactionOptions\n    /* Excluded from this release type: _pinnedServer */\n    /* Excluded from this release type: _recoveryToken */\n    /* Excluded from this release type: __constructor */\n    /* Excluded from this release type: server */\n    get recoveryToken(): Document | undefined\n    get isPinned(): boolean\n    /** @returns Whether the transaction has started */\n    get isStarting(): boolean\n    /**\n     * @returns Whether this session is presently in a transaction\n     */\n    get isActive(): boolean\n    get isCommitted(): boolean\n    /* Excluded from this release type: transition */\n    /* Excluded from this release type: pinServer */\n    /* Excluded from this release type: unpinServer */\n}\n\n/**\n * Configuration options for a transaction.\n * @public\n */\nexport declare interface TransactionOptions extends CommandOperationOptions {\n    /** A default read concern for commands in this transaction */\n    readConcern?: ReadConcernLike\n    /** A default writeConcern for commands in this transaction */\n    writeConcern?: WriteConcern\n    /** A default read preference for commands in this transaction */\n    readPreference?: ReadPreferenceLike\n    /** Specifies the maximum amount of time to allow a commit action on a transaction to run in milliseconds */\n    maxCommitTimeMS?: number\n}\n\n/* Excluded from this release type: TxnState */\n\n/**\n * Typescript type safe event emitter\n * @public\n */\nexport declare interface TypedEventEmitter<Events extends EventsDescription>\n    extends EventEmitter {\n    addListener<EventKey extends keyof Events>(\n        event: EventKey,\n        listener: Events[EventKey],\n    ): this\n    addListener(\n        event: CommonEvents,\n        listener: (\n            eventName: string | symbol,\n            listener: GenericListener,\n        ) => void,\n    ): this\n    addListener(event: string | symbol, listener: GenericListener): this\n    on<EventKey extends keyof Events>(\n        event: EventKey,\n        listener: Events[EventKey],\n    ): this\n    on(\n        event: CommonEvents,\n        listener: (\n            eventName: string | symbol,\n            listener: GenericListener,\n        ) => void,\n    ): this\n    on(event: string | symbol, listener: GenericListener): this\n    once<EventKey extends keyof Events>(\n        event: EventKey,\n        listener: Events[EventKey],\n    ): this\n    once(\n        event: CommonEvents,\n        listener: (\n            eventName: string | symbol,\n            listener: GenericListener,\n        ) => void,\n    ): this\n    once(event: string | symbol, listener: GenericListener): this\n    removeListener<EventKey extends keyof Events>(\n        event: EventKey,\n        listener: Events[EventKey],\n    ): this\n    removeListener(\n        event: CommonEvents,\n        listener: (\n            eventName: string | symbol,\n            listener: GenericListener,\n        ) => void,\n    ): this\n    removeListener(event: string | symbol, listener: GenericListener): this\n    off<EventKey extends keyof Events>(\n        event: EventKey,\n        listener: Events[EventKey],\n    ): this\n    off(\n        event: CommonEvents,\n        listener: (\n            eventName: string | symbol,\n            listener: GenericListener,\n        ) => void,\n    ): this\n    off(event: string | symbol, listener: GenericListener): this\n    removeAllListeners<EventKey extends keyof Events>(\n        event?: EventKey | CommonEvents | symbol | string,\n    ): this\n    listeners<EventKey extends keyof Events>(\n        event: EventKey | CommonEvents | symbol | string,\n    ): Events[EventKey][]\n    rawListeners<EventKey extends keyof Events>(\n        event: EventKey | CommonEvents | symbol | string,\n    ): Events[EventKey][]\n    emit<EventKey extends keyof Events>(\n        event: EventKey | symbol,\n        ...args: Parameters<Events[EventKey]>\n    ): boolean\n    listenerCount<EventKey extends keyof Events>(\n        type: EventKey | CommonEvents | symbol | string,\n    ): number\n    prependListener<EventKey extends keyof Events>(\n        event: EventKey,\n        listener: Events[EventKey],\n    ): this\n    prependListener(\n        event: CommonEvents,\n        listener: (\n            eventName: string | symbol,\n            listener: GenericListener,\n        ) => void,\n    ): this\n    prependListener(event: string | symbol, listener: GenericListener): this\n    prependOnceListener<EventKey extends keyof Events>(\n        event: EventKey,\n        listener: Events[EventKey],\n    ): this\n    prependOnceListener(\n        event: CommonEvents,\n        listener: (\n            eventName: string | symbol,\n            listener: GenericListener,\n        ) => void,\n    ): this\n    prependOnceListener(event: string | symbol, listener: GenericListener): this\n    eventNames(): string[]\n    getMaxListeners(): number\n    setMaxListeners(n: number): this\n}\n\n/**\n * Typescript type safe event emitter\n * @public\n */\nexport declare class TypedEventEmitter<\n    Events extends EventsDescription,\n> extends EventEmitter {}\n\n/** @public */\nexport declare class UnorderedBulkOperation extends BulkOperationBase {\n    /* Excluded from this release type: __constructor */\n    handleWriteError(callback: Callback, writeResult: BulkWriteResult): boolean\n    addToOperationsList(\n        batchType: BatchType,\n        document: Document | UpdateStatement | DeleteStatement,\n    ): this\n}\n\n/** @public */\nexport declare interface UpdateDescription<\n    TSchema extends Document = Document,\n> {\n    /**\n     * A document containing key:value pairs of names of the fields that were\n     * changed, and the new value for those fields.\n     */\n    updatedFields?: Partial<TSchema>\n    /**\n     * An array of field names that were removed from the document.\n     */\n    removedFields?: string[]\n    /**\n     * An array of documents which record array truncations performed with pipeline-based updates using one or more of the following stages:\n     * - $addFields\n     * - $set\n     * - $replaceRoot\n     * - $replaceWith\n     */\n    truncatedArrays?: Array<{\n        /** The name of the truncated field. */\n        field: string\n        /** The number of elements in the truncated array. */\n        newSize: number\n    }>\n    /**\n     * A document containing additional information about any ambiguous update paths from the update event.  The document\n     * maps the full ambiguous update path to an array containing the actual resolved components of the path.  For example,\n     * given a document shaped like `{ a: { '0': 0 } }`, and an update of `{ $inc: 'a.0' }`, disambiguated paths would look like\n     * the following:\n     *\n     * ```\n     *   {\n     *     'a.0': ['a', '0']\n     *   }\n     * ```\n     *\n     * This field is only present when there are ambiguous paths that are updated as a part of the update event and `showExpandedEvents`\n     * is enabled for the change stream.\n     * @sinceServerVersion 6.1.0\n     */\n    disambiguatedPaths?: Document\n}\n\n/** @public */\nexport declare type UpdateFilter<TSchema> = {\n    $currentDate?: OnlyFieldsOfType<\n        TSchema,\n        Date | Timestamp,\n        | true\n        | {\n              $type: \"date\" | \"timestamp\"\n          }\n    >\n    $inc?: OnlyFieldsOfType<TSchema, NumericType | undefined>\n    $min?: MatchKeysAndValues<TSchema>\n    $max?: MatchKeysAndValues<TSchema>\n    $mul?: OnlyFieldsOfType<TSchema, NumericType | undefined>\n    $rename?: Record<string, string>\n    $set?: MatchKeysAndValues<TSchema>\n    $setOnInsert?: MatchKeysAndValues<TSchema>\n    $unset?: OnlyFieldsOfType<TSchema, any, \"\" | true | 1>\n    $addToSet?: SetFields<TSchema>\n    $pop?: OnlyFieldsOfType<TSchema, ReadonlyArray<any>, 1 | -1>\n    $pull?: PullOperator<TSchema>\n    $push?: PushOperator<TSchema>\n    $pullAll?: PullAllOperator<TSchema>\n    $bit?: OnlyFieldsOfType<\n        TSchema,\n        NumericType | undefined,\n        | {\n              and: IntegerType\n          }\n        | {\n              or: IntegerType\n          }\n        | {\n              xor: IntegerType\n          }\n    >\n} & Document\n\n/** @public */\nexport declare interface UpdateManyModel<TSchema extends Document = Document> {\n    /** The filter to limit the updated documents. */\n    filter: Filter<TSchema>\n    /** A document or pipeline containing update operators. */\n    update: UpdateFilter<TSchema> | UpdateFilter<TSchema>[]\n    /** A set of filters specifying to which array elements an update should apply. */\n    arrayFilters?: Document[]\n    /** Specifies a collation. */\n    collation?: CollationOptions\n    /** The index to use. If specified, then the query system will only consider plans using the hinted index. */\n    hint?: Hint\n    /** When true, creates a new document if no document matches the query. */\n    upsert?: boolean\n}\n\n/** @public */\nexport declare interface UpdateOneModel<TSchema extends Document = Document> {\n    /** The filter to limit the updated documents. */\n    filter: Filter<TSchema>\n    /** A document or pipeline containing update operators. */\n    update: UpdateFilter<TSchema> | UpdateFilter<TSchema>[]\n    /** A set of filters specifying to which array elements an update should apply. */\n    arrayFilters?: Document[]\n    /** Specifies a collation. */\n    collation?: CollationOptions\n    /** The index to use. If specified, then the query system will only consider plans using the hinted index. */\n    hint?: Hint\n    /** When true, creates a new document if no document matches the query. */\n    upsert?: boolean\n}\n\n/** @public */\nexport declare interface UpdateOptions extends CommandOperationOptions {\n    /** A set of filters specifying to which array elements an update should apply */\n    arrayFilters?: Document[]\n    /** If true, allows the write to opt-out of document level validation */\n    bypassDocumentValidation?: boolean\n    /** Specifies a collation */\n    collation?: CollationOptions\n    /** Specify that the update query should only consider plans using the hinted index */\n    hint?: Hint\n    /** When true, creates a new document if no document matches the query */\n    upsert?: boolean\n    /** Map of parameter names and values that can be accessed using $$var (requires MongoDB 5.0). */\n    let?: Document\n}\n\n/** @public */\nexport declare interface UpdateResult {\n    /** Indicates whether this write result was acknowledged. If not, then all other members of this result will be undefined */\n    acknowledged: boolean\n    /** The number of documents that matched the filter */\n    matchedCount: number\n    /** The number of documents that were modified */\n    modifiedCount: number\n    /** The number of documents that were upserted */\n    upsertedCount: number\n    /** The identifier of the inserted document if an upsert took place */\n    upsertedId: ObjectId\n}\n\n/** @public */\nexport declare interface UpdateStatement {\n    /** The query that matches documents to update. */\n    q: Document\n    /** The modifications to apply. */\n    u: Document | Document[]\n    /**  If true, perform an insert if no documents match the query. */\n    upsert?: boolean\n    /** If true, updates all documents that meet the query criteria. */\n    multi?: boolean\n    /** Specifies the collation to use for the operation. */\n    collation?: CollationOptions\n    /** An array of filter documents that determines which array elements to modify for an update operation on an array field. */\n    arrayFilters?: Document[]\n    /** A document or string that specifies the index to use to support the query predicate. */\n    hint?: Hint\n}\n\n/** @public */\nexport declare interface ValidateCollectionOptions\n    extends CommandOperationOptions {\n    /** Validates a collection in the background, without interrupting read or write traffic (only in MongoDB 4.4+) */\n    background?: boolean\n}\n\n/** @public */\nexport declare type W = number | \"majority\"\n\n/* Excluded from this release type: WaitQueueMember */\n\n/** @public */\nexport declare interface WiredTigerData extends Document {\n    LSM: {\n        \"bloom filter false positives\": number\n        \"bloom filter hits\": number\n        \"bloom filter misses\": number\n        \"bloom filter pages evicted from cache\": number\n        \"bloom filter pages read into cache\": number\n        \"bloom filters in the LSM tree\": number\n        \"chunks in the LSM tree\": number\n        \"highest merge generation in the LSM tree\": number\n        \"queries that could have benefited from a Bloom filter that did not exist\": number\n        \"sleep for LSM checkpoint throttle\": number\n        \"sleep for LSM merge throttle\": number\n        \"total size of bloom filters\": number\n    } & Document\n    \"block-manager\": {\n        \"allocations requiring file extension\": number\n        \"blocks allocated\": number\n        \"blocks freed\": number\n        \"checkpoint size\": number\n        \"file allocation unit size\": number\n        \"file bytes available for reuse\": number\n        \"file magic number\": number\n        \"file major version number\": number\n        \"file size in bytes\": number\n        \"minor version number\": number\n    }\n    btree: {\n        \"btree checkpoint generation\": number\n        \"column-store fixed-size leaf pages\": number\n        \"column-store internal pages\": number\n        \"column-store variable-size RLE encoded values\": number\n        \"column-store variable-size deleted values\": number\n        \"column-store variable-size leaf pages\": number\n        \"fixed-record size\": number\n        \"maximum internal page key size\": number\n        \"maximum internal page size\": number\n        \"maximum leaf page key size\": number\n        \"maximum leaf page size\": number\n        \"maximum leaf page value size\": number\n        \"maximum tree depth\": number\n        \"number of key/value pairs\": number\n        \"overflow pages\": number\n        \"pages rewritten by compaction\": number\n        \"row-store internal pages\": number\n        \"row-store leaf pages\": number\n    } & Document\n    cache: {\n        \"bytes currently in the cache\": number\n        \"bytes read into cache\": number\n        \"bytes written from cache\": number\n        \"checkpoint blocked page eviction\": number\n        \"data source pages selected for eviction unable to be evicted\": number\n        \"hazard pointer blocked page eviction\": number\n        \"in-memory page passed criteria to be split\": number\n        \"in-memory page splits\": number\n        \"internal pages evicted\": number\n        \"internal pages split during eviction\": number\n        \"leaf pages split during eviction\": number\n        \"modified pages evicted\": number\n        \"overflow pages read into cache\": number\n        \"overflow values cached in memory\": number\n        \"page split during eviction deepened the tree\": number\n        \"page written requiring lookaside records\": number\n        \"pages read into cache\": number\n        \"pages read into cache requiring lookaside entries\": number\n        \"pages requested from the cache\": number\n        \"pages written from cache\": number\n        \"pages written requiring in-memory restoration\": number\n        \"tracked dirty bytes in the cache\": number\n        \"unmodified pages evicted\": number\n    } & Document\n    cache_walk: {\n        \"Average difference between current eviction generation when the page was last considered\": number\n        \"Average on-disk page image size seen\": number\n        \"Clean pages currently in cache\": number\n        \"Current eviction generation\": number\n        \"Dirty pages currently in cache\": number\n        \"Entries in the root page\": number\n        \"Internal pages currently in cache\": number\n        \"Leaf pages currently in cache\": number\n        \"Maximum difference between current eviction generation when the page was last considered\": number\n        \"Maximum page size seen\": number\n        \"Minimum on-disk page image size seen\": number\n        \"On-disk page image sizes smaller than a single allocation unit\": number\n        \"Pages created in memory and never written\": number\n        \"Pages currently queued for eviction\": number\n        \"Pages that could not be queued for eviction\": number\n        \"Refs skipped during cache traversal\": number\n        \"Size of the root page\": number\n        \"Total number of pages currently in cache\": number\n    } & Document\n    compression: {\n        \"compressed pages read\": number\n        \"compressed pages written\": number\n        \"page written failed to compress\": number\n        \"page written was too small to compress\": number\n        \"raw compression call failed, additional data available\": number\n        \"raw compression call failed, no additional data available\": number\n        \"raw compression call succeeded\": number\n    } & Document\n    cursor: {\n        \"bulk-loaded cursor-insert calls\": number\n        \"create calls\": number\n        \"cursor-insert key and value bytes inserted\": number\n        \"cursor-remove key bytes removed\": number\n        \"cursor-update value bytes updated\": number\n        \"insert calls\": number\n        \"next calls\": number\n        \"prev calls\": number\n        \"remove calls\": number\n        \"reset calls\": number\n        \"restarted searches\": number\n        \"search calls\": number\n        \"search near calls\": number\n        \"truncate calls\": number\n        \"update calls\": number\n    }\n    reconciliation: {\n        \"dictionary matches\": number\n        \"fast-path pages deleted\": number\n        \"internal page key bytes discarded using suffix compression\": number\n        \"internal page multi-block writes\": number\n        \"internal-page overflow keys\": number\n        \"leaf page key bytes discarded using prefix compression\": number\n        \"leaf page multi-block writes\": number\n        \"leaf-page overflow keys\": number\n        \"maximum blocks required for a page\": number\n        \"overflow values written\": number\n        \"page checksum matches\": number\n        \"page reconciliation calls\": number\n        \"page reconciliation calls for eviction\": number\n        \"pages deleted\": number\n    } & Document\n}\n\n/* Excluded from this release type: WithConnectionCallback */\n\n/** Add an _id field to an object shaped type @public */\nexport declare type WithId<TSchema> = EnhancedOmit<TSchema, \"_id\"> & {\n    _id: InferIdType<TSchema>\n}\n\n/** Remove the _id field from an object shaped type @public */\nexport declare type WithoutId<TSchema> = Omit<TSchema, \"_id\">\n\n/** @public */\nexport declare type WithSessionCallback = (\n    session: ClientSession,\n) => Promise<any>\n\n/** @public */\nexport declare type WithTransactionCallback<T = void> = (\n    session: ClientSession,\n) => Promise<T>\n\n/**\n * A MongoDB WriteConcern, which describes the level of acknowledgement\n * requested from MongoDB for write operations.\n * @public\n *\n * @see https://www.mongodb.com/docs/manual/reference/write-concern/\n */\nexport declare class WriteConcern {\n    /** request acknowledgment that the write operation has propagated to a specified number of mongod instances or to mongod instances with specified tags. */\n    w?: W\n    /** specify a time limit to prevent write operations from blocking indefinitely */\n    wtimeout?: number\n    /** request acknowledgment that the write operation has been written to the on-disk journal */\n    j?: boolean\n    /** equivalent to the j option */\n    fsync?: boolean | 1\n    /**\n     * Constructs a WriteConcern from the write concern properties.\n     * @param w - request acknowledgment that the write operation has propagated to a specified number of mongod instances or to mongod instances with specified tags.\n     * @param wtimeout - specify a time limit to prevent write operations from blocking indefinitely\n     * @param j - request acknowledgment that the write operation has been written to the on-disk journal\n     * @param fsync - equivalent to the j option\n     */\n    constructor(w?: W, wtimeout?: number, j?: boolean, fsync?: boolean | 1)\n    /** Construct a WriteConcern given an options object. */\n    static fromOptions(\n        options?: WriteConcernOptions | WriteConcern | W,\n        inherit?: WriteConcernOptions | WriteConcern,\n    ): WriteConcern | undefined\n}\n\n/**\n * An error representing a failure by the server to apply the requested write concern to the bulk operation.\n * @public\n * @category Error\n */\nexport declare class WriteConcernError {\n    /* Excluded from this release type: [kServerError] */\n    constructor(error: WriteConcernErrorData)\n    /** Write concern error code. */\n    get code(): number | undefined\n    /** Write concern error message. */\n    get errmsg(): string | undefined\n    /** Write concern error info. */\n    get errInfo(): Document | undefined\n    toJSON(): WriteConcernErrorData\n    toString(): string\n}\n\n/** @public */\nexport declare interface WriteConcernErrorData {\n    code: number\n    errmsg: string\n    errInfo?: Document\n}\n\n/** @public */\nexport declare interface WriteConcernOptions {\n    /** Write Concern as an object */\n    writeConcern?: WriteConcern | WriteConcernSettings\n}\n\n/** @public */\nexport declare interface WriteConcernSettings {\n    /** The write concern */\n    w?: W\n    /** The write concern timeout */\n    wtimeoutMS?: number\n    /** The journal write concern */\n    journal?: boolean\n    /** The journal write concern */\n    j?: boolean\n    /** The write concern timeout */\n    wtimeout?: number\n    /** The file sync write concern */\n    fsync?: boolean | 1\n}\n\n/**\n * An error that occurred during a BulkWrite on the server.\n * @public\n * @category Error\n */\nexport declare class WriteError {\n    err: BulkWriteOperationError\n    constructor(err: BulkWriteOperationError)\n    /** WriteError code. */\n    get code(): number\n    /** WriteError original bulk operation index. */\n    get index(): number\n    /** WriteError message. */\n    get errmsg(): string | undefined\n    /** WriteError details. */\n    get errInfo(): Document | undefined\n    /** Returns the underlying operation that caused the error */\n    getOperation(): Document\n    toJSON(): {\n        code: number\n        index: number\n        errmsg?: string\n        op: Document\n    }\n    toString(): string\n}\n\n/* Excluded from this release type: WriteProtocolMessageType */\n\nexport {}\n"], "sourceRoot": "../.."}