{"version": 3, "sources": ["../../src/metadata/types/RelationTypeInFunction.ts"], "names": [], "mappings": "", "file": "RelationTypeInFunction.js", "sourcesContent": ["import { EntityTarget } from \"../../common/EntityTarget\"\n\n/**\n * Function that returns a type of the field. Returned value must be a class used on the relation.\n */\nexport type RelationTypeInFunction =\n    | ((type?: any) => Function)\n    | EntityTarget<any>\n"], "sourceRoot": "../.."}