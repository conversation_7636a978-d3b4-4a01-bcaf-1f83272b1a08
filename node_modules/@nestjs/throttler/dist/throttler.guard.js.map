{"version": 3, "file": "throttler.guard.js", "sourceRoot": "", "sources": ["../src/throttler.guard.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA2E;AAC3E,uCAAyC;AACzC,iCAAgC;AAQhC,+EAAiE;AACjE,+DAO+B;AAC/B,+DAAuF;AACvF,+DAA6E;AAOtE,IAAM,cAAc,GAApB,MAAM,cAAc;IASzB,YAC4B,OAAkD,EAClD,cAAmD,EAC1D,SAAoB;QAFM,YAAO,GAAP,OAAO,CAAwB;QAC/B,mBAAc,GAAd,cAAc,CAAkB;QAC1D,cAAS,GAAT,SAAS,CAAW;QAX/B,iBAAY,GAAG,aAAa,CAAC;QAC7B,iBAAY,GAAG,sCAAgB,CAAC;IAWvC,CAAC;IAEJ,KAAK,CAAC,YAAY;;;QAChB,IAAI,CAAC,UAAU,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC;aACrF,IAAI,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE;YACtB,IAAI,OAAO,KAAK,CAAC,GAAG,KAAK,UAAU,EAAE,CAAC;gBACpC,OAAO,CAAC,CAAC;YACX,CAAC;YACD,IAAI,OAAO,MAAM,CAAC,GAAG,KAAK,UAAU,EAAE,CAAC;gBACrC,OAAO,CAAC,CAAC;YACX,CAAC;YACD,OAAO,KAAK,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC;QAChC,CAAC,CAAC;aACD,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,WAAC,OAAA,iCAAM,GAAG,KAAE,IAAI,EAAE,MAAA,GAAG,CAAC,IAAI,mCAAI,SAAS,IAAG,CAAA,EAAA,CAAC,CAAC;QAC3D,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;YAChC,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QAC1B,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,aAAa,GAAG;gBACnB,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;gBAC3B,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB;gBAC/C,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU;gBACnC,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW;aACtC,CAAC;QACJ,CAAC;QACD,YAAA,IAAI,CAAC,aAAa,EAAC,UAAU,uCAAV,UAAU,GAAK,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAC;QAC7D,YAAA,IAAI,CAAC,aAAa,EAAC,WAAW,uCAAX,WAAW,GAAK,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EAAC;IACjE,CAAC;IAOD,KAAK,CAAC,WAAW,CAAC,OAAyB;QACzC,MAAM,OAAO,GAAG,OAAO,CAAC,UAAU,EAAE,CAAC;QACrC,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;QAEpC,IAAI,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;YACnC,OAAO,IAAI,CAAC;QACd,CAAC;QACD,MAAM,SAAS,GAAc,EAAE,CAAC;QAEhC,KAAK,MAAM,cAAc,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAE7C,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAU,oCAAc,GAAG,cAAc,CAAC,IAAI,EAAE;gBAC3F,OAAO;gBACP,QAAQ;aACT,CAAC,CAAC;YACH,MAAM,MAAM,GAAG,cAAc,CAAC,MAAM,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;YAClE,IAAI,IAAI,KAAI,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAG,OAAO,CAAC,CAAA,EAAE,CAAC;gBAC9B,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACrB,SAAS;YACX,CAAC;YAGD,MAAM,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CACxD,qCAAe,GAAG,cAAc,CAAC,IAAI,EACrC,CAAC,OAAO,EAAE,QAAQ,CAAC,CACpB,CAAC;YACF,MAAM,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CACtD,mCAAa,GAAG,cAAc,CAAC,IAAI,EACnC,CAAC,OAAO,EAAE,QAAQ,CAAC,CACpB,CAAC;YACF,MAAM,yBAAyB,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAChE,8CAAwB,GAAG,cAAc,CAAC,IAAI,EAC9C,CAAC,OAAO,EAAE,QAAQ,CAAC,CACpB,CAAC;YACF,MAAM,sBAAsB,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAC7D,uCAAiB,GAAG,cAAc,CAAC,IAAI,EACvC,CAAC,OAAO,EAAE,QAAQ,CAAC,CACpB,CAAC;YACF,MAAM,2BAA2B,GAC/B,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAC9B,6CAAuB,GAAG,cAAc,CAAC,IAAI,EAC7C,CAAC,OAAO,EAAE,QAAQ,CAAC,CACpB,CAAC;YAGJ,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,iBAAiB,IAAI,cAAc,CAAC,KAAK,CAAC,CAAC;YAC1F,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,eAAe,IAAI,cAAc,CAAC,GAAG,CAAC,CAAC;YACpF,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,YAAY,CAC3C,OAAO,EACP,yBAAyB,IAAI,cAAc,CAAC,aAAa,IAAI,GAAG,CACjE,CAAC;YACF,MAAM,UAAU,GACd,sBAAsB,IAAI,cAAc,CAAC,UAAU,IAAI,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC;YACvF,MAAM,WAAW,GACf,2BAA2B,IAAI,cAAc,CAAC,WAAW,IAAI,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC;YAC9F,SAAS,CAAC,IAAI,CACZ,MAAM,IAAI,CAAC,aAAa,CAAC;gBACvB,OAAO;gBACP,KAAK;gBACL,GAAG;gBACH,SAAS,EAAE,cAAc;gBACzB,aAAa;gBACb,UAAU;gBACV,WAAW;aACZ,CAAC,CACH,CAAC;QACJ,CAAC;QACD,OAAO,SAAS,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;IACzC,CAAC;IAES,KAAK,CAAC,UAAU,CAAC,QAA0B;QACnD,OAAO,KAAK,CAAC;IACf,CAAC;IAQS,KAAK,CAAC,aAAa,CAAC,YAA8B;;QAC1D,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,SAAS,EAAE,aAAa,EAAE,UAAU,EAAE,WAAW,EAAE,GAAG,YAAY,CAAC;QAGhG,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;QACtD,MAAM,gBAAgB,GAAG,MAAA,SAAS,CAAC,gBAAgB,mCAAI,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC;QAE3F,IAAI,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE,CAAC;YACpC,KAAK,MAAM,OAAO,IAAI,gBAAgB,EAAE,CAAC;gBACvC,IAAI,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC;oBAC5C,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC;QACH,CAAC;QACD,MAAM,OAAO,GAAG,MAAM,UAAU,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;QAC/C,MAAM,GAAG,GAAG,WAAW,CAAC,OAAO,EAAE,OAAO,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC;QAC1D,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,SAAS,EAAE,iBAAiB,EAAE,GAC7D,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,aAAa,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC;QAEtF,MAAM,kBAAkB,GAAG,CAAC,IAAY,EAAE,EAAE,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC;QAGpF,IAAI,SAAS,EAAE,CAAC;YACd,GAAG,CAAC,MAAM,CAAC,cAAc,kBAAkB,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,EAAE,iBAAiB,CAAC,CAAC;YAClF,MAAM,IAAI,CAAC,wBAAwB,CAAC,OAAO,EAAE;gBAC3C,KAAK;gBACL,GAAG;gBACH,GAAG;gBACH,OAAO;gBACP,SAAS;gBACT,YAAY;gBACZ,SAAS;gBACT,iBAAiB;aAClB,CAAC,CAAC;QACL,CAAC;QAED,GAAG,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,YAAY,SAAS,kBAAkB,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;QAGrF,GAAG,CAAC,MAAM,CACR,GAAG,IAAI,CAAC,YAAY,aAAa,kBAAkB,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,EACrE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,SAAS,CAAC,CAC/B,CAAC;QACF,GAAG,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,YAAY,SAAS,kBAAkB,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,EAAE,YAAY,CAAC,CAAC;QAE5F,OAAO,IAAI,CAAC;IACd,CAAC;IAES,KAAK,CAAC,UAAU,CAAC,GAAwB;QACjD,OAAO,GAAG,CAAC,EAAE,CAAC;IAChB,CAAC;IAES,kBAAkB,CAAC,OAAyB;QAIpD,MAAM,IAAI,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC;QACpC,OAAO,EAAE,GAAG,EAAE,IAAI,CAAC,UAAU,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;IAC7D,CAAC;IAMS,WAAW,CAAC,OAAyB,EAAE,MAAc,EAAE,IAAY;QAC3E,MAAM,MAAM,GAAG,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC,IAAI,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC;QACjF,OAAO,IAAA,aAAM,EAAC,GAAG,MAAM,IAAI,MAAM,EAAE,CAAC,CAAC;IACvC,CAAC;IASS,KAAK,CAAC,wBAAwB,CACtC,OAAyB,EACzB,oBAA0C;QAE1C,MAAM,IAAI,wCAAkB,CAAC,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,oBAAoB,CAAC,CAAC,CAAC;IAC1F,CAAC;IAES,KAAK,CAAC,eAAe,CAC7B,OAAyB,EACzB,oBAA0C;QAE1C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;YACjC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY;gBAAE,OAAO,IAAI,CAAC,YAAY,CAAC;YAEzD,OAAO,OAAO,IAAI,CAAC,OAAO,CAAC,YAAY,KAAK,UAAU;gBACpD,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE,oBAAoB,CAAC;gBAC1D,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;QAChC,CAAC;QACD,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;IAEO,KAAK,CAAC,YAAY,CACxB,OAAyB,EACzB,eAA8B;QAE9B,OAAO,OAAO,eAAe,KAAK,UAAU,CAAC,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC;IAC5F,CAAC;CACF,CAAA;AArOY,wCAAc;yBAAd,cAAc;IAD1B,IAAA,mBAAU,GAAE;IAWR,WAAA,IAAA,4CAAsB,GAAE,CAAA;IACxB,WAAA,IAAA,4CAAsB,GAAE,CAAA;qDACK,gBAAS;GAZ9B,cAAc,CAqO1B"}