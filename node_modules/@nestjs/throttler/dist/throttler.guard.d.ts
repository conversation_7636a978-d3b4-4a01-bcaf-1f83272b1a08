import { CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ThrottlerGenerateKeyFunction, ThrottlerGetTrackerFunction, ThrottlerModuleOptions, ThrottlerOptions } from './throttler-module-options.interface';
import { ThrottlerStorage } from './throttler-storage.interface';
import { ThrottlerLimitDetail } from './throttler.guard.interface';
export declare class ThrottlerGuard implements CanActivate {
    protected readonly options: ThrottlerModuleOptions;
    protected readonly storageService: ThrottlerStorage;
    protected readonly reflector: Reflector;
    protected headerPrefix: string;
    protected errorMessage: string;
    protected throttlers: Array<ThrottlerOptions>;
    protected commonOptions: Pick<ThrottlerOptions, 'skipIf' | 'ignoreUserAgents' | 'getTracker' | 'generateKey'>;
    constructor(options: ThrottlerModuleOptions, storageService: ThrottlerStorage, reflector: Reflector);
    onModuleInit(): Promise<void>;
    canActivate(context: ExecutionContext): Promise<boolean>;
    protected shouldSkip(_context: ExecutionContext): Promise<boolean>;
    protected handleRequest(context: ExecutionContext, limit: number, ttl: number, throttler: ThrottlerOptions, getTracker: ThrottlerGetTrackerFunction, generateKey: ThrottlerGenerateKeyFunction): Promise<boolean>;
    protected getTracker(req: Record<string, any>): Promise<string>;
    protected getRequestResponse(context: ExecutionContext): {
        req: Record<string, any>;
        res: Record<string, any>;
    };
    protected generateKey(context: ExecutionContext, suffix: string, name: string): string;
    protected throwThrottlingException(context: ExecutionContext, throttlerLimitDetail: ThrottlerLimitDetail): Promise<void>;
    protected getErrorMessage(context: ExecutionContext, throttlerLimitDetail: ThrottlerLimitDetail): Promise<string>;
    private resolveValue;
}
