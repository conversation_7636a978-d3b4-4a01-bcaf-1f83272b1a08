{"version": 3, "file": "throttler.providers.js", "sourceRoot": "", "sources": ["../src/throttler.providers.ts"], "names": [], "mappings": ";;;AAMA,4DAOC;AAXD,+EAAiE;AACjE,+DAA0D;AAC1D,2DAA8D;AAE9D,SAAgB,wBAAwB,CAAC,OAA+B;IACtE,OAAO;QACL;YACE,OAAO,EAAE,uCAAiB;YAC1B,QAAQ,EAAE,OAAO;SAClB;KACF,CAAC;AACJ,CAAC;AAEY,QAAA,wBAAwB,GAAG;IACtC,OAAO,EAAE,8CAAgB;IACzB,UAAU,EAAE,CAAC,OAA+B,EAAE,EAAE;QAC9C,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,OAAO;YAC/C,CAAC,CAAC,OAAO,CAAC,OAAO;YACjB,CAAC,CAAC,IAAI,2CAAuB,EAAE,CAAC;IACpC,CAAC;IACD,MAAM,EAAE,CAAC,uCAAiB,CAAC;CAC5B,CAAC;AAMK,MAAM,eAAe,GAAG,GAAG,EAAE,CAAC,uCAAiB,CAAC;AAA1C,QAAA,eAAe,mBAA2B;AAMhD,MAAM,eAAe,GAAG,GAAG,EAAE,CAAC,8CAAgB,CAAC;AAAzC,QAAA,eAAe,mBAA0B"}