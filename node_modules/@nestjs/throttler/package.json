{"name": "@nestjs/throttler", "version": "6.4.0", "description": "A Rate-Limiting module for NestJS to work on Express, Fastify, Websockets, Socket.IO, and GraphQL, all rolled up into a simple package.", "author": "<PERSON> <<EMAIL>>", "contributors": [], "keywords": ["<PERSON><PERSON><PERSON>", "rate-limit", "throttle", "express", "fastify", "ws", "gql", "nest"], "publishConfig": {"access": "public"}, "private": false, "license": "MIT", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "devDependencies": {"@apollo/server": "4.11.3", "@changesets/cli": "2.27.11", "@commitlint/cli": "19.6.1", "@commitlint/config-angular": "19.7.0", "@nestjs/cli": "11.0.2", "@nestjs/common": "11.0.4", "@nestjs/core": "11.0.4", "@nestjs/graphql": "13.0.2", "@nestjs/platform-express": "11.0.4", "@nestjs/platform-fastify": "11.0.4", "@nestjs/platform-socket.io": "11.0.4", "@nestjs/platform-ws": "11.0.4", "@nestjs/schematics": "11.0.0", "@nestjs/testing": "11.0.4", "@nestjs/websockets": "11.0.4", "@semantic-release/git": "10.0.1", "@types/express": "5.0.0", "@types/express-serve-static-core": "5.0.5", "@types/jest": "29.5.14", "@types/node": "22.10.6", "@types/supertest": "6.0.2", "@typescript-eslint/eslint-plugin": "7.18.0", "@typescript-eslint/parser": "7.18.0", "apollo-server-fastify": "3.13.0", "conventional-changelog-cli": "5.0.0", "cz-conventional-changelog": "3.3.0", "eslint": "8.57.1", "eslint-config-prettier": "10.0.1", "eslint-plugin-import": "2.31.0", "graphql": "16.10.0", "graphql-tools": "9.0.11", "husky": "9.1.7", "jest": "29.7.0", "lint-staged": "15.4.1", "nodemon": "3.1.9", "pactum": "^3.4.1", "pinst": "3.0.0", "prettier": "3.4.2", "reflect-metadata": "0.2.2", "rimraf": "6.0.1", "rxjs": "7.8.1", "socket.io": "4.8.1", "supertest": "7.0.0", "ts-jest": "29.2.5", "ts-loader": "9.5.2", "ts-node": "10.9.2", "tsconfig-paths": "4.2.0", "typescript": "5.7.3", "ws": "8.18.0"}, "peerDependencies": {"@nestjs/common": "^7.0.0 || ^8.0.0 || ^9.0.0 || ^10.0.0 || ^11.0.0", "@nestjs/core": "^7.0.0 || ^8.0.0 || ^9.0.0 || ^10.0.0 || ^11.0.0", "reflect-metadata": "^0.1.13 || ^0.2.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "coverageDirectory": "../coverage", "testEnvironment": "node"}, "repository": {"type": "git", "url": "git+https://github.com/nestjs/throttler.git"}, "bugs": {"url": "https://github.com/nestjs/throttler/issues"}, "homepage": "https://github.com/nestjs/throttler#readme", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "preversion": "yarn run format && yarn run lint && yarn build", "build": "nest build", "commit": "git-cz", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start:dev": "nodemon --watch '{src,test/app}/**/*.ts' --ignore '**/*.spec.ts' --exec 'ts-node' test/app/main.ts", "lint": "eslint \"{src,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json --detect<PERSON><PERSON><PERSON>andles", "test:e2e:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --config test/jest-e2e.json --runInBand", "test:e2e:dev": "yarn test:e2e --watchAll", "release": "changeset publish"}}