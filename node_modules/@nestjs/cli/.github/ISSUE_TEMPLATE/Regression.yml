name: "\U0001F4A5 Regression"
description: "Report an unexpected behavior while upgrading your Nest application!"
labels: ["needs triage"]
body:
  - type: markdown
    attributes:
      value: |
        ## :warning: We use GitHub Issues to track bug reports, feature requests and regressions
 
        If you are not sure that your issue is a bug, you could:

        - use our [Discord community](https://discord.gg/NestJS)
        - use [StackOverflow using the tag `nestjs`](https://stackoverflow.com/questions/tagged/nestjs)
        - If it's just a quick question you can ping [our Twitter](https://twitter.com/nestframework)

        **NOTE:** You don't need to answer questions that you know that aren't relevant.

        ---

  - type: checkboxes
    attributes:
      label: "Did you read the migration guide?"
      description: "Check out the [migration guide here](https://docs.nestjs.com/migration-guide)!"
      options:
      - label: "I have read the whole migration guide"
        required: false

  - type: checkboxes
    attributes:
      label: "Is there an existing issue that is already proposing this?"
      description: "Please search [here](./?q=is%3Aissue) to see if an issue already exists for the feature you are requesting"
      options:
      - label: "I have searched the existing issues"
        required: true

  - type: input
    attributes:
      label: "Potential Commit/PR that introduced the regression"
      description: "If you have time to investigate, what PR/date/version introduced this issue"
      placeholder: "PR #123 or commit 5b3c4a4"

  - type: input
    attributes:
      label: "Versions"
      description: "From which version of `@nestjs/cli` to which version you are upgrading"
      placeholder: "8.1.0 -> 8.1.3"

  - type: textarea
    validations:
      required: true
    attributes:
      label: "Describe the regression"
      description: "A clear and concise description of what the regression is"

  - type: textarea
    attributes:
      label: "Minimum reproduction code"
      description: |
        Please share a git repo, a gist, or step-by-step instructions. [Wtf is a minimum reproduction?](https://jmcdo29.github.io/wtf-is-a-minimum-reproduction)
        **Tip:** If you leave a minimum repository, we will understand your issue faster!
      value: |
        ```ts

        ```

  - type: textarea
    validations:
      required: true
    attributes:
      label: "Expected behavior"
      description: "A clear and concise description of what you expected to happend (or code)"

  - type: textarea
    attributes:
      label: "Other"
      description: |
        Anything else relevant? eg: Logs, OS version, IDE, package manager, etc.
        **Tip:** You can attach images, recordings or log files by clicking this area to highlight it and then dragging files in
