{"name": "@nestjs/serve-static", "version": "5.0.3", "description": "Nest - modern, fast, powerful node.js web framework (@serve-static)", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "scripts": {"build": "npm run build:lib", "build:lib": "tsc -p tsconfig.json", "format": "prettier --write \"**/*.ts\"", "lint": "eslint 'lib/**/*.ts' --fix", "prepublish:npm": "npm run build", "publish:npm": "npm publish --access public", "prepublish:next": "npm run build", "publish:next": "npm publish --access public --tag next", "prerelease": "npm run build", "release": "release-it", "test:e2e": "jest --config ./tests/jest-e2e.json --runInBand", "prepare": "husky"}, "peerDependencies": {"@fastify/static": "^8.0.4", "@nestjs/common": "^11.0.2", "@nestjs/core": "^11.0.2", "express": "^5.0.1", "fastify": "^5.2.1"}, "peerDependenciesMeta": {"express": {"optional": true}, "fastify": {"optional": true}, "@fastify/static": {"optional": true}}, "devDependencies": {"@commitlint/cli": "19.7.1", "@commitlint/config-angular": "19.7.1", "@eslint/eslintrc": "3.2.0", "@eslint/js": "9.20.0", "@fastify/static": "8.1.1", "@nestjs/common": "11.0.10", "@nestjs/core": "11.0.10", "@nestjs/platform-express": "11.0.10", "@nestjs/platform-fastify": "11.0.10", "@nestjs/testing": "11.0.10", "@types/jest": "29.5.14", "@types/node": "22.13.4", "@types/supertest": "6.0.2", "eslint": "9.20.1", "eslint-config-prettier": "10.0.1", "eslint-plugin-prettier": "5.2.3", "express": "4.21.2", "fastify": "5.2.1", "globals": "15.15.0", "husky": "9.1.7", "jest": "29.7.0", "lint-staged": "15.4.3", "prettier": "3.5.1", "reflect-metadata": "0.2.2", "release-it": "18.1.2", "rxjs": "7.8.1", "supertest": "7.0.0", "ts-jest": "29.2.5", "typescript": "5.7.3", "typescript-eslint": "8.24.1"}, "lint-staged": {"*.ts": ["prettier --write"]}, "repository": {"type": "git", "url": "https://github.com/nestjs/serve-static"}, "dependencies": {"path-to-regexp": "8.2.0"}}