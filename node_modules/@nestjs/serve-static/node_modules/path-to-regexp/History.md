0.2.5 / 2014-08-07
==================

  * Allow keys parameter to be omitted

0.2.4 / 2014-08-02
==================

  * Code coverage badge
  * Updated readme
  * Attach keys to the generated regexp

0.2.3 / 2014-07-09
==================

  * Add MIT license

0.2.2 / 2014-07-06
==================

  * A passed in trailing slash in non-strict mode will become optional
  * In non-end mode, the optional trailing slash will only match at the end

0.2.1 / 2014-06-11
==================

  * Fixed a major capturing group regexp regression

0.2.0 / 2014-06-09
==================

  * Improved support for arrays
  * Improved support for regexps
  * Better support for non-ending strict mode matches with a trailing slash
  * Travis CI support
  * Block using regexp special characters in the path
  * Removed support for the asterisk to match all
  * New support for parameter suffixes - `*`, `+` and `?`
  * Updated readme
  * Provide delimiter information with keys array

0.1.2 / 2014-03-10
==================

  * Move testing dependencies to `devDependencies`

0.1.1 / 2014-03-10
==================

  * Match entire substring with `options.end`
  * Properly handle ending and non-ending matches

0.1.0 / 2014-03-06
==================

  * Add `options.end`

0.0.2 / 2013-02-10
==================

  * Update to match current express
  * Add .license property to component.json
