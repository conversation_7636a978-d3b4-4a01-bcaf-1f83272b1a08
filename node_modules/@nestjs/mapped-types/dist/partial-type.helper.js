"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PartialType = PartialType;
const type_helpers_utils_1 = require("./type-helpers.utils");
function PartialType(classRef, options = {}) {
    class PartialClassType {
        constructor() {
            (0, type_helpers_utils_1.inheritPropertyInitializers)(this, classRef);
        }
    }
    const propertyKeys = (0, type_helpers_utils_1.inheritValidationMetadata)(classRef, PartialClassType);
    (0, type_helpers_utils_1.inheritTransformationMetadata)(classRef, PartialClassType);
    if (propertyKeys) {
        propertyKeys.forEach((key) => {
            options.skipNullProperties === false
                ? (0, type_helpers_utils_1.applyValidateIfDefinedDecorator)(PartialClassType, key)
                : (0, type_helpers_utils_1.applyIsOptionalDecorator)(PartialClassType, key);
        });
    }
    Object.defineProperty(PartialClassType, 'name', {
        value: `Partial${classRef.name}`,
    });
    return PartialClassType;
}
