{"name": "@nestjs/mapped-types", "version": "2.1.0", "description": "Nest - modern, fast, powerful node.js web framework (@mapped-types)", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "url": "https://github.com/nestjs/mapped-types#readme", "scripts": {"build": "rimraf -rf dist && tsc -p tsconfig.json", "lint": "eslint 'lib/**/*.ts' --fix", "format": "prettier \"{lib,tests}/**/*.ts\" --write", "prepublish:npm": "npm run build", "publish:npm": "npm publish --access public", "prepublish:next": "npm run build", "publish:next": "npm publish --access public --tag next", "test:e2e": "jest --config ./tests/jest-e2e.json --runInBand", "test:e2e:dev": "jest --config ./tests/jest-e2e.json --runInBand --watch", "prerelease": "npm run build", "release": "release-it", "prepare": "husky"}, "devDependencies": {"@commitlint/cli": "19.6.1", "@commitlint/config-angular": "19.7.0", "@eslint/eslintrc": "3.2.0", "@eslint/js": "9.18.0", "@nestjs/common": "11.0.1", "@types/jest": "29.5.14", "@types/node": "22.10.7", "class-transformer": "0.5.1", "class-validator": "0.14.1", "eslint": "9.18.0", "eslint-config-prettier": "10.0.1", "eslint-plugin-prettier": "5.2.2", "globals": "15.14.0", "husky": "9.1.7", "jest": "29.7.0", "lint-staged": "15.4.1", "prettier": "3.4.2", "reflect-metadata": "0.2.2", "release-it": "18.1.1", "rimraf": "6.0.1", "ts-jest": "29.2.5", "typescript": "5.7.3", "typescript-eslint": "8.20.0"}, "peerDependencies": {"@nestjs/common": "^10.0.0 || ^11.0.0", "class-transformer": "^0.4.0 || ^0.5.0", "class-validator": "^0.13.0 || ^0.14.0", "reflect-metadata": "^0.1.12 || ^0.2.0"}, "peerDependenciesMeta": {"class-validator": {"optional": true}, "class-transformer": {"optional": true}}, "lint-staged": {"**/*.{ts,json}": []}, "repository": {"type": "git", "url": "https://github.com/nestjs/mapped-types"}}