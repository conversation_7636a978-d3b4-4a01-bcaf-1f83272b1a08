{"name": "@nestjs/passport", "version": "11.0.5", "description": "Nest - modern, fast, powerful node.js web framework (@passport)", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "scripts": {"build": "rimraf dist && tsc -p tsconfig.build.json", "format": "prettier --write \"lib/**/*.ts\"", "lint": "eslint 'lib/**/*.ts' --fix", "precommit": "lint-staged", "prepublish:npm": "npm run build", "publish:npm": "npm publish --access public", "prerelease": "npm run build", "release": "release-it", "test": "jest", "prepare": "husky"}, "files": ["dist", "index.js", "index.d.ts"], "peerDependencies": {"@nestjs/common": "^10.0.0 || ^11.0.0", "passport": "^0.5.0 || ^0.6.0 || ^0.7.0"}, "devDependencies": {"@commitlint/cli": "19.6.1", "@commitlint/config-angular": "19.7.0", "@eslint/eslintrc": "3.2.0", "@eslint/js": "9.18.0", "@nestjs/common": "11.0.5", "@nestjs/core": "11.0.5", "@nestjs/jwt": "11.0.0", "@nestjs/platform-express": "11.0.5", "@nestjs/testing": "11.0.5", "@types/jest": "29.5.14", "@types/node": "22.10.9", "@types/passport": "1.0.17", "@types/passport-jwt": "4.0.1", "@types/passport-local": "1.0.38", "eslint": "9.18.0", "eslint-config-prettier": "10.0.1", "eslint-plugin-prettier": "5.2.3", "globals": "15.14.0", "husky": "9.1.7", "jest": "29.7.0", "lint-staged": "15.4.2", "pactum": "3.7.6", "passport": "0.7.0", "passport-cookie": "^1.0.9", "passport-jwt": "4.0.1", "passport-local": "1.0.0", "prettier": "3.4.2", "reflect-metadata": "0.2.2", "release-it": "18.1.1", "rimraf": "6.0.1", "rxjs": "7.8.1", "ts-jest": "29.2.5", "typescript": "5.7.3", "typescript-eslint": "8.21.0"}, "lint-staged": {"**/*.{ts,json}": []}, "repository": {"type": "git", "url": "https://github.com/nestjs/passport"}}