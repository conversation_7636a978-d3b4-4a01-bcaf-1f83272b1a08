{"name": "@nestjs/platform-express", "version": "10.4.19", "description": "Nest - modern, fast, powerful node.js web framework (@platform-express)", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "homepage": "https://nestjs.com", "funding": {"type": "opencollective", "url": "https://opencollective.com/nest"}, "repository": {"type": "git", "url": "https://github.com/nestjs/nest.git", "directory": "packages/platform-express"}, "publishConfig": {"access": "public"}, "dependencies": {"body-parser": "1.20.3", "cors": "2.8.5", "express": "4.21.2", "multer": "2.0.1", "tslib": "2.8.1"}, "devDependencies": {"@nestjs/common": "10.4.19", "@nestjs/core": "10.4.19"}, "peerDependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0"}}