{"name": "@nestjs/typeorm", "version": "11.0.0", "description": "Nest - modern, fast, powerful node.js web framework (@typeorm)", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "url": "https://github.com/nestjs/typeorm#readme", "scripts": {"build": "rm -rf dist && tsc -p tsconfig.json", "format": "prettier --write \"**/*.ts\"", "lint": "eslint 'lib/**/*.ts' --fix", "prepublish:npm": "npm run build", "publish:npm": "npm publish --access public", "prepublish:next": "npm run build", "publish:next": "npm publish --access public --tag next", "test:e2e": "jest --config ./tests/jest-e2e.json --runInBand", "test:e2e:dev": "jest --config ./tests/jest-e2e.json --runInBand --watch", "prerelease": "npm run build", "release": "release-it", "prepare": "husky"}, "devDependencies": {"@commitlint/cli": "19.6.1", "@commitlint/config-angular": "19.7.0", "@eslint/eslintrc": "3.2.0", "@eslint/js": "9.18.0", "@nestjs/common": "11.0.1", "@nestjs/core": "11.0.1", "@nestjs/platform-express": "11.0.1", "@nestjs/testing": "11.0.1", "@types/jest": "29.5.14", "@types/node": "22.10.7", "@types/supertest": "6.0.2", "eslint": "9.18.0", "eslint-config-prettier": "10.0.1", "eslint-plugin-prettier": "5.2.2", "globals": "15.14.0", "husky": "9.1.7", "jest": "29.7.0", "lint-staged": "15.4.1", "mysql": "2.18.1", "pg": "8.13.1", "prettier": "3.4.2", "reflect-metadata": "0.2.2", "release-it": "18.1.1", "rxjs": "7.8.1", "supertest": "7.0.0", "ts-jest": "29.2.5", "typeorm": "0.3.20", "typescript": "5.7.3", "typescript-eslint": "8.20.0"}, "peerDependencies": {"@nestjs/common": "^10.0.0 || ^11.0.0", "@nestjs/core": "^10.0.0 || ^11.0.0", "reflect-metadata": "^0.1.13 || ^0.2.0", "rxjs": "^7.2.0", "typeorm": "^0.3.0"}, "lint-staged": {"**/*.{ts,json}": []}, "repository": {"type": "git", "url": "https://github.com/nestjs/typeorm"}}