{"name": "work-finder-server", "version": "1.0.0", "description": "A streamlined work finder server application built with NestJS", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\"", "start": "nest start", "dev": "nest start --watch", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs}/**/*.ts\" --fix"}, "dependencies": {"@nestjs/common": "10.4.13", "@nestjs/config": "^3.3.0", "@nestjs/core": "10.4.13", "@nestjs/jwt": "^10.2.0", "@nestjs/mapped-types": "*", "@nestjs/mongoose": "^10.1.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "10.4.13", "@nestjs/swagger": "^8.1.0", "@nestjs/terminus": "^10.2.3", "@nestjs/throttler": "^4.1.0", "api-query-params": "^5.4.0", "bcryptjs": "^2.4.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cookie-parser": "^1.4.7", "dayjs": "^1.11.13", "helmet": "^8.0.0", "mongoose": "^7.8.3", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "reflect-metadata": "^0.1.13", "rxjs": "7.8.1", "soft-delete-plugin-mongoose": "^1.0.17"}, "devDependencies": {"@nestjs/cli": "10.4.8", "@nestjs/schematics": "10.2.3", "@types/cookie-parser": "^1.4.8", "@types/express": "5.0.0", "@types/multer": "^1.4.12", "@types/node": "22.10.1", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@typescript-eslint/eslint-plugin": "8.17.0", "@typescript-eslint/parser": "8.17.0", "eslint": "9.16.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.2.1", "prettier": "3.4.2", "source-map-support": "0.5.21", "ts-loader": "9.5.1", "ts-node": "10.9.2", "tsconfig-paths": "4.2.0", "typescript": "5.7.2"}}