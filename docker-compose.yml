version: '3.8'

services:
  app:
    build: .
    ports:
      - '3000:3000'
    environment:
      - NODE_ENV=production
      - PORT=3000
      - MONGO_URI=mongodb://mongo:27017/work-finder
      - JWT_ACCESS_KEY=your-super-secret-jwt-access-key-change-this-in-production
      - JWT_ACCESS_EXPIRE=1d
      - JWT_REFRESH_KEY=your-super-secret-jwt-refresh-key-change-this-in-production
      - JWT_REFRESH_EXPIRE=7d
    depends_on:
      - mongo
    volumes:
      - ./public:/app/public

  mongo:
    image: mongo:7.0
    ports:
      - '27017:27017'
    volumes:
      - mongo_data:/data/db
    environment:
      - MONGO_INITDB_DATABASE=work-finder

volumes:
  mongo_data:
