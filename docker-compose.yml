version: '3.8'

services:
  app:
    build: .
    ports:
      - '3000:3000'
    environment:
      - NODE_ENV=production
      - PORT=3000
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USERNAME=postgres
      - DB_PASSWORD=password
      - DB_DATABASE=work_finder
      - JWT_ACCESS_KEY=your-super-secret-jwt-access-key-change-this-in-production
      - JWT_ACCESS_EXPIRE=1d
      - JWT_REFRESH_KEY=your-super-secret-jwt-refresh-key-change-this-in-production
      - JWT_REFRESH_EXPIRE=7d
      - UPLOAD_PATH=./uploads
      - MAX_FILE_SIZE=5242880
    depends_on:
      - postgres
    volumes:
      - ./uploads:/app/uploads

  postgres:
    image: postgres:15
    ports:
      - '5432:5432'
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=work_finder
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
