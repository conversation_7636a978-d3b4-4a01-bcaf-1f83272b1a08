# Work Finder API Documentation

## Overview

The Work Finder API is a comprehensive job finder system built with NestJS, PostgreSQL, and TypeORM. It provides endpoints for managing users, companies, job posts, applications, resumes, interviews, and more.

## Base URL

```
http://localhost:3000/api/v1
```

## Authentication

The API uses JWT (JSON Web Tokens) for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## API Endpoints

### Authentication (`/auth`)

- `POST /auth/register` - User registration
- `POST /auth/login` - User login
- `GET /auth/profile` - Get user profile
- `POST /auth/refresh` - Refresh access token
- `POST /auth/logout` - User logout

### Users (`/users`)

- `GET /users` - Get all users
- `GET /users/me` - Get current user profile
- `GET /users/:id` - Get user by ID
- `PATCH /users/me` - Update current user profile
- `PATCH /users/:id` - Update user by ID
- `DELETE /users/:id` - Delete user

### Companies (`/companies`)

- `GET /companies` - Get all companies (with search)
- `POST /companies` - Create company (HR/Admin only)
- `GET /companies/:id` - Get company by ID
- `GET /companies/:id/stats` - Get company statistics
- `PATCH /companies/:id` - Update company (HR/Admin only)
- `DELETE /companies/:id` - Delete company (Admin only)
- `POST /companies/:id/follow` - Follow a company
- `DELETE /companies/:id/unfollow` - Unfollow a company
- `GET /companies/my-followed` - Get companies I follow

### Jobs (`/jobs`)

- `GET /jobs` - Get all job posts (with filters)
- `POST /jobs` - Create job post
- `GET /jobs/:id` - Get job post by ID
- `GET /jobs/:id/stats` - Get job statistics
- `GET /jobs/company/:companyId` - Get jobs by company
- `PATCH /jobs/:id` - Update job post
- `DELETE /jobs/:id` - Delete job post

### Resumes (`/resumes`)

- `GET /resumes` - Get all resumes
- `POST /resumes` - Create resume record
- `POST /resumes/upload` - Upload resume file
- `GET /resumes/my-resumes` - Get current user resumes
- `GET /resumes/user/:userId` - Get resumes by user
- `GET /resumes/stats` - Get resume statistics
- `GET /resumes/:id` - Get resume by ID
- `PATCH /resumes/:id` - Update resume
- `DELETE /resumes/:id` - Delete resume

### Applications (`/applications`)

- `GET /applications` - Get all applications
- `POST /applications` - Create application
- `GET /applications/my-applications` - Get current user applications
- `GET /applications/job/:jobId` - Get applications by job
- `GET /applications/:id` - Get application by ID
- `PATCH /applications/:id` - Update application
- `DELETE /applications/:id` - Delete application

### Interviews (`/interviews`)

- `GET /interviews` - Get all interviews
- `POST /interviews` - Create interview
- `GET /interviews/application/:applicationId` - Get interviews by application
- `GET /interviews/:id` - Get interview by ID
- `PATCH /interviews/:id` - Update interview
- `DELETE /interviews/:id` - Delete interview

### Saved Jobs (`/saved-jobs`)

- `POST /saved-jobs` - Save a job
- `POST /saved-jobs/:jobId` - Save job by ID
- `GET /saved-jobs/my-saved-jobs` - Get current user saved jobs
- `GET /saved-jobs/user/:userId` - Get saved jobs by user
- `GET /saved-jobs/:id` - Get saved job by ID
- `DELETE /saved-jobs/:id` - Remove saved job
- `DELETE /saved-jobs/job/:jobId` - Unsave job by ID

### Notifications (`/notifications`)

- `GET /notifications` - Get my notifications
- `GET /notifications/unread` - Get my unread notifications
- `GET /notifications/stats` - Get my notification statistics
- `GET /notifications/all` - Get all notifications (Admin only)
- `PATCH /notifications/:id/read` - Mark notification as read
- `PATCH /notifications/mark-all-read` - Mark all my notifications as read

## Response Format

All API responses follow this format:

```json
{
  "statusCode": 200,
  "message": "Success message",
  "data": {
    // Response data
  }
}
```

## Error Handling

Error responses include:

```json
{
  "statusCode": 400,
  "message": "Error message",
  "error": "Bad Request"
}
```

## File Upload

Resume files can be uploaded to `/resumes/upload` endpoint:
- Supported formats: PDF, DOC, DOCX
- Maximum file size: 5MB
- Files are stored in `/uploads/resumes/` directory

## Database Schema

The API uses the following main entities:
- **permissions**: User permission levels
- **users**: User accounts with permission references
- **companies**: Company information and profiles
- **resumes**: User resume files and metadata
- **job_posts**: Job postings by companies
- **applications**: Job applications linking users to jobs
- **interviews**: Interview scheduling for applications
- **saved_jobs**: Users saving jobs for later
- **followed_companies**: Users following companies
- **notifications**: System notifications

## Rate Limiting

API endpoints are rate-limited to 10 requests per minute per IP address.

## Swagger Documentation

Interactive API documentation is available at:
```
http://localhost:3000/swagger
```
