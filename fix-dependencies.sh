#!/bin/bash

echo "🔧 Fixing npm dependencies automatically..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Step 1: Clear npm cache
print_status "Clearing npm cache..."
npm cache clean --force
if [ $? -eq 0 ]; then
    print_success "npm cache cleared"
else
    print_warning "Failed to clear npm cache, continuing..."
fi

# Step 2: Remove node_modules and package-lock.json
print_status "Removing node_modules and package-lock.json..."
rm -rf node_modules
rm -f package-lock.json
print_success "Cleaned up old dependencies"

# Step 3: Update npm to latest version
print_status "Updating npm to latest version..."
npm install -g npm@latest
print_success "npm updated"

# Step 4: Install dependencies
print_status "Installing dependencies..."
npm install
if [ $? -eq 0 ]; then
    print_success "Dependencies installed successfully!"
else
    print_error "Failed to install dependencies"
    
    # Try alternative solutions
    print_status "Trying alternative solutions..."
    
    # Try with --legacy-peer-deps
    print_status "Trying with --legacy-peer-deps..."
    npm install --legacy-peer-deps
    
    if [ $? -eq 0 ]; then
        print_success "Dependencies installed with --legacy-peer-deps!"
    else
        # Try with --force
        print_status "Trying with --force..."
        npm install --force
        
        if [ $? -eq 0 ]; then
            print_success "Dependencies installed with --force!"
        else
            print_error "All installation methods failed. Please check manually."
            exit 1
        fi
    fi
fi

# Step 5: Verify installation
print_status "Verifying installation..."
if [ -d "node_modules" ] && [ -f "package-lock.json" ]; then
    print_success "Installation verified successfully!"
    
    # Check if we can build the project
    print_status "Testing build..."
    npm run build
    if [ $? -eq 0 ]; then
        print_success "Build test passed!"
        echo ""
        echo "🎉 All fixed! You can now run:"
        echo "   npm run start:dev"
    else
        print_warning "Build test failed, but dependencies are installed"
    fi
else
    print_error "Installation verification failed"
    exit 1
fi

echo ""
print_success "✅ Dependency fix completed!"
